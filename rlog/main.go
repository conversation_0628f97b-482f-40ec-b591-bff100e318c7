package main

import (
	"bytes"
	"fmt"
	"github.com/beego/beego/v2/core/logs"
	"io"
	"net"
	"strings"
	"sync"
)

var bufPool = &sync.Pool{
	New: func() interface{} {
		return make([]byte, 1024)
	},
}

var log = logs.GetBeeLogger()

func handleConnection(conn net.Conn) {
	defer conn.Close()
	buf := bytes.NewBuffer(nil)
	tmp := bufPool.Get().([]byte)
	for {
		n, err := conn.Read(tmp)
		if err != nil {
			if err == io.EOF {
				break
			}
			log.Error("Error reading:", err)
			break
		}
		buf.Write(tmp[:n])
		for {
			line, err := buf.ReadString('\n')
			if err == io.EOF {
				break
			}
			if err != nil {
				log.Error("Error reading line:", err)
				break
			}
			addr := conn.RemoteAddr().String()
			wt := strings.LastIndex(addr, ":")
			if wt > -1 {
				addr = addr[:wt]
			}
			log.Info(fmt.Sprintf("[%s] %s", addr, line))
		}
		if buf.Len() > 1024 {
			buf.Reset()
		}
	}
	bufPool.Put(tmp)
}

/*
*
server.json配置  投递日志

	"Log": {
	  "conn": {
	    "net": "tcp",
	    "reconnect": true,
	    "addr": "127.0.0.1:6002"
	  }
	}

*
*/

func main() {
	logs.Async()
	logs.RegisterFormatter("global", &CFormat{})
	logs.SetGlobalFormatter("global")
	// 单个文件最大500m
	// 最多存30天
	err := logs.SetLogger(logs.AdapterMultiFile,
		`{"filename":"world.log", "maxsize":536870912, "maxdays": 30}`)
	if err != nil {
		panic(err)
	}

	listener, err := net.Listen("tcp", ":6002")
	if err != nil {
		panic(err)
	}

	for {
		conn, err := listener.Accept()
		if err != nil {
			log.Error("Error accepting connection:", err)
			continue
		}

		go handleConnection(conn)
	}
}

type CFormat struct {
}

func (C *CFormat) Format(lm *logs.LogMsg) string {
	return fmt.Sprintf("%s", lm.Msg)
}
