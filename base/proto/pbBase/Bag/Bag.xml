<?xml version="1.0" encoding="UTF-8" ?>
<messages package="world/common/pbBase/Bag">
    <enum name="ItemUseType" explain="使用物品时的类型定义" allowAlias="false">
        <type name="None" number="0" explain="未知"/>
        <type name="Equip" number="1" explain="装备"/>
        <type name="UnEquip" number="2" explain=""/>
        <type name="Use" number="3" explain=""/>
        <type name="Lose" number="4" explain=""/>
        <type name="CheckUp" number="5" explain=""/>
        <type name="Enchase" number="6" explain=""/>
        <type name="Bind" number="7" explain=""/>
        <type name="UseByOneKey" number="8" explain=""/>
    </enum>
    <enum name="ItemUseSubType" explain="使用物品时的类型定义" allowAlias="false">
        <type name="NoWait" number="0" explain="无需等待结果"/>
        <type name="UsePetEgg" number="1" explain="使用宠物蛋"/>
        <type name="EquipPet" number="2" explain="装备宠物"/>
        <type name="UnEquipPet" number="3" explain="卸下宠物"/>
        <type name="UseChest" number="4" explain="开启奖励盒子"/>
        <type name="CheckUpSub" number="5" explain=""/>
        <type name="EnchaseSub" number="6" explain="附魔"/>
        <type name="BindSub" number="7" explain="绑定"/>
        <type name="CommandBook" number="8" explain="使用指令书"/>
        <type name="EquipSub" number="9" explain="装备"/>
        <type name="AddStoreNum" number="10" explain="增加仓库格子数"/>
        <type name="AddExp" number="11" explain="增加经验"/>
        <type name="AddPetExp" number="12" explain="增加宠物经验"/>
        <type name="PetRest" number="13" explain="宠物洗髓"/>
        <type name="PetAge" number="14" explain="恢复宠物寿命"/>
        <type name="Repair" number="15" explain="修理"/>
        <type name="GetTitle" number="16" explain="获取称号"/>
        <type name="UnEquipSub" number="17" explain="卸下装备"/>
        <type name="ChangeJob" number="18" explain="更换职业"/>
        <type name="PetItemAddSkill" number="19" explain="宠物潜能石头"/>
        <type name="Wait" number="20" explain=""/>
        <type name="AlertSex" number="21" explain=""/>
        <type name="AddCp" number="22" explain="增加属性点"/>
        <type name="AddSp" number="23" explain="增加技能点"/>
        <type name="AddProsperityDegree" number="24" explain=""/>
        <type name="SkillSlotPlayer" number="25" explain="人物技能槽"/>
        <type name="SkillSlotPet" number="26" explain="宠物技能槽"/>
        <type name="PetAddLife" number="27" explain="恢复宠物寿命"/>
    </enum>
</messages>