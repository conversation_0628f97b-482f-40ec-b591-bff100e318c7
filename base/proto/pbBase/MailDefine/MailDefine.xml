<?xml version="1.0" encoding="UTF-8" ?>
<messages package="world/common/pbBase/MailDefine">
    <enum name="MAIL_TYPE" explain="邮件类型定义" allowAlias="false">
        <type name="TOTAL" number="0" explain="总计 服务端用"/>
        <type name="SEND_PLAYER" number="-5" explain="客户端使用"/>
        <type name="SEND_SERVICE" number="-4" explain="客户端使用"/>
        <type name="SEND" number="1" explain="已发交易邮件"/>
        <type name="SYSTEM" number="2" explain="客服系统邮件"/>
        <type name="MONEY" number="4" explain="充值邮件"/>
        <type name="TASK" number="8" explain="任务邮件"/>
        <type name="SERVICE" number="16" explain="玩家联系客服的邮件"/>
        <type name="PLAYER" number="32" explain="玩家邮件"/>
        <type name="BACK" number="64" explain="退回邮件"/>
        <type name="RECEIPT" number="128" explain="回执邮件"/>
        <type name="CHARGE_FAIL" number="256" explain="充值失败邮件"/>
        <type name="MERGE_SYSTEM_SERVICE" number="18" explain="系统+客服"/>
        <type name="MERGE_BACK_RECEIPT" number="192" explain="退回+回执"/>
    </enum>
    <enum name="MAIL_STATUS" explain="邮件类型定义" allowAlias="false">
        <type name="STATUS_UNKNOWN" number="0" explain="未知"/>
        <type name="UNREAD_TEXT" number="1" explain="未读纯文本"/>
        <type name="UNREAD_ITEM" number="2" explain="未读有附件"/>
        <type name="UNREAD_TRADE" number="3" explain="未读交易"/>
        <type name="READ_TEXT" number="4" explain="已读纯文本"/>
        <type name="READ_ITEM" number="5" explain="已读有附件"/>
        <type name="READ_TRADE" number="6" explain="已读交易"/>
        <type name="READ_NO_ITEM" number="7" explain="已读无附件"/>
    </enum>
</messages>