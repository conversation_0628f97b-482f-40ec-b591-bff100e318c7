syntax = "proto3";

package proto.MailDefine;

option go_package = "world/common/pbBase/MailDefine;MailDefine";

//After are enums.
// 邮件类型定义
// @go-enum-no-prefix
enum MAIL_TYPE{
  TOTAL = 0; //总计 服务端用
  SEND_PLAYER = -5; //客户端使用
  SEND_SERVICE = -4; //客户端使用
  SEND = 1; //已发交易邮件
  SYSTEM = 2; //客服系统邮件
  MONEY = 4; //充值邮件
  TASK = 8; //任务邮件
  SERVICE = 16; //玩家联系客服的邮件
  PLAYER = 32; //玩家邮件
  BACK = 64; //退回邮件
  RECEIPT = 128; //回执邮件
  CHARGE_FAIL = 256; //充值失败邮件
  MERGE_SYSTEM_SERVICE = 18; //系统+客服
  MERGE_BACK_RECEIPT = 192; //退回+回执
}

// 邮件类型定义
// @go-enum-no-prefix
enum MAIL_STATUS{
  STATUS_UNKNOWN = 0; //未知
  UNREAD_TEXT = 1; //未读纯文本
  UNREAD_ITEM = 2; //未读有附件
  UNREAD_TRADE = 3; //未读交易
  READ_TEXT = 4; //已读纯文本
  READ_ITEM = 5; //已读有附件
  READ_TRADE = 6; //已读交易
  READ_NO_ITEM = 7; //已读无附件
}

