<?xml version="1.0" encoding="UTF-8" ?>
<messages package="world/common/pbBase">
    <struct name="LoginResult" explain="登录结果信息">
        <field class="string" name="id" explain="用户id"/>
        <field class="string" name="token" explain="会话凭证"/>
    </struct>
    <struct name="AreaLine" explain="区服信息">
        <field class="int32" name="id" explain="区服id"/>
        <field class="string" name="name" explain="区服名称"/>
        <field class="int32" name="openTime" explain="区服名称"/>
        <field class="pbBase/AreaLineState/AreaLineState.Type" name="state" explain="区服状态"/>
        <field class="int32" name="actorCount" explain="拥有角色数量"/>
    </struct>
    <struct name="CrossSimplePlayer" explain="跨节点简要传递玩家数据">
        <field class="string" name="from" explain="发送方，即玩家当前所在节点"/>
        <field class="string" name="id" explain="玩家用户id"/>
        <field class="int32" name="gameId" explain="玩家游戏id"/>
        <field class="string" name="name" explain="玩家名称"/>
        <field class="int32" name="mapId" explain="所处地图id"/>
        <field class="int32" name="x" explain="所处地图x"/>
        <field class="int32" name="y" explain="所处地图y"/>
        <field class="int64" name="icon1" explain="icon1"/>
        <field class="int64" name="icon2" explain="icon2"/>
        <field class="int64" name="icon3" explain="icon3"/>
        <field class="int32" name="level" explain="等级"/>
        <field class="int32" name="level2" explain="传奇等级"/>
        <field class="string" name="title" explain="称号"/>
        <field class="int64" name="setting" explain="设置"/>
        <field class="int64" name="status" explain="状态"/>
        <field class="int32" name="mode" explain="设置"/>
        <field class="string" name="shopName" explain="摆摊名称"/>
        <field class="string" name="countryName" explain="国家名称"/>
        <field class="int32" name="vipLv" explain="vip等级"/>
        <field class="int32" name="vipLvMax" explain="历史最高vip等级"/>
        <field class="CrossSimplePet" name="pet" explain="宠物数据"/>
    </struct>
    <struct name="CrossSimplePet" explain="跨节点简要传递宠物数据">
        <field class="int32" name="cfgId" explain="宠物配置id"/>
        <field class="int64" name="id" explain="宠物id"/>
        <field class="string" name="name" explain="自定义名字，可能存在"/>
        <field class="int64" name="age" explain="寿命"/>
    </struct>
    <struct name="PlayerEvent" explain="玩家事件数据">
        <field class="int32" name="eventId" explain="事件id"/>
        <field class="int32" name="eventType" explain="事件类型"/>
        <field class="string" name="message" explain="事件消息"/>
        <field class="string" name="extraInfo" explain="额外信息"/>
        <field class="int64" name="expireTime" explain="过期时间"/>
        <field class="CrossSimplePlayer" name="player" explain="发起者"/>
    </struct>
    <struct name="SimplePlayerInfo" explain="简要玩家数据,用于拉取角色列表时使用">
        <field class="int32" name="id" explain="玩家id"/>
        <field class="int32" name="mapId" explain="当前所处地图"/>
        <field class="int32" name="x" explain="所处地图x"/>
        <field class="int32" name="y" explain="所处地图y"/>
        <field class="string" name="name" explain="玩家名称"/>
        <field class="AttrData" name="attr" explain="属性"/>
        <field class="int64" name="deleteEndTime" explain="删除计时"/>
        <field class="OfflineTaskData" name="offlineTask" explain="离线任务数据"/>
    </struct>
    <struct name="PlayerInfo" explain="玩家数据,玩家进入游戏时推送">
        <field class="int32" name="id" explain="玩家id"/>
        <field class="int32" name="mapId" explain="当前所处地图"/>
        <field class="int32" name="x" explain="所处地图x"/>
        <field class="int32" name="y" explain="所处地图y"/>
        <field class="string" name="name" explain="玩家名称"/>
        <field class="int64" name="setting" explain="设置"/>
        <field class="int32" name="mode" explain="设置"/>
        <field class="AttrData" name="attr" explain="属性"/>
        <field class="BagData" name="bag" explain="背包"/>
        <field class="TaskData" name="task" explain="任务"/>
        <array class="int32" name="itemSetData" explain="玩家套装数据"/>
        <field class="SkillData" name="skill" explain="技能"/>
        <field class="int64" name="petId" explain="上阵的宠物"/>
        <field class="bool" name="unreadMail" explain="是否有未读邮件"/>
    </struct>
    <struct name="AttrData" explain="属性模块">
        <field class="int64" name="icon1" explain="icon1"/>
        <field class="int64" name="icon2" explain="icon2"/>
        <field class="int64" name="icon3" explain="icon3"/>
        <field class="int64" name="status" explain="状态"/>
        <field class="int32" name="level" explain="等级"/>
        <field class="int32" name="level2" explain="传奇等级"/>
        <field class="int32" name="exp" explain="普通经验"/>
        <field class="int32" name="exp2" explain="传奇经验"/>
        <field class="int32" name="vipLv" explain="vip等级"/>
        <field class="int32" name="vipLvMax" explain="历史最高vip等级"/>
        <field class="int32" name="cp" explain="未使用的技能点"/>
        <field class="int32" name="str" explain="力量"/>
        <field class="int32" name="agi" explain="敏捷"/>
        <field class="int32" name="con" explain="体质"/>
        <field class="int32" name="ilt" explain="智力"/>
        <field class="int32" name="wis" explain="感知"/>
        <field class="int32" name="hp" explain="血"/>
        <field class="int32" name="mp" explain="蓝"/>
    </struct>
    <struct name="BagData" explain="背包数据">
        <field class="int32" name="money1" explain="黄金"/>
        <field class="int32" name="money2" explain="金叶"/>
        <field class="int32" name="money3" explain="铜币"/>
        <field class="int32" name="bagSize" explain="背包格子数量"/>
        <field class="int32" name="selfStoreSize" explain="个人仓库购买数量"/>
        <field class="map" name="store" explain="物品数据">
            <key class="int32" explain="slotPos"/>
            <value class="ItemData" explain="物品"/>
        </field>
    </struct>
    <struct name="PetData" explain="物品数据">
        <field class="int32" name="cfgId" explain="宠物配置id"/>
        <field class="string" name="name" explain="自定义名字，可能存在"/>
        <field class="int32" name="grow" explain="成长值"/>
        <field class="int32" name="learn" explain="领悟值"/>
        <field class="int32" name="growLevel" explain="成长等级"/>
        <field class="AttrData" name="attr" explain="属性"/>
        <field class="SkillData" name="skill" explain="技能"/>
        <field class="int64" name="age" explain="寿命"/>
        <field class="int32" name="growExp" explain="成长经验"/>
        <field class="int64" name="id" explain="宠物id"/>
    </struct>
    <struct name="ItemData" explain="物品数据">
        <field class="int32" name="id" explain="id"/>
        <field class="int32" name="slotPos" explain="位置"/>
        <field class="int32" name="quantity" explain="数量"/>
        <field class="int32" name="status" explain="状态"/>
        <field class="PowerData" name="power1" explain="基础属性1"/>
        <field class="PowerData" name="power2" explain="基础属性2"/>
        <field class="PowerData" name="power3" explain="基础属性3"/>
        <field class="PowerData" name="bindPower1" explain="绑定属性1"/>
        <field class="PowerData" name="bindPower2" explain="绑定属性2"/>
        <field class="PowerData" name="power4" explain="进阶属性1"/>
        <field class="PowerData" name="power5" explain="进阶属性2"/>
        <field class="PowerData" name="power6" explain="进阶属性3"/>
        <field class="PowerData" name="power7" explain="进阶属性4"/>
        <field class="PowerData" name="enchantPower1" explain="附魔1"/>
        <field class="PowerData" name="enchantPower2" explain="附魔2"/>
        <field class="int32" name="durability" explain="耐久"/>
        <field class="int32" name="attachDone" explain="宝石镶嵌数量"/>
        <field class="PowerData" name="attachPower" explain="宝石属性"/>
        <field class="int64" name="expireTime" explain="过期时间"/>
        <field class="int32" name="star" explain="普通升星数量"/>
        <field class="int32" name="upgradeStar" explain="进阶升星数量"/>
        <field class="int64" name="petId" explain="宠物唯一id"/>
        <field class="PetData" name="petItem" explain="宠物数据"/>
        <field class="map" name="extra" explain="扩展信息">
            <key class="string" explain="key"/>
            <value class="string" explain="value"/>
        </field>
    </struct>
    <struct name="Condition" explain="条件数据">
        <field class="pbBase/ConditionType/ConditionType.Type" name="type" explain="类型"/>
        <field class="int32" name="id" explain="id"/>
        <field class="int32" name="num" explain="需求数量"/>
        <field class="string" name="extra" explain="扩展数据"/>
    </struct>
    <struct name="TaskData" explain="任务模块数据">
        <field class="bytes" name="taskStatus" explain="物品数据"/>
        <array class="TaskInfo" name="tasks" explain="物品数据"/>
    </struct>
    <struct name="TaskInfo" explain="任务数据">
        <field class="int32" name="id" explain="物品数据"/>
        <array class="Condition" name="cond" explain="条件详情"/>
    </struct>
    <struct name="PowerData" explain="属性数据">
        <field class="pbBase/MyDefine/MyDefine.POWER" name="type" explain="属性类型"/>
        <field class="int32" name="value" explain="属性值"/>
    </struct>
    <struct name="OfflineTaskData" explain="离线任务数据">
    </struct>
    <struct name="Point" explain="点位数据">
        <field class="int32" name="x" explain="x"/>
        <field class="int32" name="y" explain="y"/>
    </struct>
    <struct name="SkillData" explain="技能模块数据">
        <field class="int32" name="sp" explain="技能点"/>
        <field class="map" name="list" explain="技能列表数据">
            <key class="int32" explain="技能id"/>
            <value class="SkillInfo" explain="技能信息"/>
        </field>
        <field class="int32" name="cnt" explain="技能槽数量"/>
        <field class="int32" name="activeAutoSkillId" explain="自动释放的主动技能id"/>
        <array class="int32" name="autoSkillId" explain="自动释放的自动技能id"/>
    </struct>
    <struct name="SkillInfo" explain="技能信息">
        <field class="int32" name="id" explain="技能id"/>
        <field class="int32" name="baseLevel" explain="技能基础等级"/>
        <field class="int32" name="addLevel" explain="技能增加等级"/>
        <field class="bool" name="isLearn" explain="是不是学习而来"/>
    </struct>
    <struct name="ItemUseResultPetAddSkillItem" explain="使用物品响应-宠物潜能石">
        <array class="int32" name="old" explain="原本拥有的潜能技能id"/>
        <array class="SkillInfo" name="new" explain="新的潜能技能"/>
    </struct>
    <struct name="BattlePlanObj" explain="战斗出招数据结构体">
        <field class="pbBase/BattleDefine/BattleDefine.Plan" name="type" explain="出招类型"/>
        <field class="int32" name="position" explain="目标位置"/>
        <field class="int32" name="extra" explain="如果是使用技能,技能id;如果是使用物品,物品id"/>
    </struct>
    <struct name="MailSimpleNumInfo" explain="邮件简单信息">
        <field class="pbBase/MailDefine/MailDefine.MAIL_TYPE" name="type" explain="类型"/>
        <field class="int32" name="unread" explain="未读"/>
        <field class="int32" name="read" explain="已读"/>
    </struct>
    <struct name="Mail" explain="邮件数据">
        <field class="int64" name="id" explain="邮件id"/>
        <field class="int32" name="receiver" explain="接收者id"/>
        <field class="string" name="title" explain="标题"/>
        <field class="string" name="content" explain="内容"/>
        <field class="string" name="receiverName" explain="接收者名称"/>
        <field class="string" name="senderName" explain="发送者名称"/>
        <field class="int32" name="sender" explain="发送者id"/>
        <field class="pbBase/MailDefine/MailDefine.MAIL_TYPE" name="type" explain="类型"/>
        <field class="pbBase/MailDefine/MailDefine.MAIL_STATUS" name="status" explain="状态"/>
        <array class="ItemData" name="appendix" explain="附件"/>
        <array class="ItemData" name="selectItem" explain="可选列表"/>
        <field class="int64" name="expireTime" explain="过期时间"/>
        <field class="int32" name="reqMoney1" explain="索要黄金"/>
        <field class="int32" name="reqMoney3" explain="索要铜币"/>
        <field class="int32" name="money1" explain="赠送黄金"/>
        <field class="int32" name="money3" explain="赠送铜币"/>
    </struct>
</messages>