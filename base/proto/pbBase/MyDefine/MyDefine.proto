syntax = "proto3";

package proto.MyDefine;

option go_package = "world/common/pbBase/MyDefine;MyDefine";

//After are enums.
// 通用定义
// @go-enum-no-prefix
enum Const{
  None = 0; //自动添加
  CAN_DELETE = 4; //占位
  BACK_ERROR_DUR = -400; //
  BACK_ERROR_NULL_HAND = -401; //
}

// 属性能力定义
// @go-enum-no-prefix
enum POWER{
  POWER_NONE = 0; //未知
  POWER_STR = 1; //力量
  POWER_STR_PERCENT = 2; //力量百分比
  POWER_CON = 3; //体质
  POWER_CON_PERCENT = 4; //体质百分比
  POWER_AGI = 5; //敏捷
  POWER_AGI_PERCENT = 6; //敏捷百分比
  POWER_ILT = 7; //智力
  POWER_ILT_PERCENT = 8; //智力百分比
  POWER_WIS = 9; //感知
  POWER_WIS_PERCENT = 10; //感知百分比
  POWER_HPMAX = 11; //
  POWER_HPMAX_PERCENT = 12; //
  POWER_MPMAX = 13; //
  POWER_MPMAX_PERCENT = 14; //
  POWER_SPEED_PERCENT = 23; //
  POWER_HITRATE_PERCENT = 24; //
  POWER_DODGE_PERCENT = 25; //
  POWER_MAGIC_HITRATE_PERCENT = 26; //
  POWER_CRITICAL_PERCENT = 27; //
  POWER_ATK_STR_PERCENT = 28; //
  POWER_ATK_AGI_PERCENT = 29; //
  POWER_ATK_MAGIC_PERCENT = 30; //
  POWER_DEF_STR_PERCENT = 31; //
  POWER_DEF_AGI_PERCENT = 32; //
  POWER_DEF_MAGIC_PERCENT = 33; //
  POWER_WIL_PERCENT = 34; //
  POWER_TOUGH_PERCENT = 35; //
  POWER_REFLECTION_PERCENT = 36; //
  POWER_BLOCK_PERCENT = 37; //
  POWER_INSIGHT_PERCENT = 38; //
  POWER_PENETRATION_PERCENT = 39; //
  POWER_DEF_FIELD_PERCENT = 40; //
  POWER_BACK_PERCENT = 41; //
  POWER_MAGIC_BACK_PERCENT = 42; //
  POWER_LIFE_ABSORPTION_PERCENT = 43; //
  POWER_MANA_ABSORPTION_PERCENT = 44; //
  POWER_MAGIC_PENETRATION_PERCENT = 45; //
  POWER_HIT_FORCE_PERCENT = 46; //
  POWER_HEAL_RECOVERY_PERCENT = 47; //
  POWER_MANA_RECOVERY_PERCENT = 48; //
  POWER_HP = 49; //
  POWER_HP_PERCENT = 50; //
  POWER_MP = 51; //
  POWER_MP_PERCENT = 52; //
  POWER_SPEED = 53; //
  POWER_HITRATE = 54; //
  POWER_DODGE = 55; //
  POWER_MAGIC_HITRATE = 56; //
  POWER_CRITICAL = 57; //
  POWER_ATK_STR = 58; //
  POWER_ATK_AGI = 59; //
  POWER_ATK_MAGIC = 60; //
  POWER_DEF_STR = 61; //
  POWER_DEF_AGI = 62; //
  POWER_DEF_MAGIC = 63; //
  POWER_WIL = 64; //
  POWER_TOUGH = 65; //
  POWER_REFLECTION = 66; //
  POWER_BLOCK = 67; //
  POWER_INSIGHT = 68; //
  POWER_PENETRATION = 69; //
  POWER_DEF_FIELD = 70; //
  POWER_BACK = 71; //
  POWER_MAGIC_BACK = 72; //
  POWER_LIFE_ABSORPTION = 73; //
  POWER_MANA_ABSORPTION = 74; //
  POWER_MAGIC_PENETRATION = 75; //
  POWER_HIT_FORCE = 76; //
  POWER_HEAL_RECOVERY = 77; //
  POWER_MANA_RECOVERY = 78; //
  POWER_REMOVE_STATUS = 79; //
  POWER_RECOVER = 80; //
  POWER_SKILL_DAMAGE = 81; //
  POWER_SKILL_HITRATE = 82; //
  POWER_SELF_CRITICAL = 84; //
  POWER_SKILL_HIT_FORCE = 85; //
  POWER_SKILL_MAGIC_PENETRATION = 86; //
  POWER_SKILL_BRK_ARMOR = 87; //
  POWER_SKILL_REMOVE_STATUS = 88; //
  POWER_PET_DAMAGE = 89; //
  POWER_PET_HPMAX_PERCENT = 90; //
  POWER_PET_MPMAX_PERCENT = 91; //
  POWER_PET_STR_PERCENT = 92; //
  POWER_PET_CON_PERCENT = 93; //
  POWER_PET_AGI_PERCENT = 94; //
  POWER_PET_ILT_PERCENT = 95; //
  POWER_PET_WIS_PERCENT = 96; //
  POWER_RECOVER_PERCENT = 97; //
  POWER_HPMP_RECOVER = 98; //
  POWER_OPEN_STORE = 99; //
  POWER_CHEST_LV1 = 100; //直接开启
  POWER_CHEST_LV2 = 101; //需要万能钥匙开启
  POWER_CHEST_LV3 = 102; //需要神秘钥匙开启
  POWER_REQ_SLOT = 103; //随机奖池
  POWER_TO_WORLDMAP = 104; //
  POWER_TO_GXGY = 105; //
  POWER_EXPIRE_TIME = 106; //
  POWER_RESET_MISSION = 107; //
  POWER_CHEST_KEY_LEVEL = 108; //
  POWER_PET_EGG = 109; //
  POWER_COSTUME = 110; //
  POWER_TRANSPORT = 111; //
  POWER_POWER_TITLE = 112; //
  POWER_GUARD_STR_ATTACK = 113; //
  POWER_GUARD_AGI_ATTACK = 114; //
  POWER_GUARD_MAGIC_ATTACK = 115; //
  POWER_GUARD_CURSE_ATTACK = 116; //
  POWER_GUARD_ALL_ATTACK = 117; //
  POWER_PET_ADD_EXP = 118; //
  POWER_ADD_EXP = 119; //
  POWER_EXP_BY_TIME = 120; //
  POWER_IDENTIFY = 121; //
  POWER_SWORD_ATK_TIME = 122; //
  POWER_BLADE_ATK_TIME = 123; //
  POWER_HEAVY_ATK_TIME = 124; //
  POWER_LANCE_ATK_TIME = 125; //
  POWER_STAFF_ATK_TIME = 126; //
  POWER_HAND_ATK_TIME = 127; //
  POWER_BOW_ATK_TIME = 128; //
  POWER_HAND_ITEM_ATK_TIME = 129; //
  POWER_ALL_ATK_TIME = 130; //
  POWER_COMPOSITE = 131; //
  POWER_SWORD_PERCENT = 133; //
  POWER_BLADE_PERCENT = 134; //
  POWER_HEAVY_PERCENT = 135; //
  POWER_LANCE_PERCENT = 136; //
  POWER_STAFF_PERCENT = 137; //
  POWER_HAND_PERCENT = 138; //
  POWER_BOW_PERCENT = 139; //
  POWER_HAND_ITEM_PERCENT = 140; //
  POWER_ALL_PERCENT = 141; //
  POWER_EQUIP_ARMOR_DUR_PERCENT = 142; //
  POWER_SKILL_HP = 145; //
  POWER_SKILL_HP_PERCENT = 146; //
  POWER_SKILL_MP = 147; //
  POWER_SKILL_MP_PERCENT = 148; //
  POWER_SKILL_LIFE_ABSORPTION = 149; //
  POWER_SKILL_MANA_ABSORPTION = 150; //
  POWER_SKILL_TARGET_BACK = 151; //
  POWER_SKILL_TARGET_MAGIC_BACK = 152; //
  POWER_SKILL_TARGET_BLOCK = 153; //
  POWER_SKILL_TARGET_INSIGHT = 154; //
  POWER_SKILL_TARGET_WIL = 155; //
  POWER_SKILL_TARGET_TOUCH = 156; //
  POWER_GRARD_MASTER_STR_ATTACK = 157; //
  POWER_GRARD_MASTER_AGI_ATTACK = 158; //
  POWER_GRARD_MASTER_MAGIC_ATTACK = 159; //
  POWER_GRARD_MASTER_CURSE_ATTACK = 160; //
  POWER_GRARD_MASTER_ALL_ATTACK = 161; //
  POWER_PET_GRARD_STR_ATTACK = 162; //
  POWER_PET_GRARD_AGI_ATTACK = 163; //
  POWER_PET_GRARD_MAGIC_ATTACK = 164; //
  POWER_PET_GRARD_CURSE_ATTACK = 165; //
  POWER_PET_GRARD_ALL_ATTACK = 166; //
  POWER_EXP_MISSION_BY_TIME = 167; //
  POWER_IGNORE_BACK = 168; //
  POWER_IGNORE_MAGIC_BACK = 169; //
  POWER_IGNORE_BLOCK = 170; //
  POWER_IGNORE_INSIGHT = 171; //
  POWER_IGNORE_WIL = 172; //
  POWER_IGNORE_TOUCH = 173; //
  POWER_BALL_ATK_TIME = 174; //
  POWER_BALL_PERCENT = 175; //
  POWER_SKILL_SCROLL = 176; //
  POWER_SKILL_SCROLL_PET = 177; //
  POWER_KEEPOUT_ATK_TIME = 178; //
  POWER_NEW_GET_PET = 179; //
  POWER_NEW_GET_ITEM = 180; //
  POWER_ENCHANT_ITEM = 181; //
  POWER_FORMATION_BOOK = 182; //
  POWER_CHEST_LV4 = 183; //
  POWER_COLOR_BOX = 184; //
  POWER_TURN_MONSTER_CARD = 185; //
  POWER_SKILL_BOOK_PET = 186; //
  POWER_GUN_ATK_TIME = 190; //
  POWER_GUN_PERCENT = 191; //
  POWER_HAMMER_ATK_TIME = 192; //
  POWER_HAMMER_PERCENT = 193; //
  POWER_FAN_ATK_TIME = 194; //
  POWER_FAN_PERCENT = 195; //
  POWER_MAGIC_PERCENT = 196; //
  POWER_PHYSICS_PERCENT = 197; //
  POWER_HP_MP = 198; //
  POWER_IGNORE_CRITICAL = 201; //
  POWER_IGNORE_CRITICAL_PERCENT = 202; //
  POWER_CRITICAL_DAMAGE = 203; //
  POWER_CRITICAL_DAMAGE_PERCENT = 204; //
  POWER_BLADE_L_ATK_TIME = 210; //
  POWER_BLADE_H_ATK_TIME = 211; //
  POWER_SWORD_L_ATK_TIME = 212; //
  POWER_SWORD_H_ATK_TIME = 213; //
  POWER_CROSSBOW_L_ATK_TIME = 214; //
  POWER_CROSSBOW_H_ATK_TIME = 215; //
  POWER_ARROW_ATK_TIME = 216; //
  POWER_BLADE_L_DAMAGE_PERCENT = 230; //
  POWER_BLADE_H_DAMAGE_PERCENT = 231; //
  POWER_SWORD_L_DAMAGE_PERCENT = 232; //
  POWER_SWORD_H_DAMAGE_PERCENT = 233; //
  POWER_CROSSBOW_L_DAMAGE_PERCENT = 234; //
  POWER_CROSSBOW_H_DAMAGE_PERCENT = 235; //
  POWER_ARROW_DAMAGE_PERCENT = 236; //
  POWER_DEF_STR_RANGE = 250; //
  POWER_DEF_STR_RANGE_PERCENT = 251; //
  POWER_DEF_STR_NEARBY = 252; //
  POWER_DEF_STR_NEARBY_PERCENT = 253; //
  POWER_DEF_AGI_RANGE = 254; //
  POWER_DEF_AGI_RANGE_PERCENT = 255; //
  POWER_DEF_AGI_NEARBY = 256; //
  POWER_DEF_AGI_NEARBY_PERCENT = 257; //
  POWER_ATK_STR_NEARBY = 258; //
  POWER_ATK_STR_NEARBY_PERCENT = 259; //
  POWER_ATK_STR_RANGE = 260; //
  POWER_ATK_STR_RANGE_PERCENT = 261; //
  POWER_ATK_AGI_NEARBY = 262; //
  POWER_ATK_AGI_NEARBY_PERCENT = 263; //
  POWER_ATK_AGI_RANGE = 264; //
  POWER_ATK_AGI_RANGE_PERCENT = 265; //
}

// 物品id定义
// @go-enum-no-prefix
enum ITEM_ID{
  ID_NONE = 0; //无效物品
  PET = 24; //宠物
  WOOD = 1000; //木头
  STONE = 1001; //石头
  IRON = 1002; //铁
  IDENTIFY_SCROLL = 40000; //鉴定卷轴
  IDENTIFY_SCROLL_BIND = 40001; //鉴定卷轴(绑定)
  COMMAND_BOOK = 40002; //指令书
  PET_RESET = 40004; //宠物洗髓石
  PET_AGE = 40005; //宠物返老还童石
  REPAIR = 40008; //野外修理卷
  PET_RESET_2 = 40015; //宠物重生石
  CHANGE_NAME = 40016; //改名卷
  SPEAK = 40017; //小喇叭
  PET_ADD_SKILL = 40020; //宠物潜能石
  STAR_SCROLL = 40021; //装备强化卷
  CHANGE_SEX = 42000; //变性卷轴
  CP_POINT_ADD = 42001; //属性点道具
  SP_POINT_ADD = 42002; //技能点道具
  PROSPERITY_DEGREE_POINT_ADD = 42003; //繁荣度
  SKILL_PLAYER = 42004; //人物技能槽
  SKILL_PET = 42005; //宠物技能卷槽
  SKILL_PLAYER_2 = 42006; //人物技能卷槽(绑)
  SKILL_PET_2 = 42007; //宠物技能卷槽(绑)
  HIGH_IDENTIFY_SCROLL = 40022; //高级鉴定卷轴
  HIGH_IDENTIFY_SCROLL_BIND = 40023; //高级鉴定卷轴(绑)
  UPGRADE_IDENTIFY_SCROLL = 40024; //进阶鉴定卷轴
  UPGRADE_IDENTIFY_SCROLL_BIND = 40025; //进阶鉴定卷轴(绑)
  UPGRADE_INTENSIFY_SCROLL = 40026; //进阶强化卷轴
  UPGRADE_INTENSIFY_SCROLL_BIND = 40027; //进阶强化卷轴(绑)
  PET_EXPERIENCE_BOOK = 40047; //宠物经验卷
  PET_EXPERIENCE_BOOK2 = 40043; //宠物经验卷
  PET_EXPERIENCE_BOOK3 = 40006; //宠物经验卷
  SPEAK2 = 42016; //跨服小喇叭
  SKILL_PLAYER_3 = 42017; //中级人物技能槽
  SKILL_PET_3 = 42018; //中级宠物技能槽(绑)
  ADD_BAG_SIZE = 288; //10格背包扩展
  HAIR_START = 40100; //
  HAIR_END = 40199; //
  FACE_START = 40200; //
  FACE_END = 40249; //
  RANGER = 41100; //转职书-侠客
  XIUZHEN = 41101; //转职书-修真
  WARRIOR = 41102; //转职书-战士
  WIZARD = 41103; //转职书-法师
  NEW = 41104; //转职书-贤者
  BACKUP2 = 41105; //转职书-武圣
  BACKUP3 = 41106; //转职书-枪王
  BACKUP4 = 41107; //转职书-锤师
  BACKUP5 = 41108; //转职书-岚舞
  BACKUP6 = 41109; //转职书-备用
  BACKUP7 = 41110; //转职书-备用
  UNIVERSAL_KEY = 40050; //万能钥匙
  UNIVERSAL_KEY_BIND = 40051; //万能钥匙(绑)
  SECRET_KEY = 40052; //神秘钥匙
  SECRET_KEY_BIND = 40053; //神秘钥匙(绑)
}

