syntax = "proto3";

package proto.BattleDefine;

option go_package = "world/common/pbBase/BattleDefine;BattleDefine";

//After are enums.
// 条件定义
// @go-enum-no-prefix
enum Type{
  LOCAL = 0; //本地
  REMOTE = 2; //联网
  PK = 4; //pk对战
  PET_PK = 8; //宠物对战
  FINAL_BOSS = 16; //boss
  CROSS_SERVER = 32; //跨服
  CROSS_RANK = 64; //跨服pk
  CAMP_WAR = 128; //国战
}

// 条件定义
// @go-enum-no-prefix
enum Result{
  NONE = 0; //未知
  LEFT_WIN = 1; //左边胜利
  RIGHT_WIN = 2; //右边胜利
  ROUND_OVER = 3; //超回合
  BOTH_LOSE = 4; //双方都失败
  WAR_TIME_OUT = 8; //超时
  ARENA_OUT = 10; //
}

// 出招选择定义
// @go-enum-no-prefix
enum Order{
  OrderNone = 0; //自动添加
  NONE_ORDER = -1; //未知
  ATTACK = 1; //攻击
  SKILL = 2; //技能
  ITEM = 3; //使用物品
  AUTO = 4; //自动
  INFO = 5; //查看
  ESCAPE = 6; //逃跑
  REPLAY = 7; //重播
  AUTO_ESCAPE = 8; //自动逃跑
}

// 出招计划类型
// @go-enum-no-prefix
enum Plan{
  PLAN_NONE = 0; //未知
  PLAN_ATTACK = 1; //攻击
  PLAN_USE_SKILL = 2; //使用技能
  PLAN_USE_ITEM = 3; //使用物品
  PLAN_ESCAPE = 4; //逃跑
}

