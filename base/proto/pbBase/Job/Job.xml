<?xml version="1.0" encoding="UTF-8" ?>
<messages package="world/common/pbBase/Job">
    <enum name="Type" explain="职业类型" allowAlias="false">
        <type name="ZeroNone" number="0" explain="未知"/>
        <type name="<PERSON>" number="1" explain="侠客"/>
        <type name="XiuZhen" number="2" explain="修真"/>
        <type name="Warrior" number="3" explain="战士"/>
        <type name="Wizard" number="4" explain="法师"/>
        <type name="New" number="5" explain="贤者"/>
        <type name="Backup2" number="6" explain="武圣"/>
        <type name="PetWil" number="7" explain="睿智"/>
        <type name="PetStr" number="8" explain="勇猛"/>
        <type name="PetAgi" number="9" explain="迅捷"/>
        <type name="Backup3" number="10" explain="枪王"/>
        <type name="ChuiShi" number="11" explain="锤师"/>
        <type name="LanWu" number="12" explain="岚舞"/>
    </enum>
</messages>