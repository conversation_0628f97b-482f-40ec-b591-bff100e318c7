syntax = "proto3";

package proto.ITEM_TYPE;

option go_package = "world/common/pbBase/ITEM_TYPE;ITEM_TYPE";

//After are enums.
// 物品type定义 类型
// @go-enum-no-prefix
enum Type{
  ARMOR_HEAD = 0; //防具-头盔
  ARMOR_CLOTHES = 1; //防具-衣服
  ARMOR_TROUSERS = 2; //防具-裤子
  ARMOR_SHOULDER = 3; //防具-肩部
  ARMOR_WAIST = 4; //防具-腰部
  ARMOR_BACK = 5; //防具-背部
  ARMOR_SHOES = 6; //防具-鞋子
  ARMOR_HAND = 7; //防具-手套
  ARMOR_NECKLACE = 8; //防具-项链
  ARMOR_RING = 9; //防具-戒指
  ARMOR_AMULET = 10; //防具-护符
  ARMOR_TRANSPORT  = 11; //防具-坐骑
  ARMOR_FASHION  = 12; //防具-时装
  WEAPON_ONEHAND_SWORD  = 13; //武器-单手剑
  WEAPON_TWOHAND_SWORD  = 14; //武器-重剑
  WEAPON_ONEHAND_BLADE  = 15; //武器-单手刀
  WEAPON_TWOHAND_BLADE  = 16; //武器-重刀
  WEAPON_ONEHAND_HEAVY  = 17; //武器-单手重型
  WEAPON_TWOHAND_HEAVY  = 18; //武器-双持重型
  WEAPON_TWOHAND_STAFF  = 19; //武器-法杖
  WEAPON_TWOHAND_LANCE  = 20; //武器-长柄
  WEAPON_ONEHAND_CROSSBOW  = 21; //武器-单手弩
  WEAPON_TWOHAND_CROSSBOW  = 22; //武器-重弩
  WEAPON_TWOHAND_BOW  = 23; //武器-弓箭
  WEAPON_ONEHAND_HAND  = 24; //武器-副手
  TASK = 25; //任务物品
  BATTLE_USE = 26; //战斗中使用药品
  ANYTIME_USE = 27; //任何时间使用药品
  NOT_BATTLE_USE  = 28; //战斗中不可使用药品
  BUILD_MATERIAL = 29; //建筑材料
  GEM = 30; //宝石
  SKILL_BOOK  = 31; //技能书
  PET = 32; //宠物
  SPECIAL = 33; //特殊物品
  WEAPON_BALL = 34; //法器
  WEAPON_ONEHAND_GUN = 35; //武器-轻枪
  WEAPON_TWOHAND_GUN = 36; //武器-重枪
  WEAPON_ONEHAND_HAMMER = 37; //武器-轻锤
  WEAPON_TWOHAND_HAMMER = 38; //武器-重锤
  WEAPON_TWOHAND_FAN = 39; //武器-扇
  BLOOD_BOTTLE = 40; //能量精华
  PET_EQUIP = 41; //宠物装备
  PET_EQUIP_EXP_BOOK   = 42; //宠物装备经验
  SEAL = 43; //赋灵符
  ENERGY_ESSENCE = 44; //赋灵符
  BOX_CHOOSE_ONE = 45; //自选盒子
  ADD_PET_LIFE = 46; //增加宠物寿命
  LN_STONE = 47; //
}

