syntax = "proto3";

package proto.ConditionType;

option go_package = "world/common/pbBase/ConditionType;ConditionType";

//After are enums.
// 条件定义
// @go-enum-no-prefix
enum Type{
  None = 0; //自动添加
  Level = 1; //等级
  KillMonster = 2; //杀怪数
  Money1 = 3; //黄金
  Money2 = 4; //金叶
  Money3 = 5; //铜币
  Honor = 6; //荣誉
  HaveItem = 7; //物品
  EquipItem = 8; //装备物品
  MissionDone = 9; //完成任务
  MissionDoing = 10; //接受任务（当前持有某任务）
  PlayerRace = 11; //阵营
  InMap = 12; //在某个地图
  AfterDate = 13; //某个日期之后
  AfterTime = 14; //某个小时之后（每天）
  PlayerSex = 15; //性别
  JoinCountry = 16; //加入国家
  HaveBuff = 17; //持有Buff
  PlayerHp = 18; //生命值达到
  PlayerMp = 19; //法力值达到
  PlayerCon = 20; //体质
  PlayerStr = 21; //力量
  PlayerIlt = 22; //智力
  PlayerAgi = 23; //敏捷
  Vip = 24; //vip等级
  CityLevel = 27; //城市等级
  CityBranch = 28; //完成城市任务
  CityDegree = 29; //城市繁荣度
  CityArmy = 30; //城市军力
  PlayerJob = 32; //职业
  CountryRank = 33; //国家职位是xxx
  CountryRank2 = 34; //国家职位达到xxx
  PlayerExp = 35; //经验值
}

