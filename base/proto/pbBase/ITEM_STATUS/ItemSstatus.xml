<?xml version="1.0" encoding="UTF-8" ?>
<messages package="world/common/pbBase/ITEM_STATUS">
    <enum name="Type" explain="物品状态定义" allowAlias="false">
        <type name="None" number="0" explain="自动添加"/>
        <type name="TIME_ITEM" number="1" explain="限时物品"/>
        <type name="BIND" number="2" explain="绑定"/>
        <type name="SHOP_LOCKED" number="4" explain="商店锁定"/>
        <type name="TIME_OUT" number="8" explain="过期"/>
        <type name="CAN_IDENTIFY" number="16" explain="可鉴定"/>
        <type name="ATTACK_BROKEN" number="32" explain="镶嵌破损"/>
        <type name="VIP_TIME_OUT" number="64" explain="vip过期"/>
        <type name="SELLING" number="256" explain="摆摊出售中"/>
    </enum>
</messages>