<?xml version="1.0" encoding="UTF-8" ?>
<messages package="world/common/pbBase">
    <enum name="PlayerModeType" explain="玩家模型的状态" allowAlias="false">
        <type name="ModeNormal" number="0" explain="正常状态"/>
        <type name="ModeShop" number="1" explain="摆摊"/>
        <type name="ModeBattleLocal" number="50" explain="本地战斗"/>
        <type name="ModeBattleRemote" number="51" explain="联网战斗"/>
        <type name="ModeBattlePk" number="52" explain="pk战斗"/>
        <type name="ModeBattleOb" number="53" explain=""/>
        <type name="ModeBattlePetPk" number="54" explain="宠物对战"/>
    </enum>
</messages>