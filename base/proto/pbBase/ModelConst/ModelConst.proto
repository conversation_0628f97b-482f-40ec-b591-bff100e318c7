syntax = "proto3";

package proto.ModelConst;

option go_package = "world/common/pbBase/ModelConst;ModelConst";

//After are enums.
// 玩家常量
// @go-enum-no-prefix
enum Type{
  option allow_alias = true;
  EXP = 0; //当前经验
  EXPMAX = 1; //最大经验值
  HP = 2; //血
  MP = 3; //蓝
  CP = 4; //属性点
  SP = 5; //技能点
  STR = 6; //力量
  CON = 7; //体质
  AGI = 8; //敏捷
  ILT = 9; //智力
  WIS = 10; //感知
  MONEY1 = 11; //黄金
  MONEY2 = 12; //金叶
  MONEY3 = 13; //铜币
  NUM_BAG = 14; //背包格子数量
  NUM_STROE = 15; //仓库格子数量
  COUNTRY_HONOR = 16; //国家贡献
  CITY_ID = 17; //个人城市id
  KILL_COUNT = 18; //杀怪数
  PK_WIN_COUNT = 19; //pk胜场
  PK_LOSE_COUNT = 20; //pk输场
  TOTAL_ONLINE = 21; //在线时长
  PARTNER_ID = 22; //伴侣id
  MASTER_ID = 23; //师傅
  APPRENTICE1 = 24; //徒弟1
  APPRENTICE2 = 25; //徒弟2
  APPRENTICE3 = 26; //徒弟3
  APPRENTICE4 = 27; //徒弟4
  APPRENTICE5 = 28; //徒弟5
  HPMAX = 29; //最大血量
  MPMAX = 30; //最大蓝量
  SPEED = 31; //出手速度
  ATK_TYPE = 32; //攻击方式
  ATK_MIN = 33; //最小武伤
  ATK_MAX = 34; //最大武伤
  ATK_TIME = 35; //攻击次数
  ATK_STR = 36; //劈砍攻击力
  ATK_AGI = 37; //穿刺攻击力
  ATK_MAGIC = 38; //魔法攻击力
  DEF_STR = 39; //劈砍防御力
  DEF_AGI = 40; //穿刺防御力
  DEF_MAGIC = 41; //魔法防御力
  DODGE = 42; //闪避
  HIT_RATE = 43; //物理命中
  HIT_MAGIC = 44; //魔法命中
  CRITICAL = 45; //致命点（暴击）
  FORCE_HIT = 46; //强命
  EXP_UP = 47; //经验加成
  WIL = 48; //状态抵抗
  TOUGH = 49; //伤害减免
  BLOCK = 50; //格挡
  BRK_ARMOR = 51; //破甲
  LONG_TROUSERS_IN_TRANSPORT = 52; //
  INSIGHT = 53; //洞察
  DEF_FIELD = 54; //防御场
  BACK = 55; //物理反伤
  MAGIC_BACK = 56; //魔法反伤
  LIFE_ABSORPTION = 57; //生命吸收
  MANA_ABSORPTION = 58; //魔法吸收
  MAGIC_PENETRATION = 59; //魔法穿透
  HEAL_RECOVERY = 60; //治疗恢复
  MANA_RECOVERY = 61; //魔法恢复
  RECOVERY = 62; //恢复
  ARGO = 63; //仇恨
  BACK_MAX = 64; //最大反伤
  MASTER_FLAG = 65; //师傅标记
  HELP_COUNTRY = 66; //帮助国家
  LOVE_PLAYER = 67; //喜爱玩家
  INTEGRAL = 68; //积分
  PARTNER_NAME = 69; //伴侣名字
  CRITICAL_DAMAGE = 70; //暴击伤害
  LEFT_WEAPON_TYPE = 71; //左手武器类型
  RIGHT_WEAPON_TYPE = 72; //右手武器类型
  LEFT_ATK_MIN = 73; //左手最小攻击
  LEFT_ATK_MAX = 74; //左手最大攻击
  RIGHT_ATK_MIN = 75; //右手最小攻击
  RIGHT_ATK_MAX = 76; //右手最大攻击
  LEFT_ATK_TIME = 77; //左手攻击次数
  RIGHT_ATK_TIME = 78; //右手攻击次数
  PET_COLOR = 81; //宠物颜色
  PET_GRADE = 82; //宠物品质
  PET_GROW_LEVEL = 83; //宠物成长等级
  BUFFER_REMOVE_STATUS = 90; //移除状态
  HP_DISPLAY = 91; //血量显示
  MP_DISPLAY = 92; //魔法显示
  UID = 101; //唯一ID
  ID = 102; //ID
  SEX = 103; //性别
  RACE = 104; //种族
  JOB = 105; //职业
  LEVEL = 106; //等级
  ICON = 107; //图标
  SETTING = 108; //设置
  STATUS = 109; //状态
  COUNTRY_ID = 110; //国家ID
  COUNTRY_RANK = 111; //国家等级
  COUNTRY_NAME = 112; //国家名称
  PX = 113; //位置X
  PY = 114; //位置Y
  COUNTRY_RANK_SET = 115; //国家等级设置
  COUNTRY_ID_SET = 116; //国家ID设置
  STATUS_SET = 117; //状态设置
  LEVEL2 = 118; //二级等级
  EXP2 = 119; //二级经验
  EXPMAX2 = 120; //二级最大经验
  ENCHANTVALUE = 121; //附魔值
  COMBATPOINT = 122; //战斗力
  VIP_LEVEL_2 = 123; //VIP等级2
  NPC_MSS_NONE = 0; //NPC无任务状态
  NPC_MSS_SUBMIT = 1; //NPC可提交任务
  NPC_MSS_NOT_SUBMIT = 2; //NPC不可提交任务
  NPC_MSS_ACCEPT = 3; //NPC可接任务
  NPC_MSS_NOT_ACCEPT = 4; //NPC不可接任务
  MAX_MERCENARY_MEMBER = 2; //最大佣兵成员数
  SET_EXP = 5001; //设置经验
  SET_EXPMAX = 5002; //设置最大经验
  SET_EXP2 = 5003; //设置二级经验
  SET_EXPMAX2 = 5004; //设置二级最大经验
  MAX_MOSTER_HP = 5000000; //怪物最大血量
  MAX_BASE_ATTRIBUTE = 2147483647; //基础属性最大值
  MAX_OTHER_ATTRIBUTE = 1000000; //其他属性最大值
  MAX_DEF = 2147483647; //最大防御
  MAX_PROBABILITY = 100; //最大概率
  IGNORE_BACK = 200; //忽视物理反伤
  IGNORE_MAGIC_BACK = 201; //忽视魔法反伤
  IGNORE_BLOCK = 202; //忽视格挡
  IGNORE_INSIGHT = 203; //忽视洞察
  IGNORE_WIL = 204; //忽视意志
  IGNORE_TOUCH = 205; //无视伤害减免
  IGNORE_CRITICAL = 206; //忽视暴击
  DEF_STR_NEARBY = 220; //近战劈砍防御
  DEF_STR_RANGE = 221; //远程劈砍防御力
  DEF_AGI_RANGE = 222; //远程穿刺防御
  DEF_AGI_NEARBY = 223; //近战穿刺防御
  ATK_STR_RANGE = 230; //远程劈砍攻击
  ATK_STR_NEARBY = 231; //近战劈砍攻击
  ATK_AGI_RANGE = 232; //远程穿刺攻击
  ATK_AGI_NEARBY = 233; //近战穿刺攻击
  MIN_HIT_RATE = 30; //最小命中率
  MIN_HIT_MAGIC = 20; //最小魔法命中
  MIN_FORCE_HITRATE = 30; //最小强制命中率
  MAX_FORCE_RATE = 70; //最大强制率
  MIN_HIT_TIME = 1; //最小命中次数
  MAX_HIT_TIME = 99; //最大命中次数
  MAX_ATK = 99999999; //最大攻击
  MAX_KEEPOUT_ATK_TIME = 100; //最大免伤次数
  MAX_OTHER_PROBABILITY = 1000; //其他最大概率
  MIN_DEF_FIELD = -999999; //最小防御场
  MIN_HEAL_RECOVERY = -999999; //最小治疗恢复
  MIN_MANA_RECOVERY = -999999; //最小魔法恢复
  KEEPOUT_ATK_TIME = 250; //免伤护盾
  NEW_REFLECTION = 251; //新反击
  START_HAIR = 0; //发型起始ID
  START_FACE = 1000; //脸型起始ID
  START_HAND = 2000; //手部起始ID
  START_FEET = 3000; //脚部起始ID
  START_SHOULDER = 4000; //肩部起始ID
  START_BACK = 5000; //背部起始ID
  START_CLOTHES = 6000; //衣服起始ID
  START_TROUSERS = 7000; //裤子起始ID
  START_WEAPON = 8000; //武器起始ID
  START_HELMET = 10000; //头盔起始ID
  START_PET = 12000; //宠物起始ID
  START_TRANSPORT = 14000; //坐骑起始ID
  START_FLASH = 15000; //闪光起始ID
  START_MINI_SPRITE = 16000; //迷你精灵起始ID
  START_WEAPON_ADD1 = 30000; //武器附加1起始ID
  START_WEAPON_ADD2 = 32000; //武器附加2起始ID
  BIT_1 = 1; //1位掩码
  BIT_2 = 3; //2位掩码
  BIT_3 = 7; //3位掩码
  BIT_4 = 15; //4位掩码
  BIT_5 = 31; //5位掩码
  BIT_6 = 63; //6位掩码
  BIT_7 = 127; //7位掩码
  BIT_8 = 255; //8位掩码
  OFFSET_SEX = 0; //性别偏移
  OFFSET_RACE = 1; //种族偏移
  OFFSET_JOB = 3; //职业偏移
  OFFSET_HAIR_STYLE = 7; //发型样式偏移
  OFFSET_HAIR_COLOR = 11; //发色偏移
  OFFSET_FACE_STYLE = 13; //脸型样式偏移
  OFFSET_HAND_STYLE = 17; //手部样式偏移
  OFFSET_HAND_COLOR = 19; //手部颜色偏移
  OFFSET_FEET_STYLE = 21; //脚部样式偏移
  OFFSET_FEET_COLOR = 23; //脚部颜色偏移
  OFFSET_HELMET_STYLE = 25; //头盔样式偏移
  OFFSET_HELMET_COLOR = 30; //头盔颜色偏移
  OFFSET_HAIR_ADD = 0; //发型附加偏移
  OFFSET_FACE_ADD = 3; //脸型附加偏移
  OFFSET_HAND_ADD = 4; //手部附加偏移
  OFFSET_FEET_ADD = 8; //脚部附加偏移
  OFFSET_HELMET_ADD = 12; //头盔附加偏移
  OFFSET_SHOULDER_STYLE = 0; //肩部样式偏移
  OFFSET_SHOULDER_COLOR = 4; //肩部颜色偏移
  OFFSET_LWEAPON_STYLE = 6; //左手武器样式偏移
  OFFSET_LWEAPON_COLOR = 14; //左手武器颜色偏移
  OFFSET_RWEAPON_STYLE = 16; //右手武器样式偏移
  OFFSET_RWEAPON_COLOR = 24; //右手武器颜色偏移
  OFFSET_VIP = 26; //VIP偏移
  OFFSET_WEAPON_FLASH = 28; //武器闪光偏移
  OFFSET_SHOULDER_ADD = 31; //肩部附加偏移
  OFFSET_LWEAPON_ADD = 0; //左手武器附加偏移
  OFFSET_RWEAPON_ADD = 2; //右手武器附加偏移
  OFFSET_SHOULDER_ADD_2 = 4; //肩部附加2偏移
  OFFSET_BACK_STYLE = 0; //背部样式偏移
  OFFSET_BACK_COLOR = 4; //背部颜色偏移
  OFFSET_CLOTHES_STYLE = 6; //衣服样式偏移
  OFFSET_CLOTHES_COLOR = 12; //衣服颜色偏移
  OFFSET_TROUSERS_STYLE = 14; //裤子样式偏移
  OFFSET_TROUSERS_COLOR = 20; //裤子颜色偏移
  OFFSET_TRANSPORT_STYLE = 22; //坐骑样式偏移
  OFFSET_TRANSPORT_COLOR = 26; //坐骑颜色偏移
  OFFSET_BACK_ADD = 28; //背部附加偏移
  OFFSET_TRANSPORT_ADD = 29; //坐骑附加偏移
  OFFSET_FASH_ADD = 30; //时装附加偏移
  OFFSET_TRANSPORT_ADD_2 = 31; //坐骑附加2偏移
  OFFSET_BACK_ADD_2 = 0; //背部附加2偏移
  OFFSET_CLOTHES_ADD = 2; //衣服附加偏移
  OFFSET_TROUSERS_ADD = 3; //裤子附加偏移
  OFFSET_TRANSPORT_ADD_3 = 4; //坐骑附加3偏移
  LEN_SEX = 1; //性别长度
  LEN_RACE = 3; //种族长度
  LEN_JOB = 15; //职业长度
  LEN_HAIR_STYLE = 15; //发型样式长度
  LEN_HAIR_COLOR = 3; //发色长度
  LEN_FACE_STYLE = 15; //脸型样式长度
  LEN_HAND_STYLE = 3; //手部样式长度
  LEN_HAND_COLOR = 3; //手部颜色长度
  LEN_FEET_STYLE = 3; //脚部样式长度
  LEN_FEET_COLOR = 3; //脚部颜色长度
  LEN_HELMET_STYLE = 31; //头盔样式长度
  LEN_HELMET_COLOR = 3; //头盔颜色长度
  LEN_HAIR_ADD = 7; //发型附加长度
  LEN_FACE_ADD = 1; //脸型附加长度
  LEN_HAND_ADD = 15; //手部附加长度
  LEN_FEET_ADD = 15; //脚部附加长度
  LEN_HELMET_ADD = 3; //头盔附加长度
  LEN_SHOULDER_STYLE = 15; //肩部样式长度
  LEN_SHOULDER_COLOR = 3; //肩部颜色长度
  LEN_LWEAPON_STYLE = 255; //左手武器样式长度
  LEN_LWEAPON_COLOR = 3; //左手武器颜色长度
  LEN_RWEAPON_STYLE = 255; //右手武器样式长度
  LEN_RWEAPON_COLOR = 3; //右手武器颜色长度
  LEN_VIP = 3; //VIP长度
  LEN_WEAPON_FLASH = 7; //武器闪光长度
  LEN_SHOULDER_ADD = 1; //肩部附加长度
  LEN_LWEAPON_ADD = 3; //左手武器附加长度
  LEN_RWEAPON_ADD = 3; //右手武器附加长度
  LEN_SHOULDER_ADD_2 = 3; //肩部附加2长度
  LEN_BACK_STYLE = 15; //背部样式长度
  LEN_BACK_COLOR = 3; //背部颜色长度
  LEN_CLOTHES_STYLE = 63; //衣服样式长度
  LEN_CLOTHES_COLOR = 3; //衣服颜色长度
  LEN_TROUSERS_STYLE = 63; //裤子样式长度
  LEN_TROUSERS_COLOR = 3; //裤子颜色长度
  LEN_TRANSPORT_STYLE = 15; //坐骑样式长度
  LEN_TRANSPORT_COLOR = 3; //坐骑颜色长度
  LEN_BACK_ADD = 1; //背部附加长度
  LEN_TRANSPORT_ADD = 1; //坐骑附加长度
  LEN_FASH_ADD = 1; //时装附加长度
  LEN_TRANSPORT_ADD_2 = 1; //坐骑附加2长度
  LEN_BACK_ADD_2 = 3; //背部附加2长度
  LEN_CLOTHES_ADD = 1; //衣服附加长度
  LEN_TROUSERS_ADD = 1; //裤子附加长度
  LEN_TRANSPORT_ADD_3 = 1; //坐骑附加3长度
  OFFSET_PET_STYLE = 0; //宠物样式偏移
  OFFSET_PET_COLOR = 6; //宠物颜色偏移
  OFFSET_LVUP_STYLE = 8; //升级样式偏移
  LEN_PET_STYLE = 63; //宠物样式长度
  LEN_PET_COLOR = 3; //宠物颜色长度
  LEN_LVUP_STYLE = 15; //升级样式长度
  MOVE_END_CHECK = 1; //移动结束检查
  LAST_MOVE_FILL = 2; //最后移动填充
  NPC_MISSION_LOAD = 4; //NPC任务加载
  MOVE_NPC_WELCOME = 8; //NPC欢迎
  MERCENARY_INFO = 16; //佣兵信息
  PET_INFO = 32; //宠物信息
  DEL_STATUS = 64; //删除状态
  MONSTER_BOOK_INFO = 128; //怪物图鉴信息
  PET_INNER_SHOW = 256; //宠物内部显示
  OFFLINE_DOING = 512; //离线操作
  NPC_MISSION_DISPLAY = 1024; //NPC任务显示
  NPC_MISSION_HIDE = 2048; //NPC任务隐藏
  NPC_COUNTRY_BLOAD = 4096; //NPC国家血统
  MAIL_NEW_NOTICE = 8192; //新邮件通知
  ATTR_NEW_NOTICE = 16384; //新属性通知
  CHAT_NEW_NOTICE = 32768; //新聊天通知
  SPRITE_LOADING_NPC = 65536; //精灵NPC加载
  SELL_PLAYER_INFO_LOAD = 131072; //出售玩家信息加载
  SELL_PLAYER_STORE_LOAD = 262144; //出售玩家商店加载
  HIDE_SPIRTE = 524288; //隐藏精灵
  HIDE_PHOTO_COMMAND = 1048576; //隐藏照片命令
  SELL_PLAYER_STORE_VIP_LOAD = 2097152; //VIP玩家商店加载
  BUFFER_DIE_1HP_CHECK = 134217728; //缓冲区死亡1HP检查
  BUFFER_DIE_FULLHP_CHECK = 268435456; //缓冲区死亡满血检查
  BUFFER_DIE_DELAY_CHECK = 536870912; //缓冲区死亡延迟检查
  TAG_IS_KEEP_OUT = 1073741824; //是否保持外部标记
  COUNTRY_APPLY = -2147483648; //国家申请
  STATUS_NONE = 0; //无状态
  STATUS_ONLINE = 1; //在线状态
  STATUS_ALL_DEL = 2; //全部删除状态
  STATUS_TEMP_DEL = 4; //临时删除状态
  STATUS_FROST = 8; //冻结状态
  STATUS_SELL = 16; //出售状态
  STATUS_ENGAGE = 32; //订婚状态
  STATUS_CLOSE = 64; //关闭状态
  STATUS_MOUTH = 128; //嘴部状态
  STATUS_COMMENTS = 256; //评论状态
  STATUS_OFFLINEMISSION = 512; //离线任务状态
  STATUS_VIP = 1024; //VIP状态
  STATUS_IS_CARD = 2048; //是否为卡片
  STATUS_CAN_MASTER = 4096; //可以成为大师
  STATUS_NEW = 8192; //新角色
  PHOTO_FLAG_VIP = 1; //VIP照片标记
  PHOTO_IS_VIP_CQ = 2; //是否为VIP CQ照片
  COUNTRY_FFLAG_IS_VIP = 1; //国家VIP标记
  COUNTRY_FFLAG_IS_VIP_CQ = 2; //国家VIP CQ标记
  COUNTRY_MEMBER_IS_VIP = 1; //国家成员是否VIP
  COUNTRY_MEMBER_IS_VIP_CQ = 2; //国家成员是否VIP CQ
  STATUS_IS_SOLDIER = 8192; //是否为士兵
  STATUS_IS_HELP = 16384; //是否为帮助
  STATUS_IS_PHOTO = 32768; //是否为照片
  STATUS_IS_GM = 65536; //是否为GM
  STATUS_IS_CITY = 131072; //是否为城市
  STATUS_IS_TEACHER = 262144; //是否为老师
  STATUS_VIP_TX_CQ = 524288; //VIP TX CQ状态
  STATUS_IS_TOURIST = 1048576; //是否为游客
  STATUS_IS_ROBOT = 2097152; //是否为机器人
  MODE_NORMAL = 0; //普通模式
  MODE_SHOP = 1; //商店模式
  MODE_BATTLE_LOCAL = 50; //本地战斗模式
  MODE_BATTLE_REMOTE = 51; //远程战斗模式
  MODE_BATTLE_PK = 52; //PK战斗模式
  MODE_BATTLE_OB = 53; //观战模式
  MODE_BATTLE_PET_PK = 54; //宠物PK模式
  NPC = 1; //NPC
  BATTLE_PLAYER = 2; //战斗玩家
  MAX_STORE_NUM = 60; //最大仓库数量
  MAX_PLAYER_HP = 9999999; //玩家最大血量
  MAX_PLAYER_MP = 9999999; //玩家最大魔法值
}

