<?xml version="1.0" encoding="UTF-8" ?>
<messages package="world/common/pbLogin">
    <message type="C2S" name="ClientCheck" module="login" explain="客户端版本检查">
        <field class="string" name="version" explain="客户端版本"/>
    </message>
    <message type="S2C" name="ClientCheck" explain="客户端版本检查">
        <field class="pbBase/Response/Response.Code" name="code" explain=""/>
        <field class="int64" name="serverTime" explain=""/>
    </message>
    <message type="C2S" name="ApplyLoginNotice" module="login" explain="登录时获取公告">
    </message>
    <message type="S2C" name="ApplyLoginNotice" explain="登录时获取公告">
        <field class="pbBase/Response/Response.Code" name="code" explain=""/>
        <field class="string" name="content" explain=""/>
    </message>
    <message type="C2S" name="Login" module="login" explain="登录">
        <field class="pbBase/LoginMethod/LoginMethod.Type" name="method" explain="登录方法"/>
        <field class="map" name="args" explain="登录参数">
            <key class="string" explain="参数名"/>
            <value class="string" explain="参数值"/>
        </field>
    </message>
    <message type="S2C" name="Login" explain="登录">
        <field class="pbBase/Response/Response.Code" name="code" explain=""/>
        <field class="pbBase/struct.LoginResult" name="result" explain=""/>
    </message>
    <message type="C2S" name="GetAreaLines" module="login" explain="获取区服信息">
    </message>
    <message type="S2C" name="GetAreaLines" explain="获取区服信息">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
        <field class="int32" name="lastId" explain="上一次选择的区服id"/>
        <array class="pbBase/struct.AreaLine" name="list" explain="区服数据"/>
    </message>
    <message type="S2C" name="Error" explain="告知客户端弹出错误">
        <field class="pbBase/Response/Response.Code" name="code" explain="码"/>
    </message>
    <message type="C2S" name="SelectArea" module="login" explain="选区">
        <field class="int32" name="id" explain="区服id"/>
    </message>
    <message type="S2C" name="SelectArea" explain="选区">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
    </message>
    <message type="S2C" name="SyncServerTime" explain="同步服务器时间">
        <field class="int64" name="time" explain="时间戳"/>
    </message>
</messages>