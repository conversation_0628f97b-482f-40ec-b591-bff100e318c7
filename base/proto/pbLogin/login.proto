syntax = "proto3";

package proto;

option go_package = "world/common/pbLogin;pbLogin";

import "pbBase/Response/Response.proto";

import "pbBase/LoginMethod/LoginMethod.proto";

import "pbBase/struct.proto";

//After are messages.
message C2S_ClientCheckMessage {
  string version = 1; //客户端版本
}
message S2C_ClientCheckMessage {
  proto.Response.Code code = 1; //
  int64 serverTime = 2; //
}
message C2S_ApplyLoginNoticeMessage {
}
message S2C_ApplyLoginNoticeMessage {
  proto.Response.Code code = 1; //
  string content = 2; //
}
message C2S_LoginMessage {
  proto.LoginMethod.Type method = 1; //登录方法
  map<string,string> args = 2; //登录参数
}
message S2C_LoginMessage {
  proto.Response.Code code = 1; //
  LoginResult result = 2; //
}
message C2S_GetAreaLinesMessage {
}
message S2C_GetAreaLinesMessage {
  proto.Response.Code code = 1; //响应码
  int32 lastId = 2; //上一次选择的区服id
  repeated AreaLine list = 3; //区服数据
}
message S2C_ErrorMessage {
  proto.Response.Code code = 1; //码
}
message C2S_SelectAreaMessage {
  int32 id = 1; //区服id
}
message S2C_SelectAreaMessage {
  proto.Response.Code code = 1; //响应码
}
message S2C_SyncServerTimeMessage {
  int64 time = 1; //时间戳
}
