<?xml version="1.0" encoding="UTF-8" ?>
<messages package="world/common/pbGame">
    <message type="C2S" name="GetPlayerList" module="game" explain="拉取角色列表">
    </message>
    <message type="S2C" name="GetPlayerList" explain="拉取角色列表">
        <array class="pbBase/struct.SimplePlayerInfo" name="list" explain="角色列表"/>
        <field class="int32" name="lastRoleId" explain="上次登陆选择的角色"/>
    </message>
    <message type="C2S" name="CreateRole" module="game" explain="创建角色">
        <field class="pbBase/Sex/Sex.Type" name="sex" explain="性别"/>
        <field class="pbBase/Camp/Camp.Type" name="race" explain="阵营"/>
        <field class="pbBase/Job/Job.Type" name="job" explain="职业"/>
        <field class="int32" name="model" explain="造型"/>
        <field class="string" name="name" explain="角色名称"/>
    </message>
    <message type="S2C" name="CreateRole" explain="创建角色">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
        <field class="pbBase/struct.SimplePlayerInfo" name="role" explain="创建好的角色信息"/>
    </message>
    <message type="C2S" name="EnterGame" module="game" explain="角色登入">
        <field class="int32" name="gameId" explain="角色id"/>
    </message>
    <message type="S2C" name="EnterGame" explain="角色登入">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
        <field class="pbBase/struct.PlayerInfo" name="data" explain="角色数据"/>
    </message>
    <message type="C2S" name="GmExecute" module="game" explain="执行gm">
        <field class="string" name="cmd" explain="gm命令"/>
    </message>
    <message type="S2C" name="GmExecute" explain="执行gm">
        <field class="string" name="reply" explain=""/>
    </message>
</messages>