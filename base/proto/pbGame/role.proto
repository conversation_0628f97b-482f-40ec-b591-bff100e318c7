syntax = "proto3";

package proto;

option go_package = "world/common/pbGame;pbGame";

import "pbBase/Response/Response.proto";

//After are messages.
message C2S_ActorAttributeMessage {
  map<int32,int32> info = 1; //加点详情
}
message S2C_ActorAttributeMessage {
  proto.Response.Code code = 1; //响应码
  int32 cp = 2; //未分配属性点
  int32 hp = 3; //血
  int32 mp = 4; //蓝
  int32 str = 5; //力量
  int32 con = 6; //体质
  int32 agi = 7; //敏捷
  int32 ilt = 8; //智力
  int32 wis = 9; //感知
  int32 money1 = 10; //黄金
  int32 money2 = 11; //金叶
  int32 money3 = 12; //铜币
}
message C2S_LearnSkillByShopMessage {
  int32 shopId = 1; //技能商店id
  int32 skillId = 2; //技能id
  int32 skillLevel = 3; //技能等级
}
message S2C_LearnSkillByShopMessage {
  proto.Response.Code code = 1; //响应码
  int32 money1 = 2; //黄金
  int32 money2 = 3; //金叶
  int32 money3 = 4; //铜币
}
message C2S_AutoSkillSetMessage {
  bool isPet = 1; //是不是设置宠物
  bool isActive = 2; //是不是设置主动技能
  int32 skillId = 3; //技能id
}
message S2C_AutoSkillSetMessage {
  proto.Response.Code code = 1; //响应码
}
