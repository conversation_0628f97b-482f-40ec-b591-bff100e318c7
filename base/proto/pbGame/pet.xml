<?xml version="1.0" encoding="UTF-8" ?>
<messages package="world/common/pbGame">
    <message type="C2S" name="PetAddSkillSure" module="game" explain="宠物潜能石结果替换确认">
        <field class="int64" name="petId" explain="宠物id"/>
    </message>
    <message type="S2C" name="PetAddSkillSure" explain="宠物潜能石结果替换确认">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
    </message>
    <message type="C2S" name="PetSeal" module="game" explain="宠物封印">
        <field class="int32" name="slotPos" explain="位置"/>
        <field class="int64" name="petId" explain="宠物id"/>
        <field class="int32" name="type" explain="封印类型"/>
    </message>
    <message type="S2C" name="PetSeal" explain="宠物封印">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
        <field class="pbBase/struct.ItemData" name="book" explain="封印成功返回的技能书物品"/>
    </message>
    <message type="C2S" name="PetSkillBookLearn" module="game" explain="宠物使用技能书">
        <field class="int32" name="slotPos" explain="技能书位置"/>
        <field class="int32" name="petItemSlotPos" explain="宠物物品位置"/>
    </message>
    <message type="S2C" name="PetSkillBookLearn" explain="宠物使用技能书">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
        <field class="pbBase/struct.SkillInfo" name="skill" explain="学习后的技能"/>
    </message>
</messages>