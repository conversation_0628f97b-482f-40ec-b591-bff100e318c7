<?xml version="1.0" encoding="UTF-8" ?>
<messages package="world/common/pbGame">
    <message type="C2S" name="OnMailOpen" module="game" explain="打开邮件界面时获取部分信息">
    </message>
    <message type="S2C" name="OnMailOpen" module="game" explain="打开邮件界面时获取部分信息">
        <array class="pbBase/struct.MailSimpleNumInfo" name="simpleNumInfo" explain=""/>
    </message>
    <message type="C2S" name="SendMail" module="game" explain="发送邮件">
        <field class="string" name="content" explain="内容"/>
        <field class="int32" name="money1" explain="赠送黄金"/>
        <field class="int32" name="money3" explain="赠送铜币"/>
        <field class="int32" name="reqMoney1" explain="索取黄金"/>
        <field class="int32" name="reqMoney3" explain="索取铜币"/>
        <array class="pbBase/struct.ItemData" name="appendix" explain="附件"/>
        <field class="int32" name="toId" explain="接收者id"/>
        <field class="string" name="toName" explain="接收者名称"/>
    </message>
    <message type="S2C" name="SendMail" module="game" explain="发送邮件">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
    </message>
    <message type="C2S" name="MailList" module="game" explain="获取邮件列表">
        <field class="pbBase/MailDefine/MailDefine.MAIL_TYPE" name="type" explain="类型"/>
        <field class="int32" name="pageSize" explain="每页大小"/>
        <field class="int32" name="page" explain="页码"/>
    </message>
    <message type="S2C" name="MailList" module="game" explain="获取邮件列表">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
        <array class="pbBase/struct.Mail" name="mailList" explain="邮件列表"/>
        <field class="int32" name="totalCount" explain="总页数"/>
    </message>
    <message type="C2S" name="MailDetail" module="game" explain="获取邮件详情">
        <field class="int64" name="id" explain="邮件id"/>
        <field class="pbBase/MailDefine/MailDefine.MAIL_TYPE" name="type" explain="类型"/>
    </message>
    <message type="S2C" name="MailDetail" module="game" explain="获取邮件详情">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
        <field class="pbBase/struct.Mail" name="mail" explain="邮件详情"/>
    </message>
    <message type="C2S" name="MailAttach" module="game" explain="获取邮件附件">
        <field class="int64" name="id" explain="邮件id"/>
        <field class="pbBase/MailDefine/MailDefine.MAIL_TYPE" name="type" explain="类型"/>
        <field class="int32" name="selected" explain="选择的附件id"/>
    </message>
    <message type="S2C" name="MailAttach" module="game" explain="获取邮件附件">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
        <field class="int32" name="money1" explain="黄金变动"/>
        <field class="int32" name="money3" explain="铜币变动"/>
        <array class="pbBase/struct.ItemData" name="rewards" explain="奖励"/>
    </message>
    <message type="C2S" name="MailBack" module="game" explain="拒收邮件">
        <field class="int64" name="id" explain="邮件id"/>
    </message>
    <message type="S2C" name="MailBack" module="game" explain="拒收邮件">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
    </message>
    <message type="C2S" name="MailDelete" module="game" explain="删除邮件">
        <field class="int64" name="id" explain="邮件id"/>
        <field class="pbBase/MailDefine/MailDefine.MAIL_TYPE" name="type" explain="类型"/>
    </message>
    <message type="S2C" name="MailDelete" module="game" explain="删除邮件">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
    </message>
    <message type="C2S" name="MailAttachAll" module="game" explain="全部领取">
        <field class="pbBase/MailDefine/MailDefine.MAIL_TYPE" name="type" explain="类型"/>
        <field class="int32" name="page" explain="页码"/>
        <field class="int32" name="pageSize" explain="每页大小"/>
    </message>
    <message type="S2C" name="MailAttachAll" module="game" explain="全部领取">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
        <field class="int32" name="totalCnt" explain="操作邮件数量"/>
        <field class="int32" name="money1" explain="黄金变动"/>
        <field class="int32" name="money3" explain="铜币变动"/>
        <array class="pbBase/struct.ItemData" name="rewards" explain="奖励"/>
        <field class="map" name="status" explain="状态">
            <key class="int64" explain="邮件id"/>
            <value class="pbBase/MailDefine/MailDefine.MAIL_STATUS" explain="状态"/>
        </field>
    </message>
</messages>