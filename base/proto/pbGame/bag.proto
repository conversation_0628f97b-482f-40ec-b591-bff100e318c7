syntax = "proto3";

package proto;

option go_package = "world/common/pbGame;pbGame";

import "pbBase/BagReset/BagReset.proto";

import "pbBase/Response/Response.proto";

import "pbBase/struct.proto";

import "pbBase/Bag/Bag.proto";

import "google/protobuf/any.proto";

//After are messages.
message C2S_BagResetMessage {
  proto.BagReset.Type type = 1; //整理类型
}
message S2C_BagResetMessage {
  proto.Response.Code code = 1; //响应码
  map<int32,ItemData> store = 2; //新的物品数据
}
message C2S_BagItemSellMessage {
  int32 type = 1; //操作类型,1出售,2丢弃
  repeated ItemData list = 2; //操作的物品数据列表
}
message S2C_BagItemSellMessage {
  proto.Response.Code code = 1; //物品系列操作
}
message C2S_EquipWearMessage {
  int32 slotPos = 1; //物品位置
  int32 itemId = 2; //物品id
}
message S2C_EquipWearMessage {
  proto.Response.Code code = 1; //
}
message C2S_EquipTakeOffMessage {
  int32 slotPos = 1; //物品位置
  int32 itemId = 2; //物品id
}
message S2C_EquipTakeOffMessage {
  proto.Response.Code code = 1; //
}
message C2S_PlayerBagUseMessage {
  proto.Bag.ItemUseType useType = 1; //操作类型
  int32 itemSlotPos = 2; //物品位置
  int32 itemId = 3; //物品id
  int32 useNum = 4; //使用数量
  int32 extraId = 5; //额外参数
}
message S2C_PlayerBagUseMessage {
  proto.Response.Code code = 1; //
  string responseString = 2; //响应字符串
  google.protobuf.Any any = 3; //任意扩展数据 用于响应结果
}
message C2S_BagItemBindMessage {
  ItemData item = 1; //物品数据
}
message S2C_BagItemBindMessage {
  proto.Response.Code code = 1; //响应码
}
message C2S_BagItemStarMessage {
  ItemData item = 1; //物品数据
  bool isUpgrade = 2; //是否是进阶升星
}
message S2C_BagItemStarMessage {
  proto.Response.Code code = 1; //响应码
  bool result = 2; //是否成功
}
message C2S_BagItemIdentifyMessage {
  ItemData item = 1; //物品数据
  bool isUpgrade = 2; //是否是进阶鉴定
}
message S2C_BagItemIdentifyMessage {
  proto.Response.Code code = 1; //响应码
  ItemData item = 2; //物品数据
}
message C2S_BagItemIdentifyAnswerMessage {
  int32 id = 1; //物品id
  int32 slotPos = 2; //物品位置
}
message S2C_BagItemIdentifyAnswerMessage {
  proto.Response.Code code = 1; //响应码
}
message C2S_BagItemEnchaseMessage {
  int32 itemSlotPos = 1; //物品位置
  int32 gemSlotPos = 2; //宝石位置
}
message S2C_BagItemEnchaseMessage {
  proto.Response.Code code = 1; //响应码
  bool isBroken = 2; //是否失败破损
}
message C2S_BagItemGemReplaceMessage {
  int32 itemSlotPos = 1; //物品位置
  int32 gemSlotPos = 2; //宝石位置
}
message S2C_BagItemGemReplaceMessage {
  proto.Response.Code code = 1; //响应码
}
