syntax = "proto3";

package proto;

option go_package = "world/common/pbGame;pbGame";

//After are enums.
// 攻击type定义 攻击方式
// @go-enum-no-prefix
enum AtkType{
  Str = 0; //近身劈砍
  RangeStr = 1; //远程劈砍
  Agi = 2; //近身穿刺
  RangeAgi = 3; //远程穿刺
  Magic = 4; //魔法
  Curse = 5; //诅咒
  Bless = 6; //祝福
}

// 货币定义
// @go-enum-no-prefix
enum CurrencyType{
  CurrencyTypeNone = 0; //自动添加
  Money1__1 = -1; //黄金
  Money2__2 = -2; //金叶
  Money3__3 = -3; //铜币
  Exp__4 = -4; //经验值
  Active__5 = -5; //活跃度
  TOWER__6 = -6; //爬塔货币
}

