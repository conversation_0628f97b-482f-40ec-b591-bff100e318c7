<?xml version="1.0" encoding="UTF-8" ?>
<messages package="world/common/pbGame">
    <message type="C2S" name="BattlePassTest" module="game" explain="战斗测试">
        <field class="int32" name="groupId" explain="战斗id"/>
    </message>
    <message type="S2C" name="BattlePassTest" explain="战斗测试">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
    </message>
    <message type="C2S" name="RunLocalBattle" module="game" explain="校验本地战斗">
        <field class="int32" name="seed" explain="种子 必须小于等于10000"/>
        <field class="int32" name="startHp" explain="初始生命值"/>
        <field class="int32" name="startMp" explain="初始法力值"/>
        <field class="int32" name="endHp" explain="最终生命值"/>
        <field class="int32" name="endMp" explain="最终法力值"/>
        <field class="int32" name="groupId" explain="战斗id"/>
        <field class="bool" name="hasPet" explain="是否携带宠物"/>
    </message>
    <message type="S2C" name="RunLocalBattle" explain="校验本地战斗">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
        <field class="pbBase/BattleDefine/BattleDefine.Result" name="result" explain="战斗结果"/>
    </message>
</messages>