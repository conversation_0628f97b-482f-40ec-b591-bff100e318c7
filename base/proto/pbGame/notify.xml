<?xml version="1.0" encoding="UTF-8" ?>
<messages package="world/common/pbGame">
    <message type="S2C" name="MapData" explain="主动推送，地图数据">
        <field class="int32" name="mapId" explain="地图id"/>
        <field class="pbBase/struct.Point" name="pos" explain="玩家位置"/>
        <array class="int32" name="npcList" explain="npc数据"/>
        <array class="pbBase/struct.CrossSimplePlayer" name="plrList" explain="玩家数据"/>
    </message>
    <message type="S2C" name="BagData" explain="主动推送，背包数据">
        <field class="pbBase/struct.BagData" name="bag" explain="数据列表，不包含仓库"/>
    </message>
    <message type="S2C" name="Logout" explain="使账号登出，返回登录界面">
        <field class="int32" name="code" explain="0服务器踢除，1账号在别处登录"/>
    </message>
    <message type="S2C" name="SendAward" module="game" explain="发放奖励弹窗">
        <array class="pbBase/struct.ItemData" name="ary" explain="列表"/>
    </message>
    <message type="S2C" name="NewMail" module="game" explain="收到新邮件">
    </message>
</messages>