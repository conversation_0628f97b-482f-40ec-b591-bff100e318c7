syntax = "proto3";

package proto;

option go_package = "world/common/pbGame;pbGame";

import "pbBase/Response/Response.proto";

import "pbBase/BattleDefine/BattleDefine.proto";

//After are messages.
message C2S_BattlePassTestMessage {
  int32 groupId = 1; //战斗id
}
message S2C_BattlePassTestMessage {
  proto.Response.Code code = 1; //响应码
}
message C2S_RunLocalBattleMessage {
  int32 seed = 1; //种子 必须小于等于10000
  int32 startHp = 2; //初始生命值
  int32 startMp = 3; //初始法力值
  int32 endHp = 4; //最终生命值
  int32 endMp = 5; //最终法力值
  int32 groupId = 6; //战斗id
  bool hasPet = 7; //是否携带宠物
}
message S2C_RunLocalBattleMessage {
  proto.Response.Code code = 1; //响应码
  proto.BattleDefine.Result result = 2; //战斗结果
}
