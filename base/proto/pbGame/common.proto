syntax = "proto3";

package proto;

option go_package = "world/common/pbGame;pbGame";

import "pbBase/struct.proto";

import "pbBase/Sex/Sex.proto";

import "pbBase/Camp/Camp.proto";

import "pbBase/Job/Job.proto";

import "pbBase/Response/Response.proto";

//After are messages.
message C2S_GetPlayerListMessage {
}
message S2C_GetPlayerListMessage {
  repeated SimplePlayerInfo list = 1; //角色列表
  int32 lastRoleId = 2; //上次登陆选择的角色
}
message C2S_CreateRoleMessage {
  proto.Sex.Type sex = 1; //性别
  proto.Camp.Type race = 2; //阵营
  proto.Job.Type job = 3; //职业
  int32 model = 4; //造型
  string name = 5; //角色名称
}
message S2C_CreateRoleMessage {
  proto.Response.Code code = 1; //响应码
  SimplePlayerInfo role = 2; //创建好的角色信息
}
message C2S_EnterGameMessage {
  int32 gameId = 1; //角色id
}
message S2C_EnterGameMessage {
  proto.Response.Code code = 1; //响应码
  PlayerInfo data = 2; //角色数据
}
message C2S_GmExecuteMessage {
  string cmd = 1; //gm命令
}
message S2C_GmExecuteMessage {
  string reply = 1; //
}
