<?xml version="1.0" encoding="UTF-8" ?>
<messages package="world/common/pbGame">
    <message type="C2S" name="ActorAttribute" module="game" explain="用户加点操作">
        <field class="map" name="info" explain="加点详情">
            <key class="int32" explain="属性类型"/>
            <value class="int32" explain="属性值"/>
        </field>
    </message>
    <message type="S2C" name="ActorAttribute" explain="用户加点操作">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
        <field class="int32" name="cp" explain="未分配属性点"/>
        <field class="int32" name="hp" explain="血"/>
        <field class="int32" name="mp" explain="蓝"/>
        <field class="int32" name="str" explain="力量"/>
        <field class="int32" name="con" explain="体质"/>
        <field class="int32" name="agi" explain="敏捷"/>
        <field class="int32" name="ilt" explain="智力"/>
        <field class="int32" name="wis" explain="感知"/>
        <field class="int32" name="money1" explain="黄金"/>
        <field class="int32" name="money2" explain="金叶"/>
        <field class="int32" name="money3" explain="铜币"/>
    </message>
    <message type="C2S" name="LearnSkillByShop" module="game" explain="学习技能">
        <field class="int32" name="shopId" explain="技能商店id"/>
        <field class="int32" name="skillId" explain="技能id"/>
        <field class="int32" name="skillLevel" explain="技能等级"/>
    </message>
    <message type="S2C" name="LearnSkillByShop" explain="学习技能响应">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
        <field class="int32" name="money1" explain="黄金"/>
        <field class="int32" name="money2" explain="金叶"/>
        <field class="int32" name="money3" explain="铜币"/>
    </message>
    <message type="C2S" name="AutoSkillSet" module="game" explain="自动释放的技能设置">
        <field class="bool" name="isPet" explain="是不是设置宠物"/>
        <field class="bool" name="isActive" explain="是不是设置主动技能"/>
        <field class="int32" name="skillId" explain="技能id"/>
    </message>
    <message type="S2C" name="AutoSkillSet" explain="自动释放的技能设置">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
    </message>
</messages>