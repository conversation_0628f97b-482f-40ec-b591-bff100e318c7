syntax = "proto3";

package proto;

option go_package = "world/common/pbGame;pbGame";

import "pbBase/Response/Response.proto";

import "pbBase/struct.proto";

//After are messages.
message C2S_AcceptTaskMessage {
  int32 npcId = 1; //npcId
  int32 taskId = 2; //任务id
}
message S2C_AcceptTaskMessage {
  proto.Response.Code code = 1; //
  PetData myPet = 2; //宠物可能有更新
}
message C2S_SubmitTaskMessage {
  int32 npcId = 1; //npcId
  int32 taskId = 2; //任务id
  int32 itemId = 3; //选择的物品id
}
message S2C_SubmitTaskMessage {
  proto.Response.Code code = 1; //
  PetData myPet = 2; //宠物可能有更新
}
message C2S_DeleteTaskMessage {
  int32 taskId = 1; //任务id
}
message S2C_DeleteTaskMessage {
  proto.Response.Code code = 1; //
}
