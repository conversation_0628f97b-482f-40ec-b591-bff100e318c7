package mgate

import (
	"context"
	"fmt"
	"strings"
	"time"
	"world/base/enum/NodeState"
	"world/base/enum/ServerType"
	"world/base/enum/key"
	"world/common/ecode"
	"world/common/net_helper"
	"world/common/pbCross"
	"world/common/pbLogin"
	"world/common/pb_helper"
	"world/common/router"
	ut "world/utils"

	argsutil "github.com/huyangv/vmqant/rpc/util"

	"github.com/huyangv/vmqant/conf"
	"github.com/huyangv/vmqant/gate"
	basegate "github.com/huyangv/vmqant/gate/base"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	"github.com/huyangv/vmqant/registry"
	"github.com/huyangv/vmqant/selector"
	"github.com/pkg/errors"
)

var Module = func() module.Module {
	return new(Gate)
}

type Gate struct {
	basegate.Gate //继承
}

func (g *Gate) GetType() string {
	return "gate"
}

func (g *Gate) Version() string {
	return "1.0.0"
}

func (g *Gate) OnInit(app module.App, settings *conf.ModuleSettings) {
	//注意这里一定要用 gate.Gate 而不是 module.BaseModule
	g.Gate.OnInit(g, app, settings,
		gate.Heartbeat(time.Second*30),
		//gate.BufSize(2048*20), //网络读写缓存大小
		gate.SetSessionLearner(g), //设置监听是否链接成功和断开链接
		gate.SetStorageHandler(g), //设置持久化处理器
		gate.SetRouteHandler(g),
		gate.ConcurrentTasks(10000),
	)
}

// Connect 当连接建立 并且MQTT协议握手成功
func (g *Gate) Connect(session gate.Session) {
	log.Info(session.GetIP() + " -> 建立链接(" + session.GetNetwork() + ")")
}

// DisConnect 当连接关闭	或者客户端主动发送MQTT DisConnect命令 ,这个函数中Session无法再继续后续的设置操作，只能读取部分配置内容了
func (g *Gate) DisConnect(session gate.Session) {
	uid := session.GetUserID()
	log.Info("%s -> 断开链接, %s", session.GetIP(), uid)
	if uid == "" {
		return //如果是游客的话 直接返回
	}

	// 通知游戏服
	nodeId := session.Get(key.SessionPosNodeId)

	if nodeId != "" {
		if strings.HasPrefix(nodeId, ServerType.Login) {
			log.Debug(("无需处理的状态"))
		}
		if strings.HasPrefix(nodeId, ServerType.Game) {
			if _, err := net_helper.Invoke(g, nodeId, router.S2ROnLeaveMessage, session, &pbCross.S2R_OnLeaveMessage{}); err != nil {
				log.Error("[%s] game OnLeave Error %s", uid, err.Error())
			}
		}
	}
}

func (g *Gate) Storage(session gate.Session) (err error) {
	//log.Info("需要处理对Session的持久化")
	return nil
}

func (g *Gate) Delete(session gate.Session) (err error) {
	//log.Info("需要删除Session持久化数据")
	return nil
}

func (g *Gate) Query(Userid string) ([]byte, error) {
	return nil, fmt.Errorf("no redis")
}

func (g *Gate) Heartbeat(session gate.Session) {
}

func (g *Gate) handleGateMsg(arg string, session gate.Session) (bool, interface{}, error) {
	args := strings.Split(arg, ",")
	argMap := make(map[string]string)
	for _, str := range args {
		detail := strings.Split(str, "=")
		k := detail[0]
		val := detail[1]
		argMap[k] = val
	}
	// 仅仅用于设置session客户端版本，连接后就要设置，不然后续请求无法处理
	if ver, ok := argMap["ver"]; ok {
		session.Set(key.ClientVersion, ver)
	}
	// 用于响应客户端获取服务器时间
	if _, ok := argMap["time"]; ok {
		msg := &pbLogin.S2C_SyncServerTimeMessage{Time: ut.Now()}
		_ = session.SendNR(router.S2CSyncServerTimeMessage, pb_helper.ProtoMarshalForce(msg))
	}
	return true, nil, nil
}

func (g *Gate) OnRoute(session gate.Session, topic string, msg []byte) (bool, interface{}, error) {
	var err error
	log.Info("OnRoute %s %s %s", topic, session.TraceID(), session.GetUserID())

	topics := strings.Split(topic, "/")

	if len(topics) != 3 {
		// 正确格式为:module/func/reqId"
		return true, nil, errors.Errorf("OnRoute topic format error %s %s", session.GetUserID(), topic)
	}

	targetModule := topics[0]
	msgId := topics[1]
	// reqId := cast.ToInt(topics[2])
	needReturn := true

	if targetModule == ServerType.Gate {
		return g.handleGateMsg(msgId, session)
	}

	//设置rpc参数
	var ArgsType = make([]string, 2)
	var args = make([][]byte, 2)
	args[0], err = session.Serializable()
	if err != nil {
		return true, nil, errors.Errorf("onRoute session Serializable error %s %s %s", session.GetUserID(), topic, err.Error())
	}
	ArgsType[0] = gate.RPCParamSessionType
	ArgsType[1] = argsutil.BYTES
	args[1] = msg

	var call func(retry int) (bool, interface{}, error)
	call = func(retry int) (bool, interface{}, error) {
		//获取节点
		serverSession, err := g.getServer(session, targetModule)
		if err != nil {
			return needReturn, nil, errors.Errorf("onRoute serverSession error %s %s %s", session.GetUserID(), topic, err.Error())
		}

		// 逻辑服如果转发的节点和上次的不一样，走OnPlayerLogin
		if targetModule == ServerType.Game && session.Get(key.SessionPosNodeId) != serverSession.GetID() {
			//重新设置节点id
			nodeId := serverSession.GetID()
			session.SetPush(key.SessionPosNodeId, nodeId)
			_, e := net_helper.Invoke(g, nodeId, router.S2ROnPlayerLoginMessage, session, &pbCross.S2R_OnPlayerLoginMessage{})
			if e != nil {
				session.SetPush(key.SessionPosNodeId, "")
				return needReturn, nil, e
			}
		}

		// 超时
		expired := g.App.Options().RPCExpired
		ctx, cancel := context.WithTimeout(context.TODO(), expired)
		defer cancel()
		if needReturn {
			result, e := serverSession.CallArgs(ctx, msgId, ArgsType, args)
			if e != "" {
				if retry > 0 && e == ecode.PLAYER_NOT_FOUND.String() { //重新找一个节点
					return call(retry - 1)
				}
				return needReturn, nil, errors.Errorf("onRoute call error %s %s %s", session.GetUserID(), topic, e)
			}
			return needReturn, result, nil
		}
		err = serverSession.CallNRArgs(msgId, ArgsType, args)
		if err != nil {
			return needReturn, nil, errors.Errorf("onRoute callNR error %s %s %s", session.GetUserID(), topic, err.Error())
		}
		return needReturn, nil, nil
	}

	return call(3)
}

func (g *Gate) getServer(session gate.Session, targetModule string) (s module.ServerSession, err error) {
	// 选择的逻辑区id
	sid := session.Get(key.PlayerSid)
	// 客户端版本
	clientVersion := session.Get(key.ClientVersion)
	// 绑定的游戏节点id
	gameNodeId := ""
	if targetModule == ServerType.Game {
		gameNodeId = session.Get(key.SessionPosNodeId)
	}
	return g.GetRouteServer(targetModule,
		selector.WithStrategy(func(services []*registry.Service) selector.Next {
			return func() (*registry.Node, error) {
				var nodes []*registry.Node
				for _, service := range services {
					for _, node := range service.Nodes {
						//过滤节点版本(如果节点设置了最小版本，而客户端版本小于最小版本则不能连接)
						minVersion := node.Metadata[key.MinClientVersion]
						if !ut.IsEmpty(minVersion) && clientVersion < minVersion {
							continue
						}
						//game服过滤sid
						if targetModule == ServerType.Game && sid != node.Metadata[key.NodeSid] {
							continue
						}
						// 过滤节点状态
						state := node.Metadata[key.NodeState]
						if state == NodeState.Offline {
							continue
						}
						// 节点id固定 找到了就直接返回
						if node.Id == gameNodeId {
							return node, nil
						}
						// 如果没有gameNodeId，不走准备下线的节点
						if state == NodeState.ReadyOffline {
							continue
						}
						nodes = append(nodes, node)
					}
				}
				// 这里是随机策略，可能需要修改成均衡策略
				if len(nodes) > 0 {
					index := ut.Random(0, len(nodes)-1)
					return nodes[index], nil
				}
				return nil, errors.Errorf("not found %s", clientVersion)
			}
		}))
}
