package ut

import (
	"runtime"
	"sync"
	"time"

	"github.com/huyangv/vmqant/log"
)

type TimedMutex struct {
	mu       sync.Mutex
	acquired time.Time
	timeout  time.Duration
	unique   string
	stack    string // 记录获取锁的调用栈
}

type LockPool struct {
	pool     sync.Pool
	inUse    sync.Map
	timeout  time.Duration
	capacity int
}

// 锁超时
const timeout = time.Second * 3

// 初始容量
const initialCapacity = 128

var lp = &LockPool{
	pool: sync.Pool{
		New: func() interface{} {
			return &TimedMutex{timeout: timeout}
		},
	},
	timeout:  timeout,
	capacity: initialCapacity,
}

func Init() {
	for i := 0; i < initialCapacity; i++ {
		lp.pool.Put(&TimedMutex{timeout: timeout})
	}
	lp.startTimeoutChecker()
}

func (lp *LockPool) startTimeoutChecker() {
	go func() {
		ticker := time.NewTicker(lp.timeout / 2)
		defer ticker.Stop()
		for range ticker.C {
			now := time.Now()
			lp.inUse.Range(func(key, value interface{}) bool {
				mutex := value.(*TimedMutex)
				if now.Sub(mutex.acquired) >= lp.timeout {
					log.Error("锁超时警告: 键 %s 持有锁超过 %v", mutex.unique, lp.timeout)
				}
				return true
			})
		}
	}()
}

func LPLock(unique string) *TimedMutex {
	var mutex *TimedMutex
	if existingMutex, exists := lp.inUse.Load(unique); exists {
		mutex = existingMutex.(*TimedMutex)
	} else {
		mutex = lp.pool.Get().(*TimedMutex)
	}
	mutex.unique = unique
	buf := make([]byte, 4096)
	n := runtime.Stack(buf, false)
	mutex.stack = string(buf[:n])
	mutex.mu.Lock()
	mutex.acquired = time.Now()
	// 记录使用中的锁
	lp.inUse.Store(unique, mutex)
	return mutex
}

func LPUnlock(mutex *TimedMutex) {
	if mutex == nil {
		return
	}
	// 解锁
	mutex.mu.Unlock()
	// 从使用中移除
	lp.inUse.Delete(mutex.unique)
	// 重置并放回池中
	mutex.acquired = time.Time{}
	mutex.unique = ""
	lp.pool.Put(mutex)
}

func LPUnlockByUnique(unique string) {
	if mutex, exists := lp.inUse.Load(unique); exists {
		LPUnlock(mutex.(*TimedMutex))
	}
}
