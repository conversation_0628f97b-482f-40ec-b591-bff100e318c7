package chanCenter

import "github.com/sasha-s/go-deadlock"

type ChanItem struct {
	req chan interface{} //请求
	res chan interface{} //响应
	cb  func(msg interface{}, res chan<- interface{})
}

func (this *ChanItem) do() {
	for {
		select {
		case msg := <-this.req:
			this.cb(msg, this.res)
		}
	}
}

var events = map[int][]*ChanItem{}
var lock = new(deadlock.RWMutex)

// 监听 主要不要重复监听
func On(key int, cb func(msg interface{}, res chan<- interface{})) {
	lock.Lock()
	arr := events[key]
	if arr == nil {
		arr = []*ChanItem{}
	}
	item := &ChanItem{
		req: make(chan interface{}),
		res: make(chan interface{}),
		cb:  cb,
	}
	events[key] = append(arr, item)
	lock.Unlock()
	go item.do()
}

// 请求数据
func Req(key int, msg interface{}) interface{} {
	arr := events[key]
	if arr == nil || len(arr) == 0 {
		return nil
	}
	item := arr[0]
	item.req <- msg
	return <-item.res
}

// 发送数据
func Emit(key int, msg interface{}) {
	arr := events[key]
	if arr == nil || len(arr) == 0 {
		return
	}
	for _, item := range arr {
		item.req <- msg
	}
}
