// Package serializer 提供基于marshal标签的序列化代理工具
//
// 这个包实现了一个序列化"代理人"，能够处理包含私有字段的结构体，
// 通过动态生成包装结构体的方式，将私有字段映射为公有字段，
// 然后调用标准库的bson/json进行序列化。
//
// 支持的marshal标签格式：
//   - `marshal:"-"` : 跳过序列化
//   - `marshal:"name"` : 使用指定名称
//   - `marshal:",omitempty"` : 使用小写驼峰命名，忽略零值
//   - `marshal:"name,omitempty"` : 使用指定名称，忽略零值
//
// 使用示例：
//
//	type User struct {
//	    id   int    `marshal:"id,omitempty"`
//	    name string `marshal:"name"`
//	}
//
//	user := User{id: 123, name: "张三"}
//
//	// BSON序列化
//	bsonData, err := serializer.MarshalBSON(user)
//	bsonM := serializer.ToBsonM(user)
//
//	// JSON序列化
//	jsonData, err := serializer.MarshalJSON(user)
//
//	// 反序列化
//	var newUser User
//	err = serializer.UnmarshalBSON(bsonData, &newUser)
//	err = serializer.UnmarshalJSON(jsonData, &newUser)
package serializer

import (
	"fmt"
	"reflect"
	"sync"

	"go.mongodb.org/mongo-driver/bson"
)

// 版本信息
const (
	Version = "1.0.0"
)

// 导出的主要API函数，这些函数在各自的文件中已经实现

// BSON相关函数（在bson_proxy.go中实现）
// MarshalBSON 序列化为BSON字节数组
// UnmarshalBSON 从BSON字节数组反序列化
// ToBsonM 转换为bson.M

// JSON相关函数（在json_proxy.go中实现）
// MarshalJSON 序列化为JSON字节数组
// UnmarshalJSON 从JSON字节数组反序列化
// MarshalIndent 格式化序列化为JSON
// ToMap 转换为map[string]interface{}

// 便利函数

// IsZeroValue 判断值是否为零值
func IsZeroValue(v interface{}) bool {
	return globalBSONProxy.generator.tagParser.IsZeroValue(reflect.ValueOf(v))
}

// GetFieldInfo 获取结构体字段的序列化信息
func GetFieldInfo(structType interface{}) ([]*FieldInfo, error) {
	val := reflect.ValueOf(structType)

	// 处理指针
	for val.Kind() == reflect.Ptr {
		if val.IsNil() {
			return nil, fmt.Errorf("结构体不能为nil")
		}
		val = val.Elem()
	}

	if val.Kind() != reflect.Struct {
		return nil, fmt.Errorf("只支持结构体类型")
	}

	return globalBSONProxy.generator.tagParser.ParseStructFields(val.Type(), val), nil
}

// ClearCache 清空类型缓存
func ClearCache() {
	globalBSONProxy.generator.cache = sync.Map{}
	globalJSONProxy.generator.cache = sync.Map{}
}

// SetCacheEnabled 设置是否启用缓存（默认启用）
func SetCacheEnabled(enabled bool) {
	if !enabled {
		ClearCache()
	}
	// 注意：这里只是清空缓存，实际的缓存控制需要在生成器中实现
}

// 类型别名，方便使用
type (
	// M 是 bson.M 的别名
	M = bson.M
	// D 是 bson.D 的别名
	D = bson.D
	// A 是 bson.A 的别名
	A = bson.A
)

// 常用的零值常量
var (
	// EmptyString 空字符串
	EmptyString = ""
	// EmptySlice 空切片（注意：这不是零值，nil才是零值）
	EmptySlice = []interface{}{}
	// EmptyMap 空映射
	EmptyMap = map[string]interface{}{}
)

// 错误类型定义
type SerializerError struct {
	Op  string // 操作名称
	Err error  // 原始错误
}

func (e *SerializerError) Error() string {
	return fmt.Sprintf("serializer %s: %v", e.Op, e.Err)
}

func (e *SerializerError) Unwrap() error {
	return e.Err
}

// 创建序列化错误
func newSerializerError(op string, err error) *SerializerError {
	return &SerializerError{Op: op, Err: err}
}

// 辅助函数

// MustMarshalBSON 序列化为BSON，如果出错则panic
func MustMarshalBSON(v interface{}) []byte {
	data, err := MarshalBSON(v)
	if err != nil {
		panic(err)
	}
	return data
}

// MustMarshalJSON 序列化为JSON，如果出错则panic
func MustMarshalJSON(v interface{}) []byte {
	data, err := MarshalJSON(v)
	if err != nil {
		panic(err)
	}
	return data
}

// MustToBsonM 转换为bson.M，如果出错则返回空的bson.M
func MustToBsonM(v interface{}) bson.M {
	return ToBsonM(v)
}

// MustToMap 转换为map，如果出错则返回空的map
func MustToMap(v interface{}) map[string]interface{} {
	return ToMap(v)
}
