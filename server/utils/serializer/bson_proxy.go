package serializer

import (
	"fmt"
	"reflect"

	"go.mongodb.org/mongo-driver/bson"
)

// BSONProxy BSON序列化代理
type BSONProxy struct {
	generator *WrapperGenerator
}

// NewBSONProxy 创建BSON代理
func NewBSONProxy() *BSONProxy {
	return &BSONProxy{
		generator: NewWrapperGenerator(),
	}
}

// MarshalBSON 序列化为BSON字节数组
func (p *BSONProxy) MarshalBSON(v interface{}) ([]byte, error) {
	if v == nil {
		return bson.Marshal(nil)
	}

	val := reflect.ValueOf(v)

	// 处理指针
	for val.Kind() == reflect.Ptr {
		if val.IsNil() {
			return bson.Marshal(nil)
		}
		val = val.Elem()
	}

	// 如果不是结构体，检查是否是包含结构体的切片
	if val.Kind() != reflect.Struct {
		if val.Kind() == reflect.Slice || val.Kind() == reflect.Array {
			// 检查元素类型是否需要处理
			elemType := val.Type().Elem()
			for elemType.Kind() == reflect.Ptr {
				elemType = elemType.Elem()
			}

			if elemType.Kind() == reflect.Struct {
				// 处理结构体切片
				return p.marshalStructSlice(val)
			}
		}
		return bson.Marshal(v)
	}

	// 确保值是可寻址的
	if !val.CanAddr() {
		// 创建一个可寻址的副本
		newVal := reflect.New(val.Type()).Elem()
		newVal.Set(val)
		val = newVal
	}

	// 生成包装结构体
	wrapperInfo, err := p.generator.GenerateWrapper(val.Type())
	if err != nil {
		return nil, fmt.Errorf("生成包装结构体失败: %v", err)
	}

	// 创建包装实例
	wrapper, err := p.generator.CreateWrapperInstance(val, wrapperInfo)
	if err != nil {
		return nil, fmt.Errorf("创建包装实例失败: %v", err)
	}

	// 使用标准库序列化包装结构体
	return bson.Marshal(wrapper.Interface())
}

// UnmarshalBSON 从BSON字节数组反序列化
func (p *BSONProxy) UnmarshalBSON(data []byte, v interface{}) error {
	if v == nil {
		return fmt.Errorf("目标对象不能为nil")
	}

	val := reflect.ValueOf(v)
	if val.Kind() != reflect.Ptr {
		return fmt.Errorf("目标对象必须是指针")
	}

	elem := val.Elem()

	// 如果不是结构体，检查是否是包含结构体的切片
	if elem.Kind() != reflect.Struct {
		if elem.Kind() == reflect.Slice || elem.Kind() == reflect.Array {
			// 检查元素类型是否需要处理
			elemType := elem.Type().Elem()
			for elemType.Kind() == reflect.Ptr {
				elemType = elemType.Elem()
			}

			if elemType.Kind() == reflect.Struct {
				// 处理结构体切片
				return p.unmarshalStructSlice(data, elem)
			}
		}
		return bson.Unmarshal(data, v)
	}

	// 生成包装结构体
	wrapperInfo, err := p.generator.GenerateWrapper(elem.Type())
	if err != nil {
		return fmt.Errorf("生成包装结构体失败: %v", err)
	}

	// 创建包装结构体实例
	wrapperPtr := reflect.New(wrapperInfo.WrapperType)
	wrapper := wrapperPtr.Elem()

	// 使用标准库反序列化到包装结构体
	if err := bson.Unmarshal(data, wrapperPtr.Interface()); err != nil {
		return fmt.Errorf("BSON反序列化失败: %v", err)
	}

	// 从包装结构体复制数据到原始结构体
	if err := p.generator.CopyFromWrapper(wrapper, elem, wrapperInfo); err != nil {
		return fmt.Errorf("复制数据失败: %v", err)
	}

	return nil
}

// ToBsonM 将结构体转换为bson.M
func (p *BSONProxy) ToBsonM(v interface{}) bson.M {
	if v == nil {
		return bson.M{}
	}

	val := reflect.ValueOf(v)

	// 处理指针
	for val.Kind() == reflect.Ptr {
		if val.IsNil() {
			return bson.M{}
		}
		val = val.Elem()
	}

	// 如果不是结构体，尝试直接转换
	if val.Kind() != reflect.Struct {
		// 先序列化再反序列化为bson.M
		data, err := bson.Marshal(v)
		if err != nil {
			return bson.M{}
		}

		var result bson.M
		if err := bson.Unmarshal(data, &result); err != nil {
			return bson.M{}
		}
		return result
	}

	// 确保值是可寻址的
	if !val.CanAddr() {
		// 创建一个可寻址的副本
		newVal := reflect.New(val.Type()).Elem()
		newVal.Set(val)
		val = newVal
	}

	// 生成包装结构体
	wrapperInfo, err := p.generator.GenerateWrapper(val.Type())
	if err != nil {
		return bson.M{}
	}

	// 创建包装实例
	wrapper, err := p.generator.CreateWrapperInstance(val, wrapperInfo)
	if err != nil {
		return bson.M{}
	}

	// 序列化包装结构体为BSON
	data, err := bson.Marshal(wrapper.Interface())
	if err != nil {
		return bson.M{}
	}

	// 反序列化为bson.M
	var result bson.M
	if err := bson.Unmarshal(data, &result); err != nil {
		return bson.M{}
	}

	return result
}

// MarshalBSONValue 序列化为BSON值（用于嵌套结构体）
func (p *BSONProxy) MarshalBSONValue(v interface{}) (bson.RawValue, error) {
	data, err := p.MarshalBSON(v)
	if err != nil {
		return bson.RawValue{}, err
	}

	var doc bson.D
	if err := bson.Unmarshal(data, &doc); err != nil {
		return bson.RawValue{}, err
	}

	return bson.RawValue{
		Type:  bson.TypeEmbeddedDocument,
		Value: data,
	}, nil
}

// 全局BSON代理实例
var globalBSONProxy = NewBSONProxy()

// MarshalBSON 全局BSON序列化函数
func MarshalBSON(v interface{}) ([]byte, error) {
	return globalBSONProxy.MarshalBSON(v)
}

// UnmarshalBSON 全局BSON反序列化函数
func UnmarshalBSON(data []byte, v interface{}) error {
	return globalBSONProxy.UnmarshalBSON(data, v)
}

// marshalStructSlice 序列化结构体切片
func (p *BSONProxy) marshalStructSlice(val reflect.Value) ([]byte, error) {
	if val.IsNil() {
		return bson.Marshal(nil)
	}

	// 获取元素类型
	elemType := val.Type().Elem()
	isPtr := elemType.Kind() == reflect.Ptr
	if isPtr {
		elemType = elemType.Elem()
	}

	// 生成包装结构体信息
	wrapperInfo, err := p.generator.GenerateWrapper(elemType)
	if err != nil {
		return nil, fmt.Errorf("生成包装结构体失败: %v", err)
	}

	// 创建包装切片类型
	var wrapperSliceType reflect.Type
	if isPtr {
		wrapperSliceType = reflect.SliceOf(reflect.PtrTo(wrapperInfo.WrapperType))
	} else {
		wrapperSliceType = reflect.SliceOf(wrapperInfo.WrapperType)
	}

	// 创建包装切片
	wrapperSlice := reflect.MakeSlice(wrapperSliceType, val.Len(), val.Cap())

	for i := 0; i < val.Len(); i++ {
		elem := val.Index(i)

		if isPtr {
			if elem.IsNil() {
				wrapperSlice.Index(i).Set(reflect.Zero(wrapperSliceType.Elem()))
				continue
			}

			// 获取指针指向的值
			elemValue := elem.Elem()

			// 确保值是可寻址的
			if !elemValue.CanAddr() {
				newElem := reflect.New(elemValue.Type()).Elem()
				newElem.Set(elemValue)
				elemValue = newElem
			}

			// 创建包装实例
			wrapper, err := p.generator.CreateWrapperInstance(elemValue, wrapperInfo)
			if err != nil {
				return nil, fmt.Errorf("创建包装实例失败: %v", err)
			}

			// 创建包装指针
			wrapperPtr := reflect.New(wrapperInfo.WrapperType)
			wrapperPtr.Elem().Set(wrapper)
			wrapperSlice.Index(i).Set(wrapperPtr)
		} else {
			// 确保值是可寻址的
			if !elem.CanAddr() {
				newElem := reflect.New(elem.Type()).Elem()
				newElem.Set(elem)
				elem = newElem
			}

			// 创建包装实例
			wrapper, err := p.generator.CreateWrapperInstance(elem, wrapperInfo)
			if err != nil {
				return nil, fmt.Errorf("创建包装实例失败: %v", err)
			}

			wrapperSlice.Index(i).Set(wrapper)
		}
	}

	return bson.Marshal(wrapperSlice.Interface())
}

// unmarshalStructSlice 反序列化结构体切片
func (p *BSONProxy) unmarshalStructSlice(data []byte, target reflect.Value) error {
	// 获取元素类型
	elemType := target.Type().Elem()
	isPtr := elemType.Kind() == reflect.Ptr
	if isPtr {
		elemType = elemType.Elem()
	}

	// 生成包装结构体信息
	wrapperInfo, err := p.generator.GenerateWrapper(elemType)
	if err != nil {
		return fmt.Errorf("生成包装结构体失败: %v", err)
	}

	// 创建包装切片类型
	var wrapperSliceType reflect.Type
	if isPtr {
		wrapperSliceType = reflect.SliceOf(reflect.PtrTo(wrapperInfo.WrapperType))
	} else {
		wrapperSliceType = reflect.SliceOf(wrapperInfo.WrapperType)
	}

	// 创建包装切片实例
	wrapperSlicePtr := reflect.New(wrapperSliceType)

	// 反序列化到包装切片
	if err := bson.Unmarshal(data, wrapperSlicePtr.Interface()); err != nil {
		return fmt.Errorf("BSON反序列化失败: %v", err)
	}

	wrapperSlice := wrapperSlicePtr.Elem()

	// 创建目标切片
	targetSlice := reflect.MakeSlice(target.Type(), wrapperSlice.Len(), wrapperSlice.Cap())

	// 转换每个元素
	for i := 0; i < wrapperSlice.Len(); i++ {
		wrapperElem := wrapperSlice.Index(i)

		if isPtr {
			if wrapperElem.IsNil() {
				targetSlice.Index(i).Set(reflect.Zero(target.Type().Elem()))
				continue
			}

			// 创建原始结构体实例
			originalPtr := reflect.New(elemType)
			original := originalPtr.Elem()

			// 从包装结构体复制数据
			if err := p.generator.CopyFromWrapper(wrapperElem.Elem(), original, wrapperInfo); err != nil {
				return fmt.Errorf("复制数据失败: %v", err)
			}

			targetSlice.Index(i).Set(originalPtr)
		} else {
			// 创建原始结构体实例
			original := reflect.New(elemType).Elem()

			// 从包装结构体复制数据
			if err := p.generator.CopyFromWrapper(wrapperElem, original, wrapperInfo); err != nil {
				return fmt.Errorf("复制数据失败: %v", err)
			}

			targetSlice.Index(i).Set(original)
		}
	}

	// 设置目标切片
	target.Set(targetSlice)
	return nil
}

// ToBsonM 全局转换为bson.M函数
func ToBsonM(v interface{}) bson.M {
	return globalBSONProxy.ToBsonM(v)
}
