package serializer

import (
	"encoding/json"
	"fmt"
	"reflect"
)

// JSONProxy JSON序列化代理
type JSONProxy struct {
	generator *WrapperGenerator
}

// NewJSONProxy 创建JSON代理
func NewJSONProxy() *JSONProxy {
	return &JSONProxy{
		generator: NewWrapperGenerator(),
	}
}

// MarshalJSON 序列化为JSON字节数组
func (p *JSONProxy) MarshalJSON(v interface{}) ([]byte, error) {
	if v == nil {
		return json.Marshal(nil)
	}

	val := reflect.ValueOf(v)

	// 处理指针
	for val.Kind() == reflect.Ptr {
		if val.IsNil() {
			return json.Marshal(nil)
		}
		val = val.Elem()
	}

	// 如果不是结构体，直接使用标准库
	if val.Kind() != reflect.Struct {
		return json.Marshal(v)
	}

	// 确保值是可寻址的
	if !val.CanAddr() {
		// 创建一个可寻址的副本
		newVal := reflect.New(val.Type()).Elem()
		newVal.Set(val)
		val = newVal
	}

	// 生成包装结构体
	wrapperInfo, err := p.generator.GenerateWrapper(val.Type())
	if err != nil {
		return nil, fmt.Errorf("生成包装结构体失败: %v", err)
	}

	// 创建包装实例
	wrapper, err := p.generator.CreateWrapperInstance(val, wrapperInfo)
	if err != nil {
		return nil, fmt.Errorf("创建包装实例失败: %v", err)
	}

	// 使用标准库序列化包装结构体
	return json.Marshal(wrapper.Interface())
}

// UnmarshalJSON 从JSON字节数组反序列化
func (p *JSONProxy) UnmarshalJSON(data []byte, v interface{}) error {
	if v == nil {
		return fmt.Errorf("目标对象不能为nil")
	}

	val := reflect.ValueOf(v)
	if val.Kind() != reflect.Ptr {
		return fmt.Errorf("目标对象必须是指针")
	}

	elem := val.Elem()

	// 如果不是结构体，直接使用标准库
	if elem.Kind() != reflect.Struct {
		return json.Unmarshal(data, v)
	}

	// 生成包装结构体
	wrapperInfo, err := p.generator.GenerateWrapper(elem.Type())
	if err != nil {
		return fmt.Errorf("生成包装结构体失败: %v", err)
	}

	// 创建包装结构体实例
	wrapperPtr := reflect.New(wrapperInfo.WrapperType)
	wrapper := wrapperPtr.Elem()

	// 使用标准库反序列化到包装结构体
	if err := json.Unmarshal(data, wrapperPtr.Interface()); err != nil {
		return fmt.Errorf("JSON反序列化失败: %v", err)
	}

	// 从包装结构体复制数据到原始结构体
	if err := p.generator.CopyFromWrapper(wrapper, elem, wrapperInfo); err != nil {
		return fmt.Errorf("复制数据失败: %v", err)
	}

	return nil
}

// MarshalIndent 格式化序列化为JSON
func (p *JSONProxy) MarshalIndent(v interface{}, prefix, indent string) ([]byte, error) {
	if v == nil {
		return json.MarshalIndent(nil, prefix, indent)
	}

	val := reflect.ValueOf(v)

	// 处理指针
	for val.Kind() == reflect.Ptr {
		if val.IsNil() {
			return json.MarshalIndent(nil, prefix, indent)
		}
		val = val.Elem()
	}

	// 如果不是结构体，直接使用标准库
	if val.Kind() != reflect.Struct {
		return json.MarshalIndent(v, prefix, indent)
	}

	// 生成包装结构体
	wrapperInfo, err := p.generator.GenerateWrapper(val.Type())
	if err != nil {
		return nil, fmt.Errorf("生成包装结构体失败: %v", err)
	}

	// 创建包装实例
	wrapper, err := p.generator.CreateWrapperInstance(val, wrapperInfo)
	if err != nil {
		return nil, fmt.Errorf("创建包装实例失败: %v", err)
	}

	// 使用标准库格式化序列化包装结构体
	return json.MarshalIndent(wrapper.Interface(), prefix, indent)
}

// ToMap 将结构体转换为map[string]interface{}
func (p *JSONProxy) ToMap(v interface{}) map[string]interface{} {
	if v == nil {
		return map[string]interface{}{}
	}

	val := reflect.ValueOf(v)

	// 处理指针
	for val.Kind() == reflect.Ptr {
		if val.IsNil() {
			return map[string]interface{}{}
		}
		val = val.Elem()
	}

	// 如果不是结构体，尝试直接转换
	if val.Kind() != reflect.Struct {
		// 先序列化再反序列化为map
		data, err := json.Marshal(v)
		if err != nil {
			return map[string]interface{}{}
		}

		var result map[string]interface{}
		if err := json.Unmarshal(data, &result); err != nil {
			return map[string]interface{}{}
		}
		return result
	}

	// 生成包装结构体
	wrapperInfo, err := p.generator.GenerateWrapper(val.Type())
	if err != nil {
		return map[string]interface{}{}
	}

	// 创建包装实例
	wrapper, err := p.generator.CreateWrapperInstance(val, wrapperInfo)
	if err != nil {
		return map[string]interface{}{}
	}

	// 序列化包装结构体为JSON
	data, err := json.Marshal(wrapper.Interface())
	if err != nil {
		return map[string]interface{}{}
	}

	// 反序列化为map
	var result map[string]interface{}
	if err := json.Unmarshal(data, &result); err != nil {
		return map[string]interface{}{}
	}

	return result
}

// 全局JSON代理实例
var globalJSONProxy = NewJSONProxy()

// MarshalJSON 全局JSON序列化函数
func MarshalJSON(v interface{}) ([]byte, error) {
	return globalJSONProxy.MarshalJSON(v)
}

// UnmarshalJSON 全局JSON反序列化函数
func UnmarshalJSON(data []byte, v interface{}) error {
	return globalJSONProxy.UnmarshalJSON(data, v)
}

// MarshalIndent 全局格式化JSON序列化函数
func MarshalIndent(v interface{}, prefix, indent string) ([]byte, error) {
	return globalJSONProxy.MarshalIndent(v, prefix, indent)
}

// ToMap 全局转换为map函数
func ToMap(v interface{}) map[string]interface{} {
	return globalJSONProxy.ToMap(v)
}
