package serializer

import (
	"reflect"
	"strings"
	"unicode"
)

// FieldInfo 字段信息
type FieldInfo struct {
	Name               string   // 序列化后的字段名
	OmitEmpty          bool     // 是否忽略零值
	Inline             bool     // 是否内联嵌入
	Skip               bool     // 是否跳过序列化
	IsEmbedded         bool     // 是否是嵌入字段
	Options            []string // 所有marshal参数，用于传递给底层库
	FieldIndex         int      // 字段在结构体中的索引
	EmbeddedFieldIndex int      // 如果是内联字段，这是在嵌入结构体中的索引
	FieldType          reflect.Type
	FieldValue         reflect.Value
}

// TagParser marshal标签解析器
type TagParser struct{}

// NewTagParser 创建标签解析器
func NewTagParser() *TagParser {
	return &TagParser{}
}

// ParseMarshalTag 解析marshal标签
// 支持的格式：
// - `marshal:"-"` : 跳过序列化
// - `marshal:"name"` : 使用指定名称
// - `marshal:",omitempty"` : 使用小写驼峰命名，忽略零值
// - `marshal:"name,omitempty"` : 使用指定名称，忽略零值
func (p *TagParser) ParseMarshalTag(field reflect.StructField, fieldValue reflect.Value) *FieldInfo {
	tag := field.Tag.Get("marshal")

	// 无标签的字段不参与序列化（严格模式）
	if tag == "" {
		return &FieldInfo{
			Skip:       true,
			FieldIndex: -1,
		}
	}

	// 解析标签内容
	parts := strings.Split(tag, ",")
	name := strings.TrimSpace(parts[0])

	// 跳过序列化
	if name == "-" {
		return &FieldInfo{
			Skip:       true,
			FieldIndex: -1,
		}
	}

	// 解析所有选项
	var options []string
	omitEmpty := false
	inline := false

	for i := 1; i < len(parts); i++ {
		option := strings.TrimSpace(parts[i])
		if option != "" {
			options = append(options, option)

			if option == "omitempty" {
				omitEmpty = true
			} else if option == "inline" {
				inline = true
			}
		}
	}

	// 确定序列化名称
	if name == "" {
		// 使用小写驼峰命名
		name = p.ToLowerCamelCase(field.Name)
	}

	return &FieldInfo{
		Name:       name,
		OmitEmpty:  omitEmpty,
		Inline:     inline,
		Skip:       false,
		IsEmbedded: field.Anonymous,
		Options:    options,
		FieldIndex: -1, // 将在后续设置
		FieldType:  field.Type,
		FieldValue: fieldValue,
	}
}

// ToLowerCamelCase 将字段名转换为小写驼峰命名
// 规则：
// - UserName -> userName
// - ID -> iD
// - HTTPServer -> hTTPServer
func (p *TagParser) ToLowerCamelCase(name string) string {
	if name == "" {
		return ""
	}

	runes := []rune(name)
	if len(runes) == 0 {
		return ""
	}

	result := make([]rune, 0, len(runes))

	// 找到第一个连续大写字母序列的结束位置
	i := 0
	for i < len(runes) && unicode.IsUpper(runes[i]) {
		i++
	}

	if i == 0 {
		// 第一个字符不是大写，直接返回
		return name
	}

	if i == 1 {
		// 只有第一个字符是大写，转为小写
		result = append(result, unicode.ToLower(runes[0]))
		result = append(result, runes[1:]...)
	} else if i == len(runes) {
		// 全部都是大写字母，只转换第一个
		result = append(result, unicode.ToLower(runes[0]))
		result = append(result, runes[1:]...)
	} else {
		// 有连续大写字母后跟小写字母
		// 例如：HTTPServer -> hTTPServer
		// 将第一个字母转为小写，其余保持不变
		result = append(result, unicode.ToLower(runes[0]))
		result = append(result, runes[1:]...)
	}

	return string(result)
}

// IsZeroValue 判断值是否为零值
// 根据protobuf风格的零值定义
func (p *TagParser) IsZeroValue(v reflect.Value) bool {
	if !v.IsValid() {
		return true
	}

	switch v.Kind() {
	case reflect.Bool:
		return !v.Bool()
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return v.Int() == 0
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		return v.Uint() == 0
	case reflect.Float32, reflect.Float64:
		return v.Float() == 0
	case reflect.Complex64, reflect.Complex128:
		return v.Complex() == 0
	case reflect.String:
		return v.String() == ""
	case reflect.Ptr, reflect.Interface:
		return v.IsNil()
	case reflect.Slice:
		// 注意：空切片[]不是零值，只有nil切片才是零值
		return v.IsNil()
	case reflect.Map:
		return v.IsNil() // 只有nil map才是零值，空map不是零值
	case reflect.Array:
		// 数组的零值是所有元素都为零值
		for i := 0; i < v.Len(); i++ {
			if !p.IsZeroValue(v.Index(i)) {
				return false
			}
		}
		return true
	case reflect.Struct:
		// 结构体的零值是所有字段都为零值
		for i := 0; i < v.NumField(); i++ {
			if !p.IsZeroValue(v.Field(i)) {
				return false
			}
		}
		return true
	case reflect.Chan, reflect.Func:
		return v.IsNil()
	default:
		return false
	}
}

// ParseStructFields 解析结构体的所有字段
func (p *TagParser) ParseStructFields(structType reflect.Type, structValue reflect.Value) []*FieldInfo {
	if structType.Kind() != reflect.Struct {
		return nil
	}

	var fields []*FieldInfo

	for i := 0; i < structType.NumField(); i++ {
		field := structType.Field(i)
		fieldValue := structValue.Field(i)

		// 处理嵌入字段 - 简化版本：就像普通字段一样处理
		if field.Anonymous {
			fieldInfo := p.ParseMarshalTag(field, fieldValue)
			if fieldInfo != nil && !fieldInfo.Skip {
				fieldInfo.FieldIndex = i
				fieldInfo.IsEmbedded = true
				fields = append(fields, fieldInfo)
			}
			continue
		}

		fieldInfo := p.ParseMarshalTag(field, fieldValue)
		if fieldInfo != nil && !fieldInfo.Skip {
			fieldInfo.FieldIndex = i
			fields = append(fields, fieldInfo)
		}
	}

	return fields
}
