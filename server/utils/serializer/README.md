# 序列化代理工具 (Serializer Proxy)

一个基于Go反射和unsafe操作的序列化代理工具，支持私有字段的BSON和JSON序列化。

## 项目背景

### 核心需求
1. **私有字段序列化**：Go标准库的json/bson无法序列化私有字段（小写开头）
2. **代码风格统一**：项目使用小写字段名，但需要序列化支持
3. **性能要求**：需要高效的序列化性能，避免过多反射开销
4. **兼容性要求**：需要同时支持BSON和JSON格式，保持与标准库一致的行为

### 设计思路
通过**代理包装**的方式解决私有字段序列化问题：
1. 运行时动态生成包装结构体，将私有字段映射为公有字段
2. 使用unsafe包直接访问私有字段值，绕过Go的访问限制
3. 将marshal标签参数直接传递给底层BSON/JSON库
4. 让成熟的标准库处理复杂的序列化逻辑（如嵌套、内联等）

### 核心问题与解决方案

#### 问题1：私有字段访问限制
**问题描述**：Go语言reflect包无法直接访问私有字段进行Set/Get操作
**解决方案**：使用unsafe包绕过访问限制，直接操作内存地址获取和设置字段值

#### 问题2：嵌入结构体复杂性
**问题描述**：Go的嵌入结构体有复杂的继承语义，包括命名嵌入和内联嵌入
**解决方案**：简化处理逻辑，将嵌入字段当作普通字段处理，让BSON/JSON库自己处理inline逻辑

#### 问题3：Map的omitempty行为异常
**问题描述**：BSON库的omitempty会将空map（非nil但长度为0）忽略，反序列化后变成nil，破坏了语义
**解决方案**：对map类型不使用omitempty标签，在代码层面手动跳过nil map

#### 问题4：参数兼容性问题
**问题描述**：需要支持标准库的所有序列化参数（omitempty、inline等）
**解决方案**：直接将marshal标签的参数解析后传递给bson/json标签，实现参数透传

## 特性

- ✅ **私有字段支持**：能够序列化和反序列化包含私有字段的结构体
- ✅ **marshal标签系统**：自定义的marshal标签，支持字段命名和omitempty
- ✅ **小写驼峰命名**：自动将字段名转换为小写驼峰格式
- ✅ **零值忽略**：符合protobuf风格的omitempty行为
- ✅ **BSON/JSON双支持**：同时支持BSON和JSON序列化
- ✅ **代理模式**：基于标准库bson/json，稳定可靠
- ✅ **性能缓存**：自动缓存反射信息，提升重复序列化性能
- ✅ **类型安全**：完整的类型检查和错误处理

## 安装

```go
import "world/utils/serializer"
```

## marshal标签语法

| 标签格式 | 行为说明 | 示例 |
|---------|---------|------|
| 无标签 | 字段不参与序列化（严格模式） | `name string` |
| `marshal:"-"` | 字段不参与序列化 | `marshal:"-"` |
| `marshal:"name"` | 字段参与序列化，使用指定名称 | `marshal:"customName"` |
| `marshal:",omitempty"` | 字段参与序列化，使用小写驼峰命名，忽略零值 | `marshal:",omitempty"` |
| `marshal:"name,omitempty"` | 字段参与序列化，使用指定名称，忽略零值 | `marshal:"gameId,omitempty"` |

## 命名转换规则

- `UserName` → `userName`
- `ID` → `iD`
- `HTTPServer` → `hTTPServer`
- `XMLParser` → `xMLParser`

## 零值定义

符合protobuf风格的零值定义：
- 基本类型：`0`, `false`, `""`
- 指针类型：`nil`
- 切片：`nil`（注意：空切片`[]`不是零值）
- 映射：`nil`或空映射
- 结构体：所有字段都为零值

## 基本用法

### 定义结构体

```go
type User struct {
    id       int64  `marshal:"id,omitempty"`
    userName string `marshal:"userName"`
    nickName string `marshal:"nickName,omitempty"`
    level    int    `marshal:"level,omitempty"`
    isVIP    bool   `marshal:"isVIP,omitempty"`
    ignored  string // 无标签，不会被序列化
}
```

### BSON序列化

```go
user := User{
    id:       12345,
    userName: "player001",
    nickName: "超级玩家",
    level:    50,
    isVIP:    true,
}

// 序列化为BSON字节数组
data, err := serializer.MarshalBSON(user)
if err != nil {
    log.Fatal(err)
}

// 转换为bson.M
bsonM := serializer.ToBsonM(user)
fmt.Printf("BSON结构: %v\n", bsonM)

// 反序列化
var newUser User
err = serializer.UnmarshalBSON(data, &newUser)
if err != nil {
    log.Fatal(err)
}
```

### JSON序列化

```go
// 序列化为JSON字节数组
jsonData, err := serializer.MarshalJSON(user)
if err != nil {
    log.Fatal(err)
}

// 格式化JSON
prettyJSON, err := serializer.MarshalIndent(user, "", "  ")
if err != nil {
    log.Fatal(err)
}

// 转换为map
userMap := serializer.ToMap(user)

// 反序列化
var newUser User
err = serializer.UnmarshalJSON(jsonData, &newUser)
if err != nil {
    log.Fatal(err)
}
```

## 高级用法

### 嵌套结构体

```go
type Profile struct {
    avatar   string `marshal:"avatar,omitempty"`
    bio      string `marshal:"bio,omitempty"`
}

type User struct {
    id      int64    `marshal:"id"`
    name    string   `marshal:"name"`
    profile *Profile `marshal:"profile,omitempty"`
}
```

### 复杂数据类型

```go
type GameData struct {
    settings    map[string]interface{} `marshal:"settings,omitempty"`
    achievements []string               `marshal:"achievements,omitempty"`
    inventory   []*Item                `marshal:"inventory,omitempty"`
}
```

## API参考

### 主要函数

```go
// BSON相关
func MarshalBSON(v interface{}) ([]byte, error)
func UnmarshalBSON(data []byte, v interface{}) error
func ToBsonM(v interface{}) bson.M

// JSON相关
func MarshalJSON(v interface{}) ([]byte, error)
func UnmarshalJSON(data []byte, v interface{}) error
func MarshalIndent(v interface{}, prefix, indent string) ([]byte, error)
func ToMap(v interface{}) map[string]interface{}

// 辅助函数
func IsZeroValue(v interface{}) bool
func ClearCache()
```

### Must函数（panic版本）

```go
func MustMarshalBSON(v interface{}) []byte
func MustMarshalJSON(v interface{}) []byte
func MustToBsonM(v interface{}) bson.M
func MustToMap(v interface{}) map[string]interface{}
```

## 性能特性

- **反射缓存**：自动缓存结构体的反射信息，避免重复解析
- **零拷贝**：尽可能避免数据复制，使用unsafe指针操作
- **批量处理**：支持大量数据的高效序列化

## 注意事项

1. **私有字段访问**：使用了unsafe包来访问私有字段，在某些受限环境下可能不可用
2. **类型安全**：虽然使用了unsafe，但所有操作都经过了严格的类型检查
3. **内存安全**：所有unsafe操作都有边界检查，确保内存安全
4. **并发安全**：缓存机制使用了sync.Map，支持并发访问

## 错误处理

所有函数都返回详细的错误信息，包括：
- 结构体解析错误
- 字段访问错误
- 序列化/反序列化错误
- 类型转换错误

## 架构设计

### 核心组件
1. **TagParser**：解析marshal标签，提取字段信息和参数
2. **WrapperGenerator**：动态生成包装结构体，管理字段映射关系
3. **BSONProxy/JSONProxy**：代理序列化接口，协调整个序列化流程

### 工作流程
1. **解析阶段**：解析原始结构体的marshal标签，提取字段信息
2. **生成阶段**：动态生成包装结构体（私有字段→公有字段）
3. **转换阶段**：创建包装实例，使用unsafe复制字段值
4. **序列化阶段**：调用标准库进行实际序列化
5. **反序列化阶段**：逆向操作，从包装结构体恢复到原始结构体

### 性能优化策略
- **类型缓存**：包装结构体类型缓存，避免重复生成
- **映射缓存**：字段映射关系缓存，提升转换效率
- **Unsafe优化**：直接内存访问，避免反射Set/Get开销
- **批量操作**：批量字段复制，减少函数调用开销

## 开发历程与经验

### 主要迭代过程
1. **初版实现**：基础的私有字段序列化支持
2. **嵌套结构体支持**：添加对复杂嵌套结构的处理
3. **嵌入结构体支持**：解决Go语言嵌入结构体的复杂语义
4. **Map特殊处理**：修复空map在omitempty下的行为异常
5. **参数透传优化**：实现marshal标签参数的完整透传

### 关键技术决策
1. **选择代理模式而非代码生成**：保持运行时灵活性，避免代码膨胀
2. **依赖标准库而非重新实现**：利用成熟的BSON/JSON库，确保稳定性
3. **简化嵌入处理逻辑**：避免复杂的递归处理，让底层库处理inline语义
4. **使用unsafe而非纯反射**：在安全前提下提升性能

### 测试策略
- **单元测试**：覆盖各种数据类型和边界情况
- **集成测试**：使用真实数据库数据验证完整流程
- **性能测试**：确保序列化性能满足生产环境要求
- **对比测试**：与标准库行为对比，确保兼容性

## 故障排查指南

### 常见问题及解决方案

#### 1. 字段序列化后丢失
**症状**：某些字段在序列化后消失或变成零值
**可能原因**：
- 字段没有marshal标签（严格模式）
- omitempty导致零值被忽略
- 嵌入结构体处理异常

**排查步骤**：
1. 检查字段是否有marshal标签
2. 使用ToBsonM()查看实际序列化结构
3. 检查字段值是否为零值且使用了omitempty

#### 2. Map字段反序列化后变成nil
**症状**：空map在反序列化后变成nil，导致写入时panic
**原因**：BSON库的omitempty会忽略空map
**解决方案**：已在代码中修复，map类型不使用omitempty

#### 3. 嵌入结构体字段异常
**症状**：嵌入结构体的字段没有正确序列化或反序列化
**排查步骤**：
1. 确认嵌入字段有正确的marshal标签
2. 检查是否使用了inline参数
3. 验证嵌入结构体本身的字段标签

#### 4. 性能问题
**症状**：序列化性能不如预期
**排查步骤**：
1. 检查是否有大量重复的结构体类型解析
2. 使用ClearCache()清理缓存后重试
3. 避免过深的嵌套结构

### 调试技巧
1. **使用ToBsonM()**：查看实际的序列化结构
2. **启用详细错误**：所有函数都返回详细的错误信息
3. **对比标准库**：与标准库行为对比，确定是否为预期行为
4. **单步测试**：将复杂结构体拆分为简单结构体逐步测试

## 兼容性

- Go 1.18+
- 兼容 `go.mongodb.org/mongo-driver/bson`
- 兼容标准库 `encoding/json`

## 许可证

本项目采用MIT许可证。
