package serializer

import (
	"fmt"
	"reflect"
	"strings"
	"sync"
	"unsafe"
)

// WrapperInfo 包装结构体信息
type WrapperInfo struct {
	WrapperType   reflect.Type   // 包装结构体类型
	FieldMappings []FieldMapping // 字段映射关系
}

// FieldMapping 字段映射关系
type FieldMapping struct {
	OriginalIndex int        // 原始字段索引
	WrapperIndex  int        // 包装字段索引
	FieldInfo     *FieldInfo // 字段信息
}

// WrapperGenerator 包装结构体生成器
type WrapperGenerator struct {
	tagParser *TagParser
	cache     sync.Map // 类型缓存 map[reflect.Type]*WrapperInfo
}

// NewWrapperGenerator 创建包装结构体生成器
func NewWrapperGenerator() *WrapperGenerator {
	return &WrapperGenerator{
		tagParser: NewTagParser(),
	}
}

// GenerateWrapper 为给定类型生成包装结构体
func (g *WrapperGenerator) GenerateWrapper(originalType reflect.Type) (*WrapperInfo, error) {
	// 检查缓存
	if cached, ok := g.cache.Load(originalType); ok {
		return cached.(*WrapperInfo), nil
	}

	if originalType.Kind() != reflect.Struct {
		return nil, fmt.Errorf("只支持结构体类型，得到: %v", originalType.Kind())
	}

	// 解析原始结构体的字段
	originalValue := reflect.New(originalType).Elem()
	fields := g.tagParser.ParseStructFields(originalType, originalValue)

	if len(fields) == 0 {
		return nil, fmt.Errorf("结构体 %s 没有可序列化的字段", originalType.Name())
	}

	// 构建包装结构体的字段
	var wrapperFields []reflect.StructField
	var fieldMappings []FieldMapping

	for i, fieldInfo := range fields {
		// 简化版本：所有字段都一样处理
		originalField := originalType.Field(fieldInfo.FieldIndex)
		fieldType := g.processFieldType(originalField.Type)

		// 创建包装字段
		wrapperField := reflect.StructField{
			Name: g.capitalizeFirst(fieldInfo.Name), // 确保首字母大写
			Type: fieldType,
			Tag:  g.buildTags(fieldInfo),
		}

		wrapperFields = append(wrapperFields, wrapperField)

		// 记录映射关系
		fieldMappings = append(fieldMappings, FieldMapping{
			OriginalIndex: fieldInfo.FieldIndex,
			WrapperIndex:  i,
			FieldInfo:     fieldInfo,
		})
	}

	// 创建包装结构体类型
	wrapperType := reflect.StructOf(wrapperFields)

	wrapperInfo := &WrapperInfo{
		WrapperType:   wrapperType,
		FieldMappings: fieldMappings,
	}

	// 缓存结果
	g.cache.Store(originalType, wrapperInfo)

	return wrapperInfo, nil
}

// CreateWrapperInstance 创建包装结构体实例并复制数据
func (g *WrapperGenerator) CreateWrapperInstance(original reflect.Value, wrapperInfo *WrapperInfo) (reflect.Value, error) {
	if original.Kind() != reflect.Struct {
		return reflect.Value{}, fmt.Errorf("原始值必须是结构体")
	}

	// 创建包装结构体实例
	wrapper := reflect.New(wrapperInfo.WrapperType).Elem()

	// 复制字段数据
	for _, mapping := range wrapperInfo.FieldMappings {
		originalField := original.Field(mapping.OriginalIndex)
		wrapperField := wrapper.Field(mapping.WrapperIndex)

		// 检查是否需要忽略零值
		// 特殊处理：对于map类型，只有nil map才被忽略，空map不被忽略
		shouldSkip := false
		if mapping.FieldInfo.OmitEmpty {
			if mapping.FieldInfo.FieldType.Kind() == reflect.Map {
				// 对于map，只有nil才跳过
				shouldSkip = originalField.IsNil()
			} else {
				// 对于其他类型，使用标准的零值判断
				shouldSkip = g.tagParser.IsZeroValue(originalField)
			}
		}

		if shouldSkip {
			continue
		}

		// 获取字段值 - 简化版本：所有字段都一样处理
		fieldValue := g.getFieldValue(originalField)

		// 处理嵌套结构体的值转换
		convertedValue, err := g.convertValue(fieldValue, wrapperField.Type())
		if err != nil {
			return reflect.Value{}, fmt.Errorf("转换字段 %s 失败: %v", mapping.FieldInfo.Name, err)
		}

		// 设置到包装字段
		if wrapperField.CanSet() {
			wrapperField.Set(convertedValue)
		} else {
			return reflect.Value{}, fmt.Errorf("无法设置包装字段 %s", mapping.FieldInfo.Name)
		}
	}

	return wrapper, nil
}

// CopyFromWrapper 从包装结构体复制数据回原始结构体
func (g *WrapperGenerator) CopyFromWrapper(wrapper reflect.Value, original reflect.Value, wrapperInfo *WrapperInfo) error {
	if wrapper.Kind() != reflect.Struct || original.Kind() != reflect.Struct {
		return fmt.Errorf("wrapper和original都必须是结构体")
	}

	// 复制字段数据 - 简化版本：所有字段都一样处理
	for _, mapping := range wrapperInfo.FieldMappings {
		wrapperField := wrapper.Field(mapping.WrapperIndex)
		originalField := original.Field(mapping.OriginalIndex)

		// 反向转换值
		convertedValue, err := g.convertValueBack(wrapperField, originalField.Type())
		if err != nil {
			return fmt.Errorf("反向转换字段 %s 失败: %v", mapping.FieldInfo.Name, err)
		}

		// 设置私有字段值
		if err := g.setFieldValue(originalField, convertedValue); err != nil {
			return fmt.Errorf("设置字段 %s 失败: %v", mapping.FieldInfo.Name, err)
		}
	}

	return nil
}

// getFieldValue 获取字段值（支持私有字段）
func (g *WrapperGenerator) getFieldValue(field reflect.Value) reflect.Value {
	// 对于私有字段，我们需要使用unsafe来访问
	if field.CanAddr() {
		fieldPtr := unsafe.Pointer(field.UnsafeAddr())
		// 创建一个新的可访问的值
		accessibleValue := reflect.NewAt(field.Type(), fieldPtr).Elem()
		return accessibleValue
	}

	// 如果字段可以正常访问，直接返回
	if field.CanInterface() {
		return field
	}

	// 如果无法获取地址，返回零值
	return reflect.Zero(field.Type())
}

// setFieldValue 设置字段值（支持私有字段）
func (g *WrapperGenerator) setFieldValue(field reflect.Value, value reflect.Value) error {
	if field.CanSet() {
		// 字段可以正常设置
		field.Set(value)
		return nil
	}

	// 私有字段，使用unsafe设置
	if field.CanAddr() {
		fieldPtr := unsafe.Pointer(field.UnsafeAddr())
		newField := reflect.NewAt(field.Type(), fieldPtr).Elem()
		if newField.CanSet() {
			newField.Set(value)
			return nil
		}

		// 直接内存复制
		if value.CanAddr() {
			valuePtr := unsafe.Pointer(value.UnsafeAddr())
			size := field.Type().Size()
			copy((*[1024]byte)(fieldPtr)[:size], (*[1024]byte)(valuePtr)[:size])
			return nil
		}
	}

	return fmt.Errorf("无法设置字段值")
}

// copyFieldValue 复制字段值（支持私有字段）
func (g *WrapperGenerator) copyFieldValue(src, dst reflect.Value) error {
	if !src.IsValid() {
		return nil
	}

	// 如果源字段不可访问，使用unsafe来访问
	if !src.CanInterface() && src.CanAddr() {
		srcPtr := unsafe.Pointer(src.UnsafeAddr())
		src = reflect.NewAt(src.Type(), srcPtr).Elem()
	}

	// 如果目标字段不可设置，使用unsafe来设置
	if !dst.CanSet() {
		if dst.CanAddr() {
			dstPtr := unsafe.Pointer(dst.UnsafeAddr())
			dst = reflect.NewAt(dst.Type(), dstPtr).Elem()
		} else {
			return fmt.Errorf("无法设置字段值")
		}
	}

	// 现在应该可以正常设置了
	if dst.CanSet() {
		dst.Set(src)
		return nil
	}

	// 如果还是不能设置，尝试直接内存复制
	if src.CanAddr() && dst.CanAddr() {
		srcPtr := unsafe.Pointer(src.UnsafeAddr())
		dstPtr := unsafe.Pointer(dst.UnsafeAddr())
		size := src.Type().Size()

		// 直接内存复制
		copy((*[1024]byte)(dstPtr)[:size], (*[1024]byte)(srcPtr)[:size])
		return nil
	}

	return fmt.Errorf("无法复制字段值")
}

// capitalizeFirst 确保字符串首字母大写
func (g *WrapperGenerator) capitalizeFirst(s string) string {
	if s == "" {
		return s
	}

	runes := []rune(s)
	if len(runes) > 0 {
		if runes[0] >= 'a' && runes[0] <= 'z' {
			runes[0] = runes[0] - 'a' + 'A'
		}
	}
	return string(runes)
}

// buildTags 构建包装字段的标签
func (g *WrapperGenerator) buildTags(fieldInfo *FieldInfo) reflect.StructTag {
	var bsonTag, jsonTag string

	// 构建参数字符串
	var bsonOptions, jsonOptions []string

	// 对于map类型，不使用omitempty，因为我们希望保留空map
	// 我们在CreateWrapperInstance中已经处理了nil map的跳过
	for _, option := range fieldInfo.Options {
		if option == "omitempty" && fieldInfo.FieldType.Kind() == reflect.Map {
			// 跳过map的omitempty
			continue
		}
		bsonOptions = append(bsonOptions, option)
		jsonOptions = append(jsonOptions, option)
	}

	// 构建标签
	if len(bsonOptions) > 0 {
		bsonTag = fmt.Sprintf(`bson:"%s,%s"`, fieldInfo.Name, strings.Join(bsonOptions, ","))
		jsonTag = fmt.Sprintf(`json:"%s,%s"`, fieldInfo.Name, strings.Join(jsonOptions, ","))
	} else {
		bsonTag = fmt.Sprintf(`bson:"%s"`, fieldInfo.Name)
		jsonTag = fmt.Sprintf(`json:"%s"`, fieldInfo.Name)
	}

	return reflect.StructTag(bsonTag + " " + jsonTag)
}

// processFieldType 处理字段类型，递归处理嵌套结构体
func (g *WrapperGenerator) processFieldType(fieldType reflect.Type) reflect.Type {
	switch fieldType.Kind() {
	case reflect.Struct:
		// 如果是结构体，检查是否需要包装
		if g.needsWrapping(fieldType) {
			wrapperInfo, err := g.GenerateWrapper(fieldType)
			if err != nil {
				// 如果生成包装失败，返回原类型
				return fieldType
			}
			return wrapperInfo.WrapperType
		}
		return fieldType

	case reflect.Ptr:
		// 如果是指针，递归处理指向的类型
		elemType := g.processFieldType(fieldType.Elem())
		if elemType != fieldType.Elem() {
			// 如果元素类型发生了变化，创建新的指针类型
			return reflect.PtrTo(elemType)
		}
		return fieldType

	case reflect.Slice:
		// 如果是切片，递归处理元素类型
		elemType := g.processFieldType(fieldType.Elem())
		if elemType != fieldType.Elem() {
			// 如果元素类型发生了变化，创建新的切片类型
			return reflect.SliceOf(elemType)
		}
		return fieldType

	case reflect.Array:
		// 如果是数组，递归处理元素类型
		elemType := g.processFieldType(fieldType.Elem())
		if elemType != fieldType.Elem() {
			// 如果元素类型发生了变化，创建新的数组类型
			return reflect.ArrayOf(fieldType.Len(), elemType)
		}
		return fieldType

	case reflect.Map:
		// 如果是映射，递归处理键和值类型
		keyType := g.processFieldType(fieldType.Key())
		valueType := g.processFieldType(fieldType.Elem())
		if keyType != fieldType.Key() || valueType != fieldType.Elem() {
			// 如果键或值类型发生了变化，创建新的映射类型
			return reflect.MapOf(keyType, valueType)
		}
		return fieldType

	default:
		// 其他类型直接返回
		return fieldType
	}
}

// needsWrapping 检查结构体是否需要包装（是否有marshal标签的私有字段）
func (g *WrapperGenerator) needsWrapping(structType reflect.Type) bool {
	if structType.Kind() != reflect.Struct {
		return false
	}

	// 创建一个零值来检查字段
	zeroValue := reflect.New(structType).Elem()
	fields := g.tagParser.ParseStructFields(structType, zeroValue)

	// 如果有可序列化的字段，就需要包装
	return len(fields) > 0
}

// convertValue 转换值以匹配目标类型（处理嵌套结构体）
func (g *WrapperGenerator) convertValue(value reflect.Value, targetType reflect.Type) (reflect.Value, error) {
	if !value.IsValid() {
		return reflect.Zero(targetType), nil
	}

	sourceType := value.Type()

	// 如果类型相同，直接返回
	if sourceType == targetType {
		return value, nil
	}

	switch targetType.Kind() {
	case reflect.Struct:
		// 目标是结构体
		if sourceType.Kind() == reflect.Struct {
			// 检查目标类型是否是包装类型（包装类型的字段都有bson标签）
			isWrapperType := true
			for i := 0; i < targetType.NumField(); i++ {
				field := targetType.Field(i)
				if field.Tag.Get("bson") == "" {
					isWrapperType = false
					break
				}
			}

			if isWrapperType {
				// 目标是包装类型，需要创建包装实例
				wrapperInfo, err := g.GenerateWrapper(sourceType)
				if err != nil {
					return reflect.Value{}, err
				}
				return g.CreateWrapperInstance(value, wrapperInfo)
			} else {
				// 目标不是包装类型，直接返回原值（让BSON库处理）
				return value, nil
			}
		}

	case reflect.Ptr:
		// 目标是指针
		if value.IsNil() {
			return reflect.Zero(targetType), nil
		}

		if sourceType.Kind() == reflect.Ptr {
			// 源也是指针，递归处理指向的值
			elemValue, err := g.convertValue(value.Elem(), targetType.Elem())
			if err != nil {
				return reflect.Value{}, err
			}

			// 创建新的指针
			newPtr := reflect.New(targetType.Elem())
			newPtr.Elem().Set(elemValue)
			return newPtr, nil
		}

	case reflect.Slice:
		// 目标是切片
		if sourceType.Kind() == reflect.Slice {
			if value.IsNil() {
				return reflect.Zero(targetType), nil
			}

			// 创建新切片
			newSlice := reflect.MakeSlice(targetType, value.Len(), value.Cap())

			for i := 0; i < value.Len(); i++ {
				elemValue, err := g.convertValue(value.Index(i), targetType.Elem())
				if err != nil {
					return reflect.Value{}, err
				}
				newSlice.Index(i).Set(elemValue)
			}

			return newSlice, nil
		}

	case reflect.Array:
		// 目标是数组
		if sourceType.Kind() == reflect.Array {
			newArray := reflect.New(targetType).Elem()

			for i := 0; i < value.Len() && i < newArray.Len(); i++ {
				elemValue, err := g.convertValue(value.Index(i), targetType.Elem())
				if err != nil {
					return reflect.Value{}, err
				}
				newArray.Index(i).Set(elemValue)
			}

			return newArray, nil
		}

	case reflect.Map:
		// 目标是映射
		if sourceType.Kind() == reflect.Map {
			if value.IsNil() {
				return reflect.Zero(targetType), nil
			}

			newMap := reflect.MakeMap(targetType)

			for _, key := range value.MapKeys() {
				convertedKey, err := g.convertValue(key, targetType.Key())
				if err != nil {
					return reflect.Value{}, err
				}

				convertedValue, err := g.convertValue(value.MapIndex(key), targetType.Elem())
				if err != nil {
					return reflect.Value{}, err
				}

				newMap.SetMapIndex(convertedKey, convertedValue)
			}

			return newMap, nil
		}
	}

	// 如果无法转换，尝试直接赋值
	if value.Type().ConvertibleTo(targetType) {
		return value.Convert(targetType), nil
	}

	// 最后尝试直接返回原值
	return value, nil
}

// convertValueBack 反向转换值（从包装类型转换回原始类型）
func (g *WrapperGenerator) convertValueBack(value reflect.Value, targetType reflect.Type) (reflect.Value, error) {
	if !value.IsValid() {
		return reflect.Zero(targetType), nil
	}

	sourceType := value.Type()

	// 如果类型相同，直接返回
	if sourceType == targetType {
		return value, nil
	}

	switch targetType.Kind() {
	case reflect.Struct:
		// 目标是原始结构体，需要从包装结构体转换回来
		if sourceType.Kind() == reflect.Struct {
			// 生成原始结构体的包装信息
			wrapperInfo, err := g.GenerateWrapper(targetType)
			if err != nil {
				return reflect.Value{}, err
			}

			// 创建原始结构体实例
			original := reflect.New(targetType).Elem()

			// 从包装结构体复制数据
			err = g.CopyFromWrapper(value, original, wrapperInfo)
			if err != nil {
				return reflect.Value{}, err
			}

			return original, nil
		}

	case reflect.Ptr:
		// 目标是指针
		if value.IsNil() {
			return reflect.Zero(targetType), nil
		}

		if sourceType.Kind() == reflect.Ptr {
			// 源也是指针，递归处理指向的值
			elemValue, err := g.convertValueBack(value.Elem(), targetType.Elem())
			if err != nil {
				return reflect.Value{}, err
			}

			// 创建新的指针
			newPtr := reflect.New(targetType.Elem())
			newPtr.Elem().Set(elemValue)
			return newPtr, nil
		}

	case reflect.Slice:
		// 目标是切片
		if sourceType.Kind() == reflect.Slice {
			if value.IsNil() {
				return reflect.Zero(targetType), nil
			}

			// 创建新切片
			newSlice := reflect.MakeSlice(targetType, value.Len(), value.Cap())

			for i := 0; i < value.Len(); i++ {
				elemValue, err := g.convertValueBack(value.Index(i), targetType.Elem())
				if err != nil {
					return reflect.Value{}, err
				}
				newSlice.Index(i).Set(elemValue)
			}

			return newSlice, nil
		}

	case reflect.Array:
		// 目标是数组
		if sourceType.Kind() == reflect.Array {
			newArray := reflect.New(targetType).Elem()

			for i := 0; i < value.Len() && i < newArray.Len(); i++ {
				elemValue, err := g.convertValueBack(value.Index(i), targetType.Elem())
				if err != nil {
					return reflect.Value{}, err
				}
				newArray.Index(i).Set(elemValue)
			}

			return newArray, nil
		}

	case reflect.Map:
		// 目标是映射
		if sourceType.Kind() == reflect.Map {
			if value.IsNil() {
				return reflect.Zero(targetType), nil
			}

			newMap := reflect.MakeMap(targetType)

			for _, key := range value.MapKeys() {
				convertedKey, err := g.convertValueBack(key, targetType.Key())
				if err != nil {
					return reflect.Value{}, err
				}

				convertedValue, err := g.convertValueBack(value.MapIndex(key), targetType.Elem())
				if err != nil {
					return reflect.Value{}, err
				}

				newMap.SetMapIndex(convertedKey, convertedValue)
			}

			return newMap, nil
		}
	}

	// 如果无法转换，尝试直接赋值
	if value.Type().ConvertibleTo(targetType) {
		return value.Convert(targetType), nil
	}

	// 最后尝试直接返回原值
	return value, nil
}
