package array

import (
	"github.com/spf13/cast"
)

func Remove[T comparable](items []T, item T) []T {
	if len(items) == 0 {
		return items
	}
	for i, l := 0, len(items); i < l; i++ {
		if items[i] == item {
			return append(items[:i], items[i+1:]...)
		}
	}
	return items
}

func RemoveMap[K string | int, V comparable](items []map[K]V, key K, item V) []map[K]V {
	if len(items) == 0 {
		return items
	}
	for i, l := 0, len(items); i < l; i++ {
		if items[i][key] == item {
			return append(items[:i], items[i+1:]...)
		}
	}
	return items
}

func RemoveBy[T any](items []T, cb func(m T) bool) []T {
	if len(items) == 0 {
		return items
	}
	for i, l := 0, len(items); i < l; i++ {
		if cb(items[i]) {
			return append(items[:i], items[i+1:]...)
		}
	}
	return items
}

func RemoveIndex[T any](items []T, index int, args ...int) []T {
	size := len(items)
	if size <= index || index < 0 {
		return items
	}
	deleteCount := 1
	if len(args) >= 1 {
		deleteCount = cast.ToInt(args[0])
	}
	end := index + deleteCount
	if end > size {
		end = size
	}
	return append(items[:index], items[end:]...)
}

func Insert[T any](items []T, index int, args ...T) []T {
	if len(items) < index {
		index = len(items)
	}
	if index < 0 {
		index = 0
	}
	res := []T{}
	res = append(res, items[:index]...)
	res = append(res, args...)
	return append(res, items[index:]...)
}

func Splice[T any](items []T, args ...any) []T {
	size := len(items)
	if size <= 0 {
		return items
	}
	i := 0
	deleteCount := 1
	if len(args) >= 1 {
		i = cast.ToInt(args[0])
	}
	if len(args) >= 2 {
		deleteCount = cast.ToInt(args[1])
	}

	if i > size {
		i = size
	}
	end := i + deleteCount
	if end > size {
		end = size
	}

	res := []T{}
	res = append(res, items[:i]...)
	if len(args) >= 3 {
		for _, a := range args[2:] {
			res = append(res, a.(T))
		}
	}
	return append(res, items[end:]...)
}

func Find[T any](items []T, cb func(m T) bool) T {
	var t T
	if len(items) == 0 {
		return t
	}
	for _, it := range items {
		if cb(it) {
			return it
		}
	}
	return t
}

func FindIndex[T any](items []T, cb func(m T) bool) int {
	if len(items) == 0 {
		return -1
	}
	for i, it := range items {
		if cb(it) {
			return i
		}
	}
	return -1
}

func Some[T any](items []T, cb func(m T) bool) bool {
	if len(items) == 0 {
		return false
	}
	for _, it := range items {
		if cb(it) {
			return true
		}
	}
	return false
}

func Has[T comparable](items []T, item T) bool {
	if len(items) == 0 {
		return false
	}
	for _, it := range items {
		if it == item {
			return true
		}
	}
	return false
}

func Filter[T any](items []T, cb func(m T, i int) bool) []T {
	if len(items) == 0 {
		return items
	}
	arr := []T{}
	for i, it := range items {
		if cb(it, i) {
			arr = append(arr, it)
		}
	}
	return arr
}

func Map[T any, R any](items []T, cb func(m T, i int) R) []R {
	arr := []R{}
	for i, it := range items {
		arr = append(arr, cb(it, i))
	}
	return arr
}

func Reverse[T any](items []T) []T {
	arr := []T{}
	for i := len(items) - 1; i >= 0; i-- {
		arr = append(arr, items[i])
	}
	return arr
}

func Slice[T any](items []T, args ...int) []T {
	start := 0
	end := len(items)
	if len(args) >= 1 {
		start = args[0]
	}
	if len(args) >= 2 {
		end = args[1]
	}

	size := len(items)

	if start >= end {
		return []T{}
	}

	if start > size {
		start = size
	}

	if end > size {
		end = size
	}

	return items[start:end]
}

func SliceCopy[T any](items []T, args ...int) []T {
	slice := Slice(items, args...)
	res := make([]T, 0, len(slice))
	res = append(res, slice...)
	return res
}
