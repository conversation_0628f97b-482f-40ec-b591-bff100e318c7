package ut

type BitMap struct {
	Bits []byte `marshal:"bits,omitempty"`
	Size int    `marshal:"size,omitempty"` // 用于边界检查
}

// NewBitMap 初始化 BitMap ，指定大小
func NewBitMap(size int) *BitMap {
	return &BitMap{
		Bits: make([]byte, (size+7)/8),
		Size: size,
	}
}

// Set 设置 pos 处的为的值，将其设置为 1
func (b *BitMap) Set(pos int) {
	if pos < 0 || pos >= b.Size {
		panic("BitMap out of range.")
	}
	byteIndex := pos / 8
	bitIndex := pos % 8
	b.Bits[byteIndex] |= 1 << bitIndex
}

// Clear 清理 pos 处的位的值，将其置为 0
func (b *BitMap) Clear(pos int) {
	if pos < 0 || pos >= b.Size {
		panic("BitMap out of range.")
	}
	byteIndex := pos / 8
	bitIndex := pos % 8
	b.Bits[byteIndex] &^= 1 << bitIndex
}

// Get 获取检查位置 pos 处的位的值（如果有值则返回 true，如果无值则返回 false）
func (b *BitMap) Get(pos int) bool {
	if pos < 0 || pos >= b.Size {
		panic("BitMap out of range.")
	}
	byteIndex := pos / 8
	bitIndex := pos % 8
	return b.Bits[byteIndex]&(1<<bitIndex) != 0
}
