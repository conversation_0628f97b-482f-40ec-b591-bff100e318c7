package bit

import (
	"fmt"
	"world/common/pbBase/ModelConst"
	"world/common/pbBase/ITEM_STATUS"
)

/*
 * go 中的 >> +运算符先后顺序与 ts中不一致
 * go是先计算>> 再+
 * ts是先计算+  再>>
 */

// long数据类型高低位修正
const HIGHT = 32

type Number interface {
	int32 | int | int64 | uint32 | uint | uint64 | ModelConst.Type | ITEM_STATUS.Type
}

// ShiftLeft 左位移计算
//
// Parameters:
//   - operand T 被操作数
//   - shiftCnt T 移动位数
//
// Returns:
//   - int64
func ShiftLeft[O, T Number](operand O, shiftCnt T) int64 {
	a := int64(operand)
	b := int64(shiftCnt)
	if b < 0 {
		panic(fmt.Sprintf("ShiftLeft shiftCnt too small (%d <= 0)", b))
	}
	if b >= 64 {
		// 移位数不能过大
		panic(fmt.Sprintf("ShiftLeft shiftCnt too big (%d >= 64)", b))
	}
	return a << b
}

// ShiftRight 右位移计算
//
// Parameters:
//   - operand T 被操作数
//   - shiftCnt T 移动位数
//
// Returns:
//   - int64
func ShiftRight[O, T Number](operand O, shiftCnt T) int64 {
	a := int64(operand)
	b := int64(shiftCnt)
	if b < 0 {
		panic(fmt.Sprintf("ShiftRight shiftCnt too small (%d <= 0)", b))
	}
	if b >= 64 {
		// 移位数不能过大
		panic(fmt.Sprintf("ShiftRight shiftCnt too big (%d >= 64)", b))
	}
	return a >> b
}

// Not 按位取反运算
//
// Parameters:
//   - value T 要取反的值
//
// Returns:
//   - int64 取反后的结果
func Not[T Number](value T) int64 {
	return ^int64(value)
}

// And 按位与运算
//
// Parameters:
//   - a T 第一个操作数
//   - b T 第二个操作数
//
// Returns:
//   - int64 按位与的结果 a & b
func And[O, T Number](a O, b T) int64 {
	return int64(a) & int64(b)
}

// AndAssign 按位与赋值运算
//
// Parameters:
//   - target *int64 要修改的目标值
//   - value T 与操作数
//
// Example:
//
//	var x int64 = 5
//	bit.AndAssign(&x, 3) // 等同于 x &= 3
func AndAssign[T Number](target *int64, value T) int64 {
	*target &= int64(value)
	return *target
}

// Or 按位或运算
//
// Parameters:
//   - a T 第一个操作数
//   - b T 第二个操作数
//
// Returns:
//   - int64 按位或的结果 a | b
func Or[O, T Number](a O, b T) int64 {
	return int64(a) | int64(b)
}

// OrAssign 按位或赋值运算
//
// Parameters:
//   - target *int64 要修改的目标值
//   - value T 或操作数
//
// Example:
//
//	var x int64 = 5
//	bit.OrAssign(&x, 3) // 等同于 x |= 3
func OrAssign[T Number](target *int64, value T) int64 {
	*target |= int64(value)
	return *target
}

// SetBit 根据bool值设置或清除特定位
//
// Parameters:
//   - bol bool true设置，false清除
//   - operand O 操作数
//   - bit T bit
//
// Returns:
//   - int64
func SetBit[O Number, T Number](bol bool, operand O, bit T) int64 {
	a := int64(operand)
	b := int64(bit)
	if bol {
		return Or(a, b)
	}
	return AndAssign(&a, Not(b))
}

// IsBit检查特定位是否被设置
//
// Parameters:
//   - operand O 操作数
//   - bit T bit
//
// Returns:
//   - bool
func IsBit[O Number, T Number](operand O, bit T) bool {
	a := int64(operand)
	b := int64(bit)
	return And(a, b) != 0
}

// GetBitNum 计算一个操作数中 指定bit的位数
//
// Parameters:
//   - bit T 要计数的bit
//
// Returns:
//   - int64 指定bit的位数
func GetBitNum[T Number](bit T) int64 {
	a := int64(bit)
	count := int64(0)
	for a != 0 {
		if And(a, 1) == 1 {
			count++
		}
		a = ShiftRight(a, 1)
	}
	return count
}

// SetItemSetDataKey 将两个数值打包成一个整数
//
// Parameters:
//   - setId int 物品集ID (0-4095)
//   - extra int 类型值 (0-15)
//
// Returns:
//   - int
func SetItemSetDataKey(setId, extra int) int {
	return ((setId & 4095) << 4) | (extra & 15)
}

// GetItemSetID 从打包的数据中提取物品集ID
//
// Parameters:
//   - setData int 打包后的数据
//
// Returns:
//   - int 物品集ID (0-4095)
func GetItemSetID(setData int) int {
	return (setData >> 4) & 4095
}

// GetItemSetType 获取类型值
func GetItemSetNum(setData int) int {
	return 15 & setData
}
