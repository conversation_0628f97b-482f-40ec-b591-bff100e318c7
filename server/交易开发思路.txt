1交易状态管理
  创建一个专门的交易管理器，管理所有交易状态：


type TradeManager struct {
    trades      cmap.ConcurrentMap[string, *Trade] // tradeId -> Trade
    playerTrade cmap.ConcurrentMap[string, string] // playerId -> tradeId
    app         module.RPCModule
}

type Trade struct {
    TradeId     string
    Initiator   *TradePlayer
    Target      *TradePlayer
    Status      int // 0:请求中 1:双方确认 2:锁定 3:完成 4:取消
    CreateTime  int64
    LockTime    int64
    FinishTime  int64
}

type TradePlayer struct {
    PlayerId   string
    GameNodeId string
    Items      []*TradeItem
    Money      int64
    Confirmed  bool
    Locked     bool
}
2 交易流程设计
   初始化交易：
      检查双方是否在同一地图且距离符合要求
      分配唯一交易ID
      锁定双方玩家状态为"交易中"
   物品操作阶段：
      双方添加/移除交易物品和金币
      每次变更都需要同步到对方客户端
   确认阶段：
      双方分别确认交易内容
      双方确认后进入锁定阶段
   锁定阶段：
      锁定双方背包和金币，防止变更
      进行最终确认
   执行阶段：
      执行物品和金币转移
      通知双方交易结果
3 分布式一致性处理
   跨节点交易，使用两阶段提交模式
func (m *TradeManager) ExecuteTrade(tradeId string) error {
    trade, exists := m.trades.Get(tradeId)
    if !exists {
        return errors.New("交易不存在")
    }
    
    // 第一阶段：准备
    prepareOk1, err1 := m.prepareTrade(trade.Initiator)
    prepareOk2, err2 := m.prepareTrade(trade.Target)
    
    if !prepareOk1 || !prepareOk2 {
        // 回滚
        m.rollbackTrade(trade, err1, err2)
        return errors.New("交易准备失败")
    }
    
    // 第二阶段：提交
    commitOk1 := m.commitTrade(trade.Initiator, trade.Target)
    commitOk2 := m.commitTrade(trade.Target, trade.Initiator)
    
    if !commitOk1 || !commitOk2 {
        // 错误处理和恢复
        m.emergencyRecovery(trade)
        return errors.New("交易提交失败")
    }
    
    // 完成交易
    m.finishTrade(trade)
    return nil
}

4 关键技术点
   RPC通信：
      利用您现有的Invoke和改进后的CallAll函数进行节点间通信
      为交易操作创建专门的RPC路由
   事务管理：
      实现交易原子性，要么全部成功，要么全部失败
      使用两阶段提交确保跨节点一致性
   超时处理：
      设置交易各阶段超时机制
      超时自动取消交易并恢复状态
   日志记录：
      记录所有交易操作以便审计和恢复
      记录物品流转以防止复制和丢失
   异常恢复：
      实现节点故障时的交易恢复机制
      定期检查和清理僵尸交易

交易前验证双方物品和金币
防止同一玩家同时参与多个交易
实现交易频率限制防止滥用
重要物品交易增加额外验证