[{"id": 1, "bit": [2, 5, 25, 0, 1]}, {"id": 2, "bit": [2, 6, 20, 0, 1]}, {"id": 3, "bit": [2, 12, 20, 0, 1]}, {"id": 4, "bit": [2, 73, 55, 0, 1]}, {"id": 5, "bit": [2, 55, 45, 0, 1]}, {"id": 6, "bit": [2, 64, 50, 0, 1]}, {"id": 7, "bit": [2, 71, 65, 0, 1]}, {"id": 8, "bit": [2, 65, 35, 0, 1]}, {"id": 9, "bit": [2, 69, 300, 0, 1]}, {"id": 10, "bit": [4, 14, 10, 0, 1, 5, 7, 10, 0, 1, 6, 9, 10, 0, 1, 7, 26, 5, 0, 1]}, {"id": 11, "bit": [4, 7, 10, 0, 1, 5, 8, 10, 0, 1, 6, 57, 40, 0, 1, 7, 30, 10, 0, 1]}, {"id": 12, "bit": [5, 14, 30, 0, 1, 6, 72, 100, 0, 1, 7, 68, 100, 0, 1]}, {"id": 13, "bit": [7, 30, 15, 0, 1]}, {"id": 14, "bit": [7, 7, 10, 0, 1]}, {"id": 15, "bit": [2, 7, 2, 0, 1, 5, 77, 20, 0, 1, 6, 78, 30, 0, 1, 7, 30, 10, 0, 1]}, {"id": 16, "bit": [7, 56, 30, 0, 1]}, {"id": 17, "bit": [6, 14, 10, 0, 1, 7, 7, 30, 0, 1]}, {"id": 18, "bit": [6, 65, 10, 0, 1, 7, 7, 28, 0, 1]}, {"id": 19, "bit": [7, 64, 100, 0, 1]}, {"id": 20, "bit": [7, 68, 100, 0, 1]}, {"id": 21, "bit": [7, 74, 100, 0, 1]}, {"id": 22, "bit": [5, 77, 150, 0, 1, 6, 78, 150, 0, 1, 7, 30, 5, 0, 1]}, {"id": 23, "bit": [6, 73, 100, 0, 1, 7, 74, 100, 0, 1]}, {"id": 24, "bit": [2, 25, 25, 0, 1]}, {"id": 25, "bit": [2, 29, 20, 0, 1]}, {"id": 26, "bit": [2, 23, 5, 0, 1]}, {"id": 27, "bit": [2, 5, 25, 0, 1]}, {"id": 28, "bit": [2, 1, 25, 0, 1]}, {"id": 29, "bit": [2, 6, 30, 0, 1]}, {"id": 30, "bit": [2, 2, 15, 0, 1]}, {"id": 31, "bit": [2, 57, 40, 0, 1]}, {"id": 32, "bit": [2, 70, 70, 0, 1, 4, 74, 20, 0, 1, 5, 56, 10, 0, 1, 7, 78, 70, 0, 1]}, {"id": 33, "bit": [5, 9, 10, 0, 1, 6, 7, 10, 0, 1, 7, 65, 15, 0, 1]}, {"id": 34, "bit": [2, 9, 10, 0, 1, 5, 12, 10, 0, 1, 6, 14, 10, 0, 1, 7, 64, 30, 0, 1]}, {"id": 35, "bit": [5, 78, 200, 0, 1, 6, 3, 30, 0, 1, 7, 56, 30, 0, 1]}, {"id": 36, "bit": [5, 64, 80, 0, 1, 6, 78, 400, 0, 1, 7, 40, 20, 0, 1]}, {"id": 37, "bit": [6, 7, 30, 0, 1, 7, 30, 10, 0, 1]}, {"id": 38, "bit": [5, 26, 5, 0, 1, 6, 72, 50, 0, 1, 7, 68, 50, 0, 1]}, {"id": 39, "bit": [5, 60, 350, 0, 1, 6, 56, 20, 0, 1, 7, 126, 1, 0, 1]}, {"id": 40, "bit": [7, 55, 50, 0, 1]}, {"id": 41, "bit": [7, 3, 30, 0, 1]}, {"id": 42, "bit": [7, 5, 35, 0, 1]}, {"id": 43, "bit": [6, 53, 200, 0, 1, 7, 55, 50, 0, 1]}, {"id": 44, "bit": [7, 55, 25, 0, 1]}, {"id": 45, "bit": [7, 1, 15, 0, 1]}, {"id": 46, "bit": [6, 9, 10, 0, 1, 7, 5, 10, 0, 1]}, {"id": 47, "bit": [6, 1, 10, 0, 1, 7, 3, 10, 0, 1]}, {"id": 48, "bit": [7, 3, 25, 0, 1]}, {"id": 49, "bit": [7, 67, 50, 0, 1]}, {"id": 50, "bit": [7, 57, 50, 0, 1]}, {"id": 51, "bit": [7, 64, 50, 0, 1]}, {"id": 52, "bit": [7, 73, 60, 0, 1]}, {"id": 53, "bit": [7, 71, 50, 0, 1]}, {"id": 54, "bit": [2, 69, 350, 0, 1]}, {"id": 55, "bit": [2, 1, 35, 0, 1]}, {"id": 56, "bit": [2, 9, 35, 0, 1]}, {"id": 57, "bit": [2, 123, 2, 0, 1]}, {"id": 58, "bit": [2, 73, 80, 0, 1]}, {"id": 59, "bit": [2, 5, 15, 0, 1]}, {"id": 60, "bit": [2, 54, 30, 0, 1]}, {"id": 61, "bit": [2, 5, 25, 0, 1]}, {"id": 62, "bit": [2, 5, 5, 0, 1, 4, 69, 100, 0, 1, 5, 139, 10, 0, 1, 7, 128, 2, 0, 1]}, {"id": 63, "bit": [4, 14, 10, 0, 1, 5, 12, 10, 0, 1, 6, 64, 10, 0, 1, 7, 65, 10, 0, 1]}, {"id": 64, "bit": [5, 5, 5, 0, 1, 6, 5, 10, 0, 1, 7, 5, 25, 0, 1]}, {"id": 65, "bit": [5, 54, 10, 0, 1, 6, 53, 400, 0, 1, 7, 55, 40, 0, 1]}, {"id": 66, "bit": [5, 12, 15, 0, 1, 6, 69, 300, 0, 1, 7, 71, 50, 0, 1]}, {"id": 67, "bit": [5, 76, 5, 0, 1, 6, 35, 10, 0, 1, 7, 39, 30, 0, 1]}, {"id": 68, "bit": [5, 6, 15, 0, 1, 6, 65, 40, 0, 1, 7, 67, 80, 0, 1]}, {"id": 69, "bit": [4, 3, 30, 0, 1, 6, 1, 35, 0, 1, 7, 24, 10, 0, 1]}, {"id": 70, "bit": [4, 7, 5, 0, 1, 7, 9, 15, 0, 1]}, {"id": 71, "bit": [7, 14, 30, 0, 1]}, {"id": 72, "bit": [6, 40, 10, 0, 1, 7, 70, 300, 0, 1]}, {"id": 73, "bit": [6, 13, 300, 0, 1, 7, 14, 20, 0, 1]}, {"id": 75, "bit": [2, 7, 5, 0, 1, 4, 9, 5, 0, 1, 5, 14, 5, 0, 1, 7, 12, 5, 0, 1]}, {"id": 76, "bit": [6, 5, 10, 0, 1, 7, 3, 10, 0, 1]}, {"id": 77, "bit": [6, 3, 10, 0, 1, 7, 1, 10, 0, 1]}, {"id": 78, "bit": [6, 65, 10, 0, 1, 7, 64, 30, 0, 1]}, {"id": 79, "bit": [6, 55, 30, 0, 1, 7, 53, 100, 0, 1]}, {"id": 80, "bit": [6, 13, 300, 0, 1, 7, 57, 50, 0, 1]}, {"id": 81, "bit": [7, 64, 50, 0, 1]}, {"id": 82, "bit": [7, 73, 50, 0, 1]}, {"id": 83, "bit": [7, 71, 30, 0, 1]}, {"id": 84, "bit": [2, 76, 5, 0, 1]}, {"id": 85, "bit": [2, 2, 10, 0, 1]}, {"id": 86, "bit": [2, 69, 250, 0, 1]}, {"id": 87, "bit": [2, 54, 40, 0, 1]}, {"id": 88, "bit": [2, 57, 50, 0, 1]}, {"id": 89, "bit": [2, 69, 350, 0, 1]}, {"id": 90, "bit": [2, 139, 15, 0, 1]}, {"id": 91, "bit": [2, 54, 50, 0, 1]}, {"id": 92, "bit": [2, 9, 5, 0, 1, 4, 56, 10, 0, 1, 5, 126, 1, 0, 1, 7, 72, 20, 0, 1]}, {"id": 93, "bit": [5, 37, 10, 0, 1, 6, 24, 10, 0, 1, 7, 38, 10, 0, 1]}, {"id": 94, "bit": [5, 7, 30, 0, 1, 7, 72, 150, 0, 1]}, {"id": 95, "bit": [5, 3, 30, 0, 1, 6, 65, 50, 0, 1, 7, 64, 50, 0, 1]}, {"id": 96, "bit": [5, 40, 15, 0, 1, 6, 5, 35, 0, 1, 7, 6, 10, 0, 1]}, {"id": 97, "bit": [5, 7, 15, 0, 1, 6, 75, 400, 0, 1, 7, 56, 30, 0, 1]}, {"id": 98, "bit": [6, 40, 10, 0, 1, 7, 30, 10, 0, 1]}, {"id": 99, "bit": [5, 65, 35, 0, 1, 6, 64, 35, 0, 1, 7, 9, 40, 0, 1]}, {"id": 100, "bit": [7, 28, 5, 0, 1]}, {"id": 101, "bit": [5, 11, 300, 0, 1, 7, 13, 300, 0, 1]}, {"id": 102, "bit": [5, 1, 10, 0, 1, 6, 2, 10, 0, 1, 7, 3, 10, 0, 1]}, {"id": 103, "bit": [5, 65, 30, 0, 1, 6, 65, 30, 0, 1, 7, 65, 30, 0, 1]}, {"id": 105, "bit": [2, 1, 5, 0, 1, 4, 3, 5, 0, 1, 5, 12, 5, 0, 1, 7, 14, 60, 0, 1]}, {"id": 106, "bit": [7, 1, 25, 0, 1]}, {"id": 107, "bit": [7, 13, 20, 0, 1]}, {"id": 108, "bit": [7, 3, 25, 0, 1]}, {"id": 109, "bit": [7, 1, 25, 0, 1]}, {"id": 110, "bit": [7, 72, 50, 0, 1]}, {"id": 111, "bit": [6, 9, 15, 0, 1, 7, 13, 250, 0, 1]}, {"id": 112, "bit": [6, 13, 200, 0, 1, 7, 78, 200, 0, 1]}, {"id": 113, "bit": [7, 67, 80, 0, 1]}, {"id": 114, "bit": [2, 128, 2, 0, 1]}, {"id": 122, "bit": [2, 1, 5, 0, 1, 4, 53, 40, 0, 1, 5, 54, 15, 0, 1, 7, 58, 40, 0, 1]}, {"id": 123, "bit": [6, 64, 40, 0, 1, 7, 65, 30, 0, 1]}, {"id": 124, "bit": [6, 9, 20, 0, 1, 7, 3, 40, 0, 1]}, {"id": 125, "bit": [6, 13, 400, 0, 1, 7, 12, 30, 0, 1]}, {"id": 126, "bit": [5, 53, 150, 0, 1, 6, 55, 40, 0, 1, 7, 65, 35, 0, 1]}, {"id": 127, "bit": [6, 4, 15, 0, 1, 7, 2, 15, 0, 1]}, {"id": 128, "bit": [7, 12, 35, 0, 1]}, {"id": 129, "bit": [5, 3, 25, 0, 1, 6, 1, 25, 0, 1, 7, 76, 2, 0, 1]}, {"id": 130, "bit": [2, 64, 100, 0, 1]}, {"id": 131, "bit": [1, 130, 2, 0, 1]}, {"id": 132, "bit": [2, 139, 20, 0, 1]}, {"id": 133, "bit": [2, 122, 2, 0, 1]}, {"id": 134, "bit": [5, 10, 5, 0, 1, 6, 174, 1, 0, 1, 7, 175, 10, 0, 1]}, {"id": 135, "bit": [2, 133, 50, 0, 1]}, {"id": 136, "bit": [2, 134, 20, 0, 1]}, {"id": 137, "bit": [2, 65, 100, 0, 1, 3, 178, 1, 0, 1]}, {"id": 138, "bit": [6, 178, 1, 0, 1, 7, 130, 1, 0, 1]}, {"id": 139, "bit": [4, 126, 1, 0, 1, 5, 8, 10, 0, 1, 6, 73, 200, 0, 1, 7, 178, 1, 0, 1]}, {"id": 140, "bit": [2, 133, 50, 0, 1]}, {"id": 141, "bit": [4, 56, 50, 0, 1, 5, 8, 10, 0, 1, 6, 10, 15, 0, 1, 7, 30, 10, 0, 1]}, {"id": 142, "bit": [3, 23, 10, 0, 1, 4, 5, 40, 0, 1, 5, 130, 1, 0, 1, 6, 141, 30, 0, 1, 7, 168, 170, 1, 0]}, {"id": 143, "bit": [3, 75, 2000, 0, 1, 4, 7, 100, 0, 1, 5, 26, 50, 0, 1, 6, 45, 50, 0, 1, 7, 169, 300, 1, 0]}, {"id": 145, "bit": [5, 168, 200, 0, 1, 6, 28, 15, 0, 1, 7, 29, 15, 0, 1]}, {"id": 146, "bit": [5, 173, 200, 0, 1, 6, 28, 15, 0, 1, 7, 29, 15, 0, 1]}, {"id": 147, "bit": [5, 169, 200, 0, 1, 6, 45, 15, 0, 1, 7, 30, 15, 0, 1]}, {"id": 148, "bit": [5, 172, 200, 0, 1, 6, 171, 200, 0, 1, 7, 34, 15, 0, 1]}, {"id": 149, "bit": [1, 53, 1200, 1, 0, 2, 23, 20, 1, 0]}, {"id": 151, "bit": [1, 38, 10, 1, 0, 2, 34, 20, 1, 0]}, {"id": 152, "bit": [1, 23, 20, 1, 0, 2, 172, 400, 1, 0]}, {"id": 153, "bit": [1, 172, 50, 1, 0, 2, 172, 100, 2, 0]}, {"id": 154, "bit": [1, 64, 50, 1, 0, 2, 64, 100, 2, 0]}, {"id": 155, "bit": [1, 171, 50, 1, 0, 2, 171, 100, 2, 0]}, {"id": 156, "bit": [1, 23, 20, 6, 0]}, {"id": 157, "bit": [1, 191, 30, 5, 0]}, {"id": 158, "bit": [1, 64, 400, 7, 0]}, {"id": 159, "bit": [1, 68, 400, 8, 0]}, {"id": 160, "bit": [1, 171, 400, 8, 0]}, {"id": 161, "bit": [1, 26, 20, 7, 0]}, {"id": 162, "bit": [1, 173, 500, 7, 0]}, {"id": 163, "bit": [1, 173, 500, 7, 0]}, {"id": 164, "bit": [1, 141, 20, 5, 0]}, {"id": 165, "bit": [1, 138, 30, 6, 0]}, {"id": 166, "bit": [1, 141, 30, 7, 0]}, {"id": 167, "bit": [5, 170, 400, 4, 0, 6, 168, 400, 5, 0, 7, 173, 400, 6, 0]}, {"id": 168, "bit": [1, 171, 400, 7, 0]}, {"id": 169, "bit": [1, 68, 400, 7, 0]}, {"id": 170, "bit": [1, 172, 400, 6, 0]}, {"id": 171, "bit": [1, 23, 40, 7, 0]}, {"id": 172, "bit": [1, 68, 50, 1, 0, 2, 68, 100, 2, 0]}, {"id": 173, "bit": [1, 68, 400, 9, 0]}, {"id": 174, "bit": [1, 171, 400, 8, 0]}, {"id": 175, "bit": [1, 25, 20, 2, 0, 2, 23, 30, 3, 0]}, {"id": 176, "bit": [1, 171, 500, 8, 0]}, {"id": 177, "bit": [1, 27, 20, 4, 0, 2, 130, 4, 5, 0]}, {"id": 178, "bit": [1, 171, 400, 8, 0]}, {"id": 179, "bit": [1, 139, 50, 7, 0]}, {"id": 180, "bit": [1, 54, 50, 1, 0, 2, 54, 100, 2, 0]}, {"id": 181, "bit": [1, 54, 300, 6, 0]}, {"id": 182, "bit": [1, 136, 50, 7, 0]}, {"id": 183, "bit": [1, 23, 40, 8, 0]}, {"id": 184, "bit": [1, 141, 50, 8, 0]}, {"id": 185, "bit": [1, 124, 2, 7, 0]}, {"id": 186, "bit": [1, 64, 200, 5, 0, 2, 23, 30, 7, 0]}, {"id": 187, "bit": [1, 30, 50, 7, 0]}, {"id": 188, "bit": [1, 133, 50, 7, 0]}, {"id": 189, "bit": [1, 23, 80, 8, 0]}, {"id": 190, "bit": [1, 178, 3, 8, 0]}, {"id": 191, "bit": [1, 125, 3, 7, 0]}, {"id": 192, "bit": [1, 39, 50, 7, 0]}, {"id": 193, "bit": [5, 40, 20, 5, 0, 6, 30, 20, 6, 0, 7, 10, 20, 7, 0]}, {"id": 194, "bit": [1, 171, 500, 8, 0]}, {"id": 195, "bit": [1, 139, 80, 8, 0]}, {"id": 196, "bit": [1, 23, 80, 8, 0]}, {"id": 197, "bit": [1, 178, 3, 8, 0]}, {"id": 198, "bit": [1, 134, 80, 7, 0]}, {"id": 199, "bit": [1, 172, 200, 5, 0, 2, 171, 400, 7, 0]}, {"id": 200, "bit": [1, 134, 20, 5, 0, 2, 23, 50, 7, 0]}, {"id": 201, "bit": [1, 23, 80, 8, 0]}, {"id": 202, "bit": [1, 168, 400, 5, 0, 2, 173, 400, 7, 0]}, {"id": 203, "bit": [1, 34, 20, 3, 0, 2, 35, 30, 5, 0]}, {"id": 204, "bit": [1, 169, 200, 6, 0]}, {"id": 205, "bit": [1, 172, 200, 6, 0]}, {"id": 206, "bit": [1, 171, 200, 6, 0]}, {"id": 207, "bit": [5, 169, 400, 5, 0, 6, 173, 400, 6, 0, 7, 57, 400, 7, 0]}, {"id": 208, "bit": [4, 12, 10, 0, 1, 5, 14, 10, 0, 1, 6, 56, 10, 0, 1, 7, 70, 10, 0, 1]}, {"id": 209, "bit": [4, 12, 10, 0, 1, 5, 14, 10, 0, 1, 6, 54, 10, 0, 1, 7, 65, 10, 0, 1]}, {"id": 210, "bit": [1, 192, 1, 7, 0, 1, 56, 500, 8, 0]}, {"id": 211, "bit": [4, 171, 50, 4, 0, 5, 171, 150, 6, 0, 7, 171, 300, 7, 0]}, {"id": 212, "bit": [1, 128, 3, 5, 0, 2, 23, 50, 7, 0]}, {"id": 213, "bit": [1, 122, 3, 5, 0, 2, 23, 50, 7, 0]}, {"id": 214, "bit": [1, 53, 2400, 5, 0, 1, 26, 30, 9, 0]}, {"id": 215, "bit": [1, 57, 300, 7, 0]}]