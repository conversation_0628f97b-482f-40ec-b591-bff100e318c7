package cfg

import (
	"world/common/pbBase/ModelConst"

	"github.com/samber/lo"
)

const ConfigNameMisc_C = "Misc_C"

type Misc_C[K string] struct {
	Bag   *BagCfg   `json:"bag"`
	Role  *RoleCfg  `json:"role"`
	Skill *SkillCfg `json:"skill"`
	Pet   *PetCfg   `json:"pet"`
}

func (m *Misc_C[K]) GetName() string {
	return ConfigNameMisc_C
}
func (m *Misc_C[K]) GetUnique() K {
	return ConfigNameMisc_C
}

func (m *Misc_C[K]) OnInit() {
	if m.Bag != nil {
		maxBy := lo.MaxBy(m.Bag.VipStoreNum, func(i int, i2 int) bool {
			return i > i2
		})
		// 初始化vip仓库最大格子数量
		m.Bag.VipStoreNumMax = maxBy
	}

	if m.Pet != nil {

	}

}

// GetAttributePoint 获取角色升级时每级获得的属性点
//
// Returns:
//   - int
func (m *Misc_C[K]) GetAttributePoint() int {
	return m.Role.AttributePoint
}

// GetSkillPoint 获取角色升级时每级获得的技能点 结果需要乘以等级
//
// Returns:
//   - int
func (m *Misc_C[K]) GetSkillPoint() int {
	return m.Role.SkillPoint
}

type BagCfg struct {
	BagNum         int   `json:"bagNum"`
	BagNumMax      int   `json:"bagNumMax"`
	StoreNum       int   `json:"storeNum"`
	StoreNumMax    int   `json:"storeNumMax"`
	VipStoreNum    []int `json:"vipStoreNum"`
	VipStoreNumMax int   `json:"-"`
}

// GetVipStoreNum 获取vip对应的仓库大小
//
// Parameters:
//   - vipLv int vip等级
//
// Returns:
//   - int 仓库大小
func (b *BagCfg) GetVipStoreNum(vipLv int) int {
	if vipLv <= 0 {
		return 0
	}
	return b.VipStoreNum[vipLv-1]
}

// GetMaxVipStoreNum
/*
 * @description 获取vip能解锁的最大仓库大小
 * @return int
 */
func (b *BagCfg) GetMaxVipStoreNum() int {
	return b.VipStoreNum[len(b.VipStoreNum)-1]
}

type RoleCfg struct {
	AttributePoint              int `json:"attributePoint"`
	SkillPoint                  int `json:"skillPoint"`
	AttributePointChangeCost    int `json:"attributePointChangeCost"`
	AttributePointChangeCostMax int `json:"attributePointChangeCostMax"`
}

type SkillCfg struct {
	ReqMoney        []*skillLearnReqMoney `json:"reqMoney"`
	CrossJobRate    int                   `json:"crossJobRate"`
	DefaultSkillCnt int                   `json:"defaultSkillCnt"`
}

type skillLearnReqMoney struct {
	Money1 int `json:"money1"`
	Money2 int `json:"money2"`
	Money3 int `json:"money3"`
}

func (s *skillLearnReqMoney) GetMoneyData() (moneyType ModelConst.Type, moneyValue int) {
	if s.Money1 > 0 {
		return ModelConst.MONEY1, s.Money1
	}
	if s.Money2 > 0 {
		return ModelConst.MONEY2, s.Money2
	}
	if s.Money3 > 0 {
		return ModelConst.MONEY3, s.Money3
	}
	return ModelConst.MONEY1, 99999
}

type PetCfg struct {
	TakeNum int `json:"takeNum"`
	Seal    []struct {
		Rate       int   `json:"rate"`
		MoneyType  int   `json:"moneyType"`
		MoneyValue []int `json:"moneyValue"`
	} `json:"seal"`
	SealSkillLearnSp []int `json:"sealSkillLearnSp"`
	BookLearnCntMax  int   `json:"bookLearnCntMax"`
}
