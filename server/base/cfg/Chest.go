package cfg

const ConfigNameChest = "Chest"

type Chest[K int] struct {
	Id             K             `json:"id"`
	Reward         []*RewardItem `json:"reward"`
	RdReward       []*RewardItem `json:"rdReward"`
	hasServerLimit bool          `json:"-"`
}

func (c *Chest[K]) GetName() string {
	return ConfigNameChest
}
func (c *Chest[K]) GetUnique() K {
	return c.Id
}
func (c *Chest[K]) OnInit() {
	checkItem(c.Reward, ConfigNameChest)
	checkItem(c.RdReward, ConfigNameChest)
	for _, item := range c.RdReward {
		if item.NeedRecord() && item.LimitByServer() {
			c.hasServerLimit = true
			break
		}
	}
}

func (c *Chest[K]) HasServerLimit() bool { return c.hasServerLimit }
