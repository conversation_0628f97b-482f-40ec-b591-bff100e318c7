package cfg

const ConfigNameMission = "Mission"

type Mission[K int] struct {
	Id                  K                  `json:"id"`
	Exp                 int                `json:"exp"`
	Money2              int                `json:"money2"`
	Money3              int                `json:"money3"`
	AcceptMapId         int                `json:"acceptMapId"`         // 领取任务的地图id
	AcceptNpcId         int                `json:"acceptNpcId"`         // 领取任务的npc
	AcceptBattleID      int                `json:"acceptBattleID"`      // 领取任务时进入战斗
	MapId               int                `json:"mapId"`               // 提交任务的地图id
	NpcId               int                `json:"npcId"`               // 提交任务的npc
	SubmitBattleID      int                `json:"submitBattleID"`      // 提交任务时进入战斗id
	SubmitJumpMapID     int                `json:"submitJumpMapID"`     // 提交任务后 跳转地图
	SubmitJumpMapGx     int                `json:"submitJumpMapGx"`     // 提交任务后 跳转地图x
	SubmitJumpMapGy     int                `json:"submitJumpMapGy"`     // 提交任务后 跳转地图y
	SubmitNextMissionID int                `json:"submitNextMissionID"` // 提交任务后 下一个任务id
	AcceptCondition     []*ConfigCondition `json:"acceptCondition"`     // 领取条件
	SubmitCondition     []*ConfigCondition `json:"submitCondition"`     // 提交条件
	Type                int                `json:"type"`                // 任务类型
	Setting             int                `json:"setting"`             // 任务设置bit
	RewardItems         []*RewardItem      `json:"rewardItems"`         // 必得奖励物品
	SelectItems         []*RewardItem      `json:"selectItems"`         // 单选奖励物品
}

func (m *Mission[K]) GetName() string {
	return ConfigNameMission
}
func (m *Mission[K]) GetUnique() K {
	return m.Id
}
func (m *Mission[K]) OnInit() {
	checkItem(m.RewardItems, ConfigNameMission)
	checkItem(m.SelectItems, ConfigNameMission)
}

// IsDirectSubmitMission 可以直接完成的任务，不需要领取
func (m *Mission[K]) IsDirectSubmitMission() bool {
	return (m.Setting & 1) > 0
}

// HasRewardItems 是否拥有必得任务奖励
//
// Returns:
//   - bool
func (m *Mission[K]) HasRewardItems() bool {
	return len(m.RewardItems) > 0
}

// HasSelectItems 是否拥有可选任务奖励
//
// Returns:
//   - bool
func (m *Mission[K]) HasSelectItems() bool {
	return len(m.SelectItems) > 0
}
