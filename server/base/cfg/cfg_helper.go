package cfg

import (
	"fmt"
	"world/common/pbBase/ConditionType"
	ut "world/utils"

	"github.com/samber/lo"
)

func afterInit() {
	initMapNpc()
	initSkill()
}

func initMapNpc() {
	lo.ForEach(ContainerNpc.GetData(), func(v *Npc[int], _ int) {
		mapBean, _ := ContainerMap.GetBeanByUnique(v.MapId)
		if mapBean.NpcMap == nil {
			mapBean.NpcMap = make(map[int]*Npc[int])
		}
		// 关联地图
		mapBean.NpcMap[v.Id] = v
		if v.TaskMap == nil {
			v.TaskMap = make(map[int]*Mission[int])
		}
		// 关联任务
		for _, taskId := range v.Missions {
			taskBean, _ := ContainerMission.GetBeanByUnique(taskId)
			v.TaskMap[taskId] = taskBean
		}
	})
}

// initSkill 主要是初始化技能的最大等级数据
func initSkill() {
	ary := ContainerSkill.GetData()
	info := make(map[int]int)

	for _, data := range ary {
		lv := 0
		if v, ok := info[data.Id]; ok {
			lv = v
		}
		info[data.Id] = ut.Max(lv, data.Level)
	}

	lo.ForEach(ContainerSkill.GetData(), func(v *Skill[string], _ int) {
		v.ConfigMaxLevel = info[v.Id]
	})
}

// checkItem 检查物品配置
//
// Parameters:
//   - items []*RewardItem
//   - tag string
func checkItem(items []*RewardItem, tag string) {
	for idx, item := range items {
		if item.Type == ConditionType.None {
			item.Type = ConditionType.HaveItem
		}
		item.RefreshTime *= ut.TIME_HOUR
		// 物品类型  需要检查道具是否存在
		if item.Type == ConditionType.HaveItem {
			bean, _ := ContainerItem.GetBeanByUnique(item.Id)
			if bean == nil {
				panic(fmt.Sprintf("[%s]check item %d not exist", tag, item.Id))
			}
		}

		switch item.LimitType {
		case "SRV":
		case "SLF":
		default:
			item.LimitNum = 0
			item.RefreshTime = 0
		}

		if item.NeedRecord() {
			item.LimitIndex = idx
		}
	}
}
