package cfg

import (
	"encoding/json"
	"world/common/pbGame"
	"world/common/pbBase/ITEM_GRADE"
	"world/common/pbBase/ITEM_TYPE"
	"world/common/pbBase/MyDefine"
)

const ConfigNameItem = "Item"

type Item[K int] struct {
	Id                   K               `json:"id"`
	Type                 ITEM_TYPE.Type  `json:"type"`
	Grade                ITEM_GRADE.Type `json:"grade"`
	ReqLv                int             `json:"reqLv"`
	ReqStr               int             `json:"reqStr"`
	ReqCon               int             `json:"reqCon"`
	ReqAgi               int             `json:"reqAgi"`
	ReqIlt               int             `json:"reqIlt"`
	ReqWis               int             `json:"reqWis"`
	AtkType              pbGame.AtkType  `json:"atkType"`
	AtkTime              int             `json:"atk_time"`
	AtkMin               int             `json:"atkMin"`
	AtkMax               int             `json:"atkMax"`
	DefStr               int             `json:"def_str"`
	DefAgi               int             `json:"def_agi"`
	DefMag               int             `json:"def_mag"`
	HitRate              int             `json:"hitrate"`
	Round                int             `json:"round"`
	Area                 int             `json:"area"`
	DurMax               int             `json:"durMax"`
	VipLevelReq          int             `json:"vipLevelReq"` // 需求vip等级
	FashionIcon1         int64           `json:"fashIcon1"`
	FashionIcon2         int64           `json:"fashIcon2"`
	FashionIcon3         int64           `json:"fashIcon3"`
	BagIcon              int             `json:"bagIcon"`
	AutoBind             int             `json:"autoBinding"`
	StackNum             int             `json:"stackNum"`
	Decompose            bool            `json:"-"`
	Price                int             `json:"price"`
	PowerValueBlood1     int             `json:"PowerValueBlood1"`    // 能量精华数据
	PowerValueBloodMax1  int             `json:"PowerValueBloodMax1"` // 能量精华数据
	OwnTime              int             `json:"ownTime"`
	Icon                 int             `json:"icon"`
	AttachCount          int             `json:"attachCount"`
	ItemSet              int             `json:"itemSet"`
	AscensionStar        int             `json:"ascensionStar"`        // 升星
	UpgradeAscensionStar int             `json:"upgradeAscensionStar"` // 进阶升星
	PetProvideExp        int             `json:"petProvideExp"`        // 提供宠物装备经验
	SealGrade            int             `json:"seal_grade"`
	SealType             int             `json:"seal_type"`
	Power1               *Power          `json:"-"`
	Power2               *Power          `json:"-"`
	Power3               *Power          `json:"-"`
	BindPower1           *Power          `json:"-"`
	BindPower2           *Power          `json:"-"`
}

func (i *Item[K]) GetName() string { return ConfigNameItem }
func (i *Item[K]) GetUnique() K    { return i.Id }
func (i *Item[K]) OnInit() {
	// 检查配置
	// switch i.Power1.Type {
	// case MyDefine.POWER_CHEST_LV1:
	// 	fallthrough
	// case MyDefine.POWER_CHEST_LV2:
	// 	fallthrough
	// case MyDefine.POWER_CHEST_LV3:
	// 	chestId := int(i.Power1.Value)
	// 	chest, _ := ContainerChest.GetBeanByUnique(chestId)
	// 	if chest == nil {
	// 		log.Error("物品宝箱配置不存在: %d -> %d", i.Id, chestId)
	// 	} else {
	// 		if i.Power2 == nil {
	// 			log.Error("宝箱奖池配置不存在: %d -> %d", i.Id, chestId)
	// 		} else {
	// 			if i.Power2.Type != MyDefine.POWER_REQ_SLOT {
	// 				log.Error("宝箱奖池配置错误: %d -> %d", i.Id, chestId)
	// 			} else {
	// 				// 必得
	// 				must := len(chest.Reward)
	// 				// 随机奖池
	// 				random := len(chest.RdReward)
	// 				if must+random < int(i.Power2.Value) {
	// 					log.Error("宝箱奖池道具数量配置过少，不满足最小配置: %d -> %d", i.Id, chestId)
	// 				}
	// 			}
	// 		}
	// 	}
	// }

}

// 序列化后处理
func (i *Item[K]) UnmarshalJSON(data []byte) error {
	type Alias Item[K]
	type wrapItem struct {
		*Alias
		DecomChest      string `json:"decomCHest"`
		Power1          int    `json:"power1"`
		PowerValue1     int    `json:"powerValue1"`
		Power2          int    `json:"power2"`
		PowerValue2     int    `json:"powerValue2"`
		Power3          int    `json:"power3"`
		PowerValue3     int    `json:"powerValue3"`
		BindPower1      int    `json:"bindPower1"`
		BindPowerValue1 int    `json:"bindPowerValue1"`
		BindPower2      int    `json:"bindPower2"`
		BindPowerValue2 int    `json:"bindPowerValue2"`
	}
	// wrap.Alias 实际上指向了 i 实例
	wrap := wrapItem{
		Alias: (*Alias)(i),
	}
	if err := json.Unmarshal(data, &wrap); err != nil {
		return err
	}
	// 数据后处理
	i.Decompose = wrap.DecomChest == "true"
	i.Power1 = i.parsePower(wrap.Power1, wrap.PowerValue1)
	i.Power2 = i.parsePower(wrap.Power2, wrap.PowerValue2)
	i.Power3 = i.parsePower(wrap.Power3, wrap.PowerValue3)
	i.BindPower1 = i.parsePower(wrap.BindPower1, wrap.BindPowerValue1)
	i.BindPower2 = i.parsePower(wrap.BindPower2, wrap.BindPowerValue2)
	return nil
}

// IsCanStack 是否可以堆积
func (i *Item[K]) IsCanStack() bool { return i.StackNum > 1 }

// IsAutoBinding 是否自动绑定
func (i *Item[K]) IsAutoBinding() bool { return i.AutoBind == 1 }

// parsePower
/*
 * @description 生成power
 * @param typ
 * @param val
 * @return *PowerCfg
 */
func (i *Item[K]) parsePower(typ, val int) *Power { return CreatePower(MyDefine.POWER(typ), val) }

// IsOneHandWeaponType 是不是单手武器类型
func (i *Item[K]) IsOneHandWeaponType() bool {
	if i.Type == ITEM_TYPE.WEAPON_ONEHAND_SWORD {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_ONEHAND_BLADE {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_ONEHAND_CROSSBOW {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_ONEHAND_GUN {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_ONEHAND_HAMMER {
		return true
	}
	return false
}

// IsTwoHandWeaponType 是不是双手武器类型
func (i *Item[K]) IsTwoHandWeaponType() bool {
	if i.Type == ITEM_TYPE.WEAPON_TWOHAND_SWORD {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_TWOHAND_BLADE {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_TWOHAND_HEAVY {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_TWOHAND_STAFF {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_TWOHAND_LANCE {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_TWOHAND_CROSSBOW {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_BALL {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_TWOHAND_BOW {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_TWOHAND_GUN {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_TWOHAND_HAMMER {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_TWOHAND_FAN {
		return true
	}
	return false
}
