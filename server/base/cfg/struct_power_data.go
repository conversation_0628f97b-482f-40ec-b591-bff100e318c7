package cfg

import (
	"world/common/pbBase"
	"world/common/pbBase/MyDefine"
)

// 从protobuf创建Power
func CreatePowerByPb(pd *pbBase.PowerData) *Power {
	if pd == nil {
		return nil
	}
	return &Power{typ: pd.Type, val: int(pd.Value)}
}

// 创建Power
func CreatePower(typ MyDefine.POWER, val int) *Power { return &Power{typ: typ, val: val} }

// 从protobuf设置Power
func SetPowerByPb(p *Power, pd *pbBase.PowerData) *Power {
	if pd == nil {
		return p
	}
	if p == nil {
		return CreatePowerByPb(pd)
	}
	return p.SetType(pd.Type).SetValue(int(pd.Value))
}

type Power struct {
	typ MyDefine.POWER `marshal:"typ,omitempty"`
	val int            `marshal:"val,omitempty"`
}

func (p *Power) ToPb() *pbBase.PowerData {
	if p == nil {
		return nil
	}
	return &pbBase.PowerData{
		Type:  p.typ,
		Value: int32(p.val),
	}
}

// 复制一个
func (p *Power) Clone() *Power {
	if p == nil {
		return nil
	}
	return CreatePower(p.typ, p.val)
}

// 复制到另一个Power 如果目标为nil则创建一个
func (p *Power) CloneTo(target *Power) *Power {
	if p == nil {
		return target
	}
	if target == nil {
		return p.Clone()
	}
	return target.SetType(p.typ).SetValue(p.val)
}

// 设置值
func (p *Power) SetValue(v int) *Power { p.val = v; return p }

// int32设置值
func (p *Power) SetValueInt32(v int32) *Power { p.val = int(v); return p }

// 增加值
func (p *Power) AddValue(v int) *Power { p.val += v; return p }

// int32增加值
func (p *Power) AddValueInt32(v int32) *Power { p.val += int(v); return p }

// 获取值
func (p *Power) GetValue() int { return p.val }

// int32获取值
func (p *Power) GetValueInt32() int32 { return int32(p.val) }

// 获取类型
func (p *Power) GetType() MyDefine.POWER { return p.typ }

// 设置类型
func (p *Power) SetType(typ MyDefine.POWER) *Power { p.typ = typ; return p }

// 判断类型是否相同
func (p *Power) IsTypeEqual(typ MyDefine.POWER) bool {
	if p == nil {
		return false
	}
	return p.typ == typ
}

// 判断类型是否相同
func (p *Power) IsTypeEqualBy(compare *Power) bool {
	if p == nil || compare == nil {
		return false
	}
	return p.typ == compare.typ
}

// IsSameBy 判断两个Power是否相同，即类型和值都相同
func (p *Power) IsSameBy(compare *Power) bool {
	if p == nil || compare == nil {
		return false
	}
	return p.typ == compare.typ && p.val == compare.val
}
