package cfg

const ConfigNameGroup = "Group"

type Group[K int] struct {
	Id                K           `json:"id"`
	Flag              int         `json:"flag"`
	MonsterAry        []int       `json:"monsters"`
	Npcs              []int       `json:"npcs"`
	Type              int         `json:"type"`
	NextBattleGroupID int         `json:"nextBattleGroupID"`
	Coefficient       int         `json:"coefficient"`
	groupedMonster    map[int]int `json:"-"`
}

func (g *Group[K]) GetName() string {
	return ConfigNameGroup
}
func (g *Group[K]) GetUnique() K {
	return g.Id
}
func (g *Group[K]) OnInit() {
	// 将怪物数据提前分好  方便后面获取这个怪物组的每个怪物个数
	g.groupedMonster = make(map[int]int)
	for _, id := range g.MonsterAry {
		v, ok := g.groupedMonster[id]
		if !ok {
			g.groupedMonster[id] = 1
		} else {
			g.groupedMonster[id] = v + 1
		}
	}
}

// GetGroupedMonster 获取怪物组中每个怪物的个数
//
// Returns:
//   - map[int]int
func (g *Group[K]) GetGroupedMonster() map[int]int {
	return g.groupedMonster
}

// GetGroupedMonsterById 获取怪物组中怪物的个数
//
// Parameters:
//   - id int 怪物的id
//
// Returns:
//   - int 怪物的个数
func (g *Group[K]) GetGroupedMonsterById(id int) int {
	return g.groupedMonster[id]
}
