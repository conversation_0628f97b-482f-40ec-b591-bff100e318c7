package cfg

import "fmt"

const ConfigNameSkillShop = "SkillShop"

type SkillShop[K string] struct {
	Id            int    `json:"id"`
	SkillId       int    `json:"skillId"`
	SkillMaxLevel int    `json:"skillMaxLevel"`
	Unique        string `json:"-"`
}

func (s *SkillShop[K]) GetName() string {
	return ConfigNameSkillShop
}
func (s *SkillShop[K]) GetUnique() K {
	if s.Unique == "" {
		s.Unique = fmt.Sprintf("%d-%d", s.Id, s.SkillId)
	}
	return K(s.Unique)
}
func (s *SkillShop[K]) OnInit() {
}
