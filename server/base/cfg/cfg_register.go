package cfg

import (
	"github.com/huyangv/vmqant/log"
	"time"
)

var ContainerAi *ConfigContainer[int, *Ai[int]]
var ContainerChest *ConfigContainer[int, *Chest[int]]
var ContainerGroup *ConfigContainer[int, *Group[int]]
var ContainerIdentity *ConfigContainer[int, *Identity[int]]
var ContainerItem *ConfigContainer[int, *Item[int]]
var ContainerLevelExp *ConfigContainer[int, *LevelExp[int]]
var ContainerMap *ConfigContainer[int, *Map[int]]
var ContainerMisc_C *ConfigContainer[string, *Misc_C[string]]
var ContainerMission *ConfigContainer[int, *Mission[int]]
var ContainerMonster *ConfigContainer[int, *Monster[int]]
var ContainerNpc *ConfigContainer[int, *Npc[int]]
var ContainerPet *ConfigContainer[int, *Pet[int]]
var ContainerSkill *ConfigContainer[string, *Skill[string]]
var ContainerSkillShop *ConfigContainer[string, *SkillShop[string]]
var ContainerSuit *ConfigContainer[int, *Suit[int]]

func LoadConfig() {
	sTime := time.Now()
	log.Info("开始初始化配置文件!")
	ContainerAi = localNewContainer[int, *Ai[int]](ConfigNameAi)
	ContainerChest = localNewContainer[int, *Chest[int]](ConfigNameChest)
	ContainerGroup = localNewContainer[int, *Group[int]](ConfigNameGroup)
	ContainerIdentity = localNewContainer[int, *Identity[int]](ConfigNameIdentity)
	ContainerItem = localNewContainer[int, *Item[int]](ConfigNameItem)
	ContainerLevelExp = localNewContainer[int, *LevelExp[int]](ConfigNameLevelExp)
	ContainerMap = localNewContainer[int, *Map[int]](ConfigNameMap)
	ContainerMisc_C = localNewContainer[string, *Misc_C[string]](ConfigNameMisc_C)
	ContainerMission = localNewContainer[int, *Mission[int]](ConfigNameMission)
	ContainerMonster = localNewContainer[int, *Monster[int]](ConfigNameMonster)
	ContainerNpc = localNewContainer[int, *Npc[int]](ConfigNameNpc)
	ContainerPet = localNewContainer[int, *Pet[int]](ConfigNamePet)
	ContainerSkill = localNewContainer[string, *Skill[string]](ConfigNameSkill)
	ContainerSkillShop = localNewContainer[string, *SkillShop[string]](ConfigNameSkillShop)
	ContainerSuit = localNewContainer[int, *Suit[int]](ConfigNameSuit)
	log.Info("配置文件初始化完成， during:%fs", time.Since(sTime).Seconds())
}
