package cfg

const ConfigNameNpc = "Npc"

type Npc[K int] struct {
	Id                     K                     `json:"id"`
	MapId                  int                   `json:"mapId"`
	NpcType                int                   `json:"npcType"`
	X                      int                   `json:"x"`
	Y                      int                   `json:"y"`
	Missions               []int                 `json:"missions"`
	JumpMapID              []int                 `json:"jumpMapID"`
	JumpMapGx              []int                 `json:"jumpMapGx"`
	JumpMapGy              []int                 `json:"jumpMapGy"`
	JumpMapReqMissionID    []int                 `json:"jumpMapReqMissionID"`
	JumpMapReqMissionState []int                 `json:"jumpMapReqMissionState"`
	BattleId               int                   `json:"battleId"`
	BattleReqMissionID     []int                 `json:"battleReqMissionID"`
	BattleReqMissionState  []int                 `json:"battleReqMissionState"`
	ShopReqMissionID       []int                 `json:"shopReqMissionID"`
	ShopReqMissionState    []int                 `json:"shopReqMissionState"`
	ShopID                 []int                 `json:"shopID"`
	TaskMap                map[int]*Mission[int] `json:"-" bson:"-"`
}

func (n *Npc[K]) GetName() string {
	return ConfigNameNpc
}
func (n *Npc[K]) GetUnique() K {
	return n.Id
}
func (n *Npc[K]) OnInit() {
}
