package cfg

import (
	"fmt"
	"world/common/pbBase/ConditionType"
)

// LEVEL_MAX 服务器限制最高等级
var lv_max int = 0

func setLevelMax(v int) { lv_max = v }
func LevelMax() int     { return lv_max + 1 }

type ConfigCondition struct {
	Type int `json:"type"`
	Id   int `json:"id"`
	Num  int `json:"num"`
}

// RewardItem 奖励物品
type RewardItem struct {
	Type        ConditionType.Type `json:"type"`        // 物品类型
	Id          int                `json:"id"`          // 物品id
	Quantity    int                `json:"quantity"`    // 数量
	Rate        int                `json:"rate"`        // 概率
	LimitType   string             `json:"limitType"`   // 限制类型
	LimitNum    int                `json:"limitNum"`    // 限制数量
	RefreshTime int64              `json:"refreshTime"` // 刷新时间
	LimitIndex  int                `json:"-"`           // 限制索引 即配置数组索引
}

func (r *RewardItem) NeedRecord() bool    { return r.LimitType != "" }
func (r *RewardItem) LimitBySelf() bool   { return r.LimitType == "SLF" }
func (r *RewardItem) LimitByServer() bool { return r.LimitType == "SRV" }

// GetSkillConfigMaxLevel 获取技能存在的配置最大等级
//
// Parameters:
//   - skillId int
//
// Returns:
//   - int
func GetSkillConfigMaxLevel(skillId int) int {
	bean, _ := ContainerSkill.GetBeanByUnique(fmt.Sprintf("%d-1", skillId))
	if bean == nil {
		return 0
	}
	return bean.ConfigMaxLevel
}

// 宠物天赋技能
type PetBornSkill struct {
	Id    int `json:"id"`
	Level int `json:"level"`
}

// 宠物领悟技能
type PetLearnSkill struct {
	Id   int `json:"id"`
	Seal int `json:"seal"` // 封印概率
}
