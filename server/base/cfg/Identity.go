package cfg

import (
	"fmt"
	"world/common/pbBase/MyDefine"
)

const ConfigNameIdentity = "Identity"

type Identity[K int] struct {
	Id         K                `json:"id"`
	Power1     []*IdentifyPower `json:"power1"`
	Power2     []*IdentifyPower `json:"power2"`
	BindPower1 []*IdentifyPower `json:"bindPower1"`
	BindPower2 []*IdentifyPower `json:"bindPower2"`
	Power4     []*IdentifyPower `json:"power4"`
	Power5     []*IdentifyPower `json:"power5"`
	Power6     []*IdentifyPower `json:"power6"`
	Power7     []*IdentifyPower `json:"power7"`
}

func (i *Identity[K]) GetName() string {
	return ConfigNameIdentity
}
func (i *Identity[K]) GetUnique() K {
	return i.Id
}
func (i *Identity[K]) OnInit() {
	check := func(power []*IdentifyPower) {
		for _, v := range power {
			if !v.IsValid() {
				panic(fmt.Sprintf("鉴定配置有错误,出现0类型属性,物品id:[%d]", i.Id))
			}
		}
	}
	check(i.Power1)
	check(i.Power2)
	check(i.BindPower1)
	check(i.BindPower2)
	check(i.Power4)
	check(i.Power5)
	check(i.Power6)
	check(i.Power7)
}

type IdentifyPower struct {
	PowerType MyDefine.POWER `json:"powerType"`
	Min       int            `json:"min"`
	Max       int            `json:"max"`
}

func (i *IdentifyPower) IsValid() bool { return i.PowerType != MyDefine.POWER_NONE }
