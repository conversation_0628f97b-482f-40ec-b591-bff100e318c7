package ModelHelper

import (
	"world/common/pbBase/Job"
	"world/common/pbBase/Sex"
	ut "world/utils"
)

// GetCreateHair 根据职业性别获取发型
func GetCreateHair(job Job.Type, sex Sex.Type) int64 {
	switch job {
	case Job.Ranger:
		return ut.If[int64](sex == Sex.Male, 1, 15)
	case Job.XiuZhen:
		return ut.If[int64](sex == Sex.Male, 8, 3)
	case Job.Warrior:
		return ut.If[int64](sex == Sex.Male, 2, 1)
	case Job.Wizard:
		return ut.If[int64](sex == Sex.Male, 7, 3)
	}
	return 1
}

// GetCreateHairColor 根据职业性别获取发型颜色
func GetCreateHairColor(job Job.Type, sex Sex.Type) int64 {
	switch job {
	case Job.Ranger:
		return ut.If[int64](sex == Sex.Male, 0, 1)
	case Job.XiuZhen:
		return ut.If[int64](sex == Sex.Male, 3, 1)
	case Job.Warrior:
		return ut.If[int64](sex == Sex.Male, 1, 2)
	case Job.Wizard:
		return 0
	}
	return 1
}
