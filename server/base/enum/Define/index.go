package Define

type PlayerEvent int32

const (
	PLAYER_EVENT_TEAM_INVITE       PlayerEvent = 1 // 组队邀请
	PLAYER_EVENT_TEAM_APPLY        PlayerEvent = 2 // 组队申请
	PLAYER_EVENT_PKASK             PlayerEvent = 3 // 决斗申请
	PLAYER_EVENT_JOINCOUNTRYASK    PlayerEvent = 4 // 加入国家申请
	PLAYER_EVENT_ESCORT            PlayerEvent = 5 // 运送任务ask
	PLAYER_EVENT_MASTER            PlayerEvent = 6 // 申请拜师
	PLAYER_EVENT_JOINCOUNTRYHANDLE PlayerEvent = 7 // 加入国家处理
	PLAYER_EVENT_MERRY             PlayerEvent = 8 // 结婚申请
	PLAYER_WEDDING_CARD            PlayerEvent = 9 //
	PLAYER_EVENT_PRENTICE          PlayerEvent = 10
	PLAYER_EVENT_CROSS_TEAM_INVITE PlayerEvent = 11
	PLAYER_EVENT_CROSS_TEAM_JOIN   PlayerEvent = 12
	PLAYER_EVENT_MAIL              PlayerEvent = 100
	PLAYER_EVENT_TOURIST_MODIFY    PlayerEvent = 101
	PLAYER_EVENT_ACTIVITY_LIST     PlayerEvent = 102
	PLAYER_EVENT_CD_CONFIRM        PlayerEvent = 103
)
