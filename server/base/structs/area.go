package structs

import (
	"context"
	"world/common/pbBase"
	"world/common/pbBase/AreaLineState"
	"world/db"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Area struct {
	Id       int                `bson:"id"`       // 区服id,需要创建唯一索引
	Name     string             `bson:"name"`     // 区服名称
	OpenTime int64              `bson:"openTime"` // 开区时间,也是创建时间.
	State    AreaLineState.Type `bson:"state"`    // 区服状态
	Desc     string             `bson:"desc"`     // 区服备注
}

// Save 数据保存
func (a *Area) Save() {
	db.GetCollection(db.AREA).UpdateOne(context.TODO(), &bson.M{
		"id": a.Id,
	}, &bson.M{
		"$set": a,
	}, options.Update().SetUpsert(true))
}

func (a *Area) ToPb() *pbBase.AreaLine {
	return &pbBase.AreaLine{
		Id:       int32(a.Id),
		Name:     a.Name,
		State:    a.State,
		OpenTime: int32(a.OpenTime / 1000),
	}
}
