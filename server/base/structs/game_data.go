package structs

import (
	"context"
	"world/base/enum/GameDataKey"
	"world/db"

	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// GameData 游戏公共数据
type GameData struct {
	Key   string `bson:"key"`   // 键
	Value any    `bson:"value"` // 值
}

// NewGameData 创建一个新的游戏公共数据
func NewGameData(key string, value any) *GameData {
	return &GameData{
		Key:   key,
		Value: value,
	}
}

func InitDefaultPlayerId() {
	data := NewGameData(GameDataKey.PlayerIdSeq, 10000)
	// 利用key唯一性，直接新增
	_, _ = db.COMMON.GetCollection().InsertOne(context.Background(), data)
}
func InitDefaultPetId() {
	data := NewGameData(GameDataKey.PetIdSeq, 10000)
	// 利用key唯一性，直接新增
	_, _ = db.COMMON.GetCollection().InsertOne(context.Background(), data)
}

// GetNext
/*
 * @description 获取下一个自增值
 * @return int
 */
func GetNext(key string) int64 {
	var result GameData
	err := db.COMMON.GetCollection().FindOneAndUpdate(context.Background(),
		bson.M{"key": key},
		bson.M{"$inc": bson.M{"value": 1}},
		options.FindOneAndUpdate().
			SetUpsert(true)).
		Decode(&result)
	if err != nil {
		if mongo.ErrNoDocuments != err {
			return 1
		}
		panic(err)
	}
	return cast.ToInt64(result.Value)
}

func GetNextPetId() int64  { return GetNext(GameDataKey.PetIdSeq) }
func GetNextPlayerId() int { return int(GetNext(GameDataKey.PlayerIdSeq)) }
