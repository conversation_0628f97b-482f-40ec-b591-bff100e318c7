package data

import (
	"crypto/rsa"
	"github.com/golang-jwt/jwt/v4"
	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
	ut "world/utils"
)

// KeyCenter初始化锁
var keyCenterLock deadlock.Once
var keyCenter *KeyCenter

type KeyCenter struct {
	privateKey string
	publicKey  string
	puk        *rsa.PublicKey
	prk        *rsa.PrivateKey
}

// PrivateKey 获取私钥
func (this *KeyCenter) PrivateKey() string {
	return this.privateKey
}

// PublicKey 获取公钥
func (this *KeyCenter) PublicKey() string {
	return this.publicKey
}

func (this *KeyCenter) ParsePublicKey() *rsa.PublicKey {
	pem, _ := jwt.ParseRSAPublicKeyFromPEM([]byte(this.publicKey))
	this.puk = pem
	return this.puk
}

func (this *KeyCenter) ParsePrivateKey() *rsa.PrivateKey {
	pem, _ := jwt.ParseRSAPrivateKeyFromPEM([]byte(this.privateKey))
	this.prk = pem
	return this.prk
}

// GlobalGetKeyCenter 秘钥管理中心
func GlobalGetKeyCenter() *KeyCenter {
	keyCenterLock.Do(func() {
		keyCenter = &KeyCenter{}
		v, err := ut.ReadFileContent("/bin/twomiles.cn.pem")
		if err != nil {
			log.Error("load public key failed.%v", err)
		}
		keyCenter.publicKey = v
		v, err = ut.ReadFileContent("/bin/twomiles.cn.key")
		if err != nil {
			log.Error("load private key failed.%v", err)
		}
		keyCenter.privateKey = v
	})
	return keyCenter
}
