package env

import (
	"encoding/json"
	"fmt"

	"github.com/huyangv/vmqant/conf"
	"github.com/huyangv/vmqant/log"
	"github.com/spf13/cast"
)

var serverConfig *conf.Config
var done bool

var serverTypeStr string // 当前进程服务器类型
var sid int              // 如果是game的话，sid代表区服
var mark int             // 服务器标记

func Init(bytes []byte) {
	if done {
		log.Error("服务器环境已经初始化过了!!!")
		return
	}
	err := json.Unmarshal(bytes, &serverConfig)
	if err != nil {
		panic(fmt.Sprintf("解析server.json出错:%s", err.Error()))
	}
	done = true
}

func IsDebug() bool { return cast.ToBool(GetSetting()["Debug"]) }

func GetConsulUrl() string { return cast.ToString(GetSetting()["ConsulURL"]) }

func GetNatsUrl() string { return cast.ToString(GetSetting()["NatsURL"]) }

func GetSetting() map[string]interface{} { return serverConfig.Settings }

// GetServerTypeStr 获取当前进程服务器类型
func GetServerTypeStr() string { return serverTypeStr }

func SetServerTypeStr(v string) {
	if serverTypeStr != "" {
		log.Error("重复设置serverTypeStr %s ???", serverTypeStr)
		return
	}
	serverTypeStr = v
}

// GetSid 获取当前进程区服
func GetSid() int { return sid }

func SetSid(v int) {
	if sid != 0 {
		log.Error("重复设置sid %d ???", sid)
		return
	}
	sid = v
}

// GetMark 获取当前进程服务器标记
func GetMark() int { return mark }

func SetMark(v int) {
	mark = v
}
