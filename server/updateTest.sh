# bin目录
path="./bin"
# 目标服务器
ip="************"
project_path="$(cd `dirname $0`; cd ../; pwd)"/
remote_server_path="/root/train_test"
server_path=""$project_path"server/"
pem=""$server_path"tw_hotel.pem"
# 需要忽略的文件(夹)列表
# 文件夹只能匹配全字符
# test.js -> 忽略test.js文件
# test. -> 忽略test.*文件
# .json -> 忽略所有.json
ignore=(logs dist server.json *.go)

time=$(date "+%Y%m%d-%H%M%S")
tar_file="tar_${time}.tar.gz"

#归纳文件 因为会遍历ignore列表,所以会存在优先忽略的情况.
_reduce_files(){
    for file in $(ls "$1"); do
      #当前遍历文件的完整路径
      local path="$1/$file"
      # 如果是目录则递归，如果是文件则打印完整路径
      if [ -d "$path" ]; then
        _check_file_name $file
        if [ $? -eq 0 ];then
          _reduce_files "$path"
        fi
      else
        _check_file_name $file
        if [ $? -eq 0 ];then
          #echo $path
          tar -rvf $tar_file $path >/dev/null 2>&1
        fi
      fi
    done
    _check_error
}
_check_file_name(){
  local file=$1 #文件(夹)名
  for ig in ${ignore[@]};do
    local p=${file#*$ig}
    if [[ $p == '' ]];then
      return 1
    fi
    if [[ $p != $1 ]];then
      return 1 #不通过
    fi
  done
  return 0 #通过
}

_do_upload_for_server(){
  _create_tar_package
  tar -rvf $tar_file "train" >/dev/null 2>&1
  _reduce_files $path
  _upload
#  _check_file_name './server/.gitignore'
#  echo $?
}
_upload(){
  # 上传
  sudo scp -r -i "$pem" "$tar_file" root@"$ip":"$remote_server_path"
  # 解压并删除
  cmd="tar xvf "$remote_server_path"/"$tar_file" -C "$remote_server_path" >/dev/null 2>&1 && rm -rf "$remote_server_path"/"$tar_file""
  _exec $ip "$cmd"
  rm -rf $tar_file
}

_create_tar_package(){
  touch $tar_file
  _check_error
}

_exec(){
  ip_=$1
  cmd_=$2
  cmd_str="sudo ssh -i "$pem" root@"$ip_" "$cmd_""
  $cmd_str
}

_check_error(){
  if [ $? != 0 ]; then
    echo '出错了, exit...'
    exit
  fi
}
_showUsage(){
  echo "-s : 打包并上传server"
  exit
}
main(){
    if [[ $1 != "-c" ]]; then
        ignore+=("*.json")
    fi

    #编译
    GOOS=linux GOARCH=amd64 go build
    if [ $? -ne 0 ]; then
       echo "=============== 编译出错 | 结束上传 ==============="
       exit 500
    fi
    #上传
    _do_upload_for_server
    #停止
    _exec $ip "pm2 stop train"
    #重启
    _exec $ip ""$remote_server_path"/reload.sh"

#   if [ $# -ne 1 ]; then
#     _showUsage
#   fi
#   for i in $@; do
#     if [ $i = '-s' ];then
#       _do_upload_for_server
#     fi
#   done
}
main ${*}