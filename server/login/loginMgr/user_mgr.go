package loginMgr

import (
	"world/common/pbBase/LoginMethod"
	"world/common/pbBase/Response"
	"world/login/base"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
	cmap "github.com/orcaman/concurrent-map/v2"
	"github.com/sasha-s/go-deadlock"
)

var userManagerLock deadlock.Once
var userManager *UserManager

// User 全局获取用户管理中心
func User() *UserManager {
	userManagerLock.Do(func() {
		userManager = &UserManager{}
		userManager.userMap = cmap.New[*base.User]()
	})
	return userManager
}

type UserManager struct {
	userMap cmap.ConcurrentMap[string, *base.User] // key是用户id,用户登录后，session会存进来，选区切换到登陆服务后，这里就删除了
}

// HandleUserLogin 用户登录
func (u *UserManager) HandleUserLogin(session gate.Session, method LoginMethod.Type, args map[string]string) (code Response.Code, user *base.User, token string) {
	switch method {
	case LoginMethod.TypeAccount:
		code, user = u.handleAccountLogin(args)
	case LoginMethod.TypeNone:
		fallthrough
	default:
		code = Response.ErrLoginMethod
		return
	}

	if user != nil {
		user.Bind(session)
		token = user.GenToken()
	}
	return
}

// 账号密码登录
func (u *UserManager) handleAccountLogin(args map[string]string) (code Response.Code, user *base.User) {
	if args == nil {
		return Response.ErrUsernamePassword, nil
	}
	username := args["username"]
	password := args["password"]
	user = base.GetUserFromDbByAccount(username, password)
	if user == nil {
		return Response.ErrUsernamePassword, nil
		//if !env.IsDebug() {
		//	return pb.Code_ErrUsernamePassword, nil
		//}
		// 测试环境下直接注册
		//var err error
		//user, err = base.NewUserByUserName(username, password)
		//if err != nil {
		//	log.Error("handleAccountLogin error: %v", err)
		//	return pb.Code_ErrUsernamePassword, nil
		//}
	}
	return Response.NoError, user
}

// AddTmpUser 存放临时user数据
func (u *UserManager) AddTmpUser(user *base.User) {
	if user != nil {
		u.userMap.Set(user.Id, user)
	}
	log.Debug("login userMgr 添加，当前数量:%d", u.userMap.Count())
}

func (u *UserManager) RemoveTmpUserByUid(uid string) {
	if uid != "" {
		u.userMap.Remove(uid)
	}
	log.Debug("login userMgr 移除，当前数量:%d", u.userMap.Count())
}

// RemoveTmpUser 删除临时user数据
func (u *UserManager) RemoveTmpUser(user *base.User) {
	if user != nil {
		u.RemoveTmpUserByUid(user.Id)
	}
}

// GetTmpUser 获取临时user数据
func (u *UserManager) GetTmpUser(uid string) (user *base.User, exists bool) {
	return u.userMap.Get(uid)
}
