package login

import (
	"world/login/handler/login"
	"world/login/handler/node"
)

// InitRpc 自动生成，不要在这个方法添加任何内容。
func InitRpc(this *Login) {
	// 登录时获取公告
	this.middleware.Wrap("C2S_ApplyLoginNoticeMessage", login.C2sApplyLoginNoticeMessageHandler)
	// 客户端版本检查
	this.middleware.Wrap("C2S_ClientCheckMessage", login.C2sClientCheckMessageHandler)
	// 获取区服信息
	this.middleware.Wrap("C2S_GetAreaLinesMessage", login.C2sGetAreaLinesMessageHandler)
	// 登录
	this.middleware.Wrap("C2S_LoginMessage", login.C2sLoginMessageHandler)
	// 选区
	this.middleware.Wrap("C2S_SelectAreaMessage", login.C2sSelectAreaMessageHandler)
	// 节点之间相互通知，使会话失效，登录服使用
	this.middleware.Wrap("S2R_KickSessionByUidMessage", node.S2rKickSessionByUidMessageHandler)
	// game通知login，相关用户在game登录了某个角色，login用来移除user
	this.middleware.Wrap("S2R_RemoveUserByUidMessage", node.S2rRemoveUserByUidMessageHandler)
}
