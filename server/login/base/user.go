package base

import (
	"context"
	"time"
	"world/base/enum"
	comm "world/common"
	"world/db"
	ut "world/utils"

	"github.com/golang-jwt/jwt/v4"
	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// User 用户基础信息
type User struct {
	Session            gate.Session `bson:"-"`                             // 会话通道
	Id                 string       `bson:"-"`                             // 用户id
	CreateTime         int64        `bson:"createTime, omitempty"`         // 创建时间
	UserType           string       `bson:"userType, omitempty"`           // 用户类型，用什么方式登录
	LastLoginTime      int          `bson:"lastLoginTime, omitempty"`      // 上次登录时间
	Username           string       `bson:"username, omitempty"`           // 用户名
	Password           string       `bson:"password, omitempty"`           // 密码
	LastSelectServerId int          `bson:"lastSelectServerId, omitempty"` // 上次选择的服务器id
	ActorCnt           map[int]int  `bson:"actorCnt, omitempty"`           // 各个区服对应的角色数量
}

// Bind 绑定
func (u *User) Bind(session gate.Session) {
	if u.Session == session {
		return
	}
	u.Session = session
	u.Session.Bind(u.GetUid())
	u.Session.Push()
}

// GetAge 获取年龄
func (u *User) GetAge() int32 {
	return 18
}

func (u *User) GetUid() string {
	return u.Id
}

// SaveToDb  更新用户数据到数据库
func (u *User) SaveToDb(v bson.M) error {
	if len(v) == 0 {
		log.Warning("user:%s - SaveToDb,but v is empty :%v", u.GetUid(), v)
		return nil
	}
	sTime := time.Now()
	id, e := ut.NewMongoIdFrom(u.GetUid())
	if e != nil {
		log.Error("错误的user id:%s - SaveToDb:%v", u.GetUid(), e.Error())
		return e
	}
	_, err := db.USER.GetCollection().UpdateOne(context.TODO(), &bson.M{
		"_id": id.ObjectId(),
	}, &bson.M{
		"$set": &v,
	}, options.Update().SetUpsert(false))
	if err != nil {
		log.Error("user:%s - SaveToDb:%v", u.GetUid(), err.Error())
	}
	log.Debug("user:%s - SaveToDb ,during :%fs", u.GetUid(), time.Since(sTime).Seconds())
	return err
}

func NewUserByUserName(username, password string) (*User, error) {
	user := &User{
		Username: username,
		Password: password,
	}
	return NewUser(user)
}

// NewUser 创建新用户 用户名重复判断:mongo.IsDuplicateKeyError(err)
func NewUser(u *User) (*User, error) {
	u.CreateTime = ut.Now()
	u.ActorCnt = make(map[int]int)
	// 插入数据库
	result, err := db.USER.GetCollection().InsertOne(context.TODO(), u)
	if err != nil {
		return nil, err
	}
	id := result.InsertedID.(primitive.ObjectID)
	u.Id = id.Hex()
	if err != nil {
		return nil, err
	}
	log.Info("[%s] signup: %s", u.GetUid())
	return u, nil
}

// GetUserFromDbById 尝试从数据库中根据uid获取一个user,可能返回nil
func GetUserFromDbById(id string) *User {
	if ut.IsEmpty(id) {
		return nil
	}
	user := &User{
		Id: id,
	}
	mongoId, err := ut.NewMongoIdFrom(id)
	if err != nil {
		return nil
	}
	err = db.USER.GetCollection().FindOne(context.TODO(), &bson.M{
		"_id": mongoId.ObjectId(),
	}).Decode(user)
	if err != nil {
		return nil
	}
	return user
}

// GetUserFromDbByAccount 尝试从数据库中根据用户名和密码获取一个user,可能返回nil
func GetUserFromDbByAccount(username, password string) *User {
	if ut.IsEmpty(username) || ut.IsEmpty(password) {
		return nil
	}
	user := &User{}
	res := db.USER.GetCollection().FindOne(context.TODO(), &bson.M{
		"username": username,
		"password": password,
	})
	var result bson.M
	err := res.Decode(&result)
	_ = res.Decode(user)
	if err != nil {
		return nil
	}
	oid := result["_id"].(primitive.ObjectID)
	user.Id = oid.Hex()
	return user
}

// GetUserFromDbByUserName 使用第三方id查询用户
func GetUserFromDbByUserName(username string) *User {
	user := &User{}
	res := db.USER.GetCollection().FindOne(context.TODO(), &bson.M{
		"username": username,
	})
	var result bson.M
	err := res.Decode(&result)
	_ = res.Decode(user)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil
		}
		panic(err)
	}
	oid := result["_id"].(primitive.ObjectID)
	user.Id = oid.Hex()
	return user
}

func (u *User) GenToken() string {
	return comm.GetTokenByRsa(&jwt.MapClaims{
		"exp": time.Now().Add(enum.LoginTokenDuringTime).Unix(),
		"id":  u.GetUid(),
	})
}

// Offline 进行离线操作
func (u *User) Offline() {
	if u.Session != nil {
		u.Session.UnBind()
		u.Session.Push()
		u.Session.Close()
		u.Session = nil
	}
}
