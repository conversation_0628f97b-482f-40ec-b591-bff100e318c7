package login

import (
	"world/base/enum/key"
	"world/base/mgr"
	"world/common/pbBase/Response"
	"world/common/pbLogin"
	ut "world/utils"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/module"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sSelectAreaMessageHandler 选区
func C2sSelectAreaMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(session gate.Session, msg *pbLogin.C2S_SelectAreaMessage) protoreflect.ProtoMessage {
		id := session.GetUserID()
		if ut.IsEmpty(id) {
			// 非法会话
			return &pbLogin.S2C_SelectAreaMessage{
				Code: Response.ErrPleaseLoginFirst,
			}
		}
		aid := cast.ToInt(msg.GetId())
		area := mgr.Area().GetArea(aid)
		if area == nil {
			// 找不到要进入的区服
			return &pbLogin.S2C_SelectAreaMessage{
				Code: Response.ErrAreaLineId,
			}
		}
		// 设置选择的游戏服id
		session.SetPush(key.PlayerSid, cast.ToString(aid))
		return &pbLogin.S2C_SelectAreaMessage{}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
