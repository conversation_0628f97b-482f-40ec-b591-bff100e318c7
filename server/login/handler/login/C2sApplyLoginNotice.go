package login

import (
	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
	"world/common/pbLogin"
)

// C2sApplyLoginNoticeMessageHandler 登录时获取公告
func C2sApplyLoginNoticeMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(session gate.Session, msg *pbLogin.C2S_ApplyLoginNoticeMessage) protoreflect.ProtoMessage {
		return &pbLogin.S2C_ApplyLoginNoticeMessage{Content: "暂无"}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
