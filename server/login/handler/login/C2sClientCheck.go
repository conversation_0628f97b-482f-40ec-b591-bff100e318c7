package login

import (
	"time"
	"world/common/pbBase/Response"
	"world/common/pbLogin"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sClientCheckMessageHandler 客户端版本检查
func C2sClientCheckMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(session gate.Session, msg *pbLogin.C2S_ClientCheckMessage) protoreflect.ProtoMessage {
		return &pbLogin.S2C_ClientCheckMessage{
			Code:       Response.NoError,
			ServerTime: time.Now().UnixNano() / 1e6,
		}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
