package node

import (
	"world/common/pbCross"
	"world/common/pbGame"
	"world/common/pb_helper"
	"world/common/router"
	"world/login/loginMgr"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// S2rKickSessionByUidMessageHandler 节点之间相互通知，使会话失效，登录服使用
func S2rKickSessionByUidMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(session gate.Session, msg *pbCross.S2R_KickSessionByUidMessage) protoreflect.ProtoMessage {
		uid := msg.GetUid()
		user, exists := loginMgr.User().GetTmpUser(uid)
		if exists {
			// 断开会话 并且通知
			if user.Session != nil {
				user.Session.SendNR(router.S2CLogoutMessage, pb_helper.ProtoMarshalForce(&pbGame.S2C_LogoutMessage{Code: 1}))
			}
			user.Offline()
			loginMgr.User().RemoveTmpUser(user)
		}
		return &pbCross.R2S_KickPlayerForceMessage{
			Is: exists,
		}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
