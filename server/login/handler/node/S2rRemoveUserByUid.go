package node

import (
	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
	"world/common/pbCross"
	"world/login/loginMgr"
)

// S2rRemoveUserByUidMessageHandler game通知login，相关用户在game登录了某个角色，login用来移除user
func S2rRemoveUserByUidMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(msg *pbCross.S2R_RemoveUserByUidMessage) protoreflect.ProtoMessage {
		uid := msg.GetUid()
		// 移除登录临时用户信息
		loginMgr.User().RemoveTmpUserByUid(uid)
		return nil
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
