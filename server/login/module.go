package login

import (
	"world/base/enum/NodeState"
	"world/base/enum/key"
	"world/login/loginMgr"
	"world/net"

	mqrpc "github.com/huyangv/vmqant/rpc"

	"github.com/gin-gonic/gin"
	"github.com/huyangv/vmqant/conf"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	basemodule "github.com/huyangv/vmqant/module/base"
	"github.com/huyangv/vmqant/server"
)

var Module = func() module.Module {
	return new(Login)
}

type Login struct {
	basemodule.BaseModule
	middleware *net.Middleware
	httpServer *gin.Engine
}

func (this *Login) GetType() string {
	return "login" //很关键,需要与配置文件中的Module配置对应
}

func (this *Login) Version() string {
	return "1.0.0" //可以在监控时了解代码版本
}

// 最低支持的客户端版本
func (this *Login) GetMinClientVersion() string {
	return "0.0.0"
}

// OnAppConfigurationLoaded 当应用配置加载完成时调用
func (this *Login) OnAppConfigurationLoaded(app module.App) {
	this.BaseModule.OnAppConfigurationLoaded(app)
}

func (this *Login) OnInit(app module.App, settings *conf.ModuleSettings) {
	this.BaseModule.OnInit(this, app, settings, func(op *server.Options) {
		op.Metadata = map[string]string{
			key.MinClientVersion: this.GetMinClientVersion(),
		}
	})
	// 作为集群启动
	//if script.IsClusterMod() {
	//	this.GetServer().Options().Metadata["cluster"] = cast.ToString(script.Cluster)
	//}
	loginMgr.User()
	this.middleware = net.Create(this)
	InitRpc(this)
}

func (this *Login) Run(closeSig chan bool) {
	//run
	<-closeSig
	log.Info("%v模块已停止 正在保存信息...", this.GetType())
	this.Offline()
	//stop
}

func (this *Login) OnDestroy() {
	this.BaseModule.OnDestroy()
}

func (this *Login) GetModuleServer() server.Server {
	return this.GetServer()
}
func (this *Login) GetRpcServer() mqrpc.RPCServer {
	return this.GetServer().GetRpcServer()
}

func (this *Login) Offline() {
	this.setMeta(key.NodeState, NodeState.Offline)
}

func (this *Login) setMeta(key string, value string) {
	this.GetServer().Options().Metadata["state"] = NodeState.Offline
	this.GetServer().ServiceRegister()
}
