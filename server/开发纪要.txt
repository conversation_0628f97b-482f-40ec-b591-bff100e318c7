1 服务器架构
    1）必然需要实现高可用性，单区服可以通过增加游戏服务节点的方式实现横向滚动扩展，这样就能实现单区数万人的服务器。
    为了节约服务器成本，那必要需要使用分布式架构，通过多台服务器组成集群，实现负载均衡，提高服务器利用率，所以设计上登录服和游戏服务，网关服务三大
    基础组件都是支持分布式的。
    2）由于golang没有成熟的热更新技术，使用分布式的方式可以实现滚筒重启更新game服务器，其优点也是如此，缺点也很明显，对于gate和login无状态，无需进行同步。
    但是对于game服务器，游戏的即时回合制多人在线玩法，就需要频繁的进行game节点通信，以保持数据正确性。

2 地图，玩家始终处于某个地图中，组队和战斗必然在同一个地图，并且地图较多。
    1）如果每个地图占用一个game节点，可以让玩家在同一节点，会简单很多，但是如果一个地图人很多会导致服务器资源难以承受，这个时候仍然需要对game节点进行横向扩容？
    2）考虑将地图做成协程，但是这样的话，又会出现同一个地图的玩家，可能处于不同的协程，需要进行大量的数据同步通信。
    3）无地图机制，每个game节点都维持一个map数据，用来保存玩家在对应的地图id，中心服也维持一个map数据。
3 组队



4 背包设计，暂时参考小程序的设计
   1）初始免费60。
   2）最多可扩充至600，即付费扩充540格。
   3）免费仓库60。
   4）vip仓库初始拥有90格。
   5）从vip7开始，到vip9，每个等级额外提高12格。
   6）高级vip仓库vip5开放，从vip5开始，到vip9，每个等级额外获得20格。


5 宠物
   1）初始成长值和领悟值的公式：初始值 = 基础最小值 + 随机部分，暂定基础最小值是5(也就是0.5星)
      随机部分如果使用均匀分布(random(0, max - 5))，每个值出现概率相同，可能导致极端值出现频率过高。
      所以暂定使用“加权随机（偏向中间值）” ： 初始值 = 5 + (max - 5) * ((random1 + random2) / 2)，random1和random2是两个独立的0到1之间的均匀分布随机数。
   2）成长值与成长经验算法：根据成长值作为概率，第一次成功则获得3点，失败则再次判定，成功获得2点，否则获得1点。
   3）领悟值与领悟经验算法：类似成长值




