package event

import "world/game/gameBase/gameStruct"

// EvtAfterAcceptTask 领取任务之后
const EvtAfterAcceptTask = "AfterAcceptTask"

type IAfterAcceptTask interface {

	// AfterAcceptTask 领取任务之后
	//
	// Parameters:
	//   - plr *gameStruct.Player
	//   - task *gameStruct.Task 领取的任务
	AfterAcceptTask(plr *gameStruct.Player, task *gameStruct.Task)
}

// EvtAfterBattle 战斗结束后
const EvtAfterBattle = "AfterBattle"

type IAfterBattle interface {

	// AfterBattle 战斗结束
	//
	// Parameters:
	//   - plr *gameStruct.Player
	//   - battleGroupId int 战斗id
	AfterBattle(plr *gameStruct.Player, battleGroupId int)
}

// EvtAfterEquipChanged 装备改变
const EvtAfterEquipChanged = "AfterEquipChanged"

type IAfterEquipChanged interface {

	// AfterEquipChanged 装备改变
	//
	// Parameters:
	//   - plr *gameStruct.Player
	AfterEquipChanged(plr *gameStruct.Player, itemId int)
}
