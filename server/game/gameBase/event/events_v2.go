package event

import (
	"context"
	"reflect"
	"time"

	"github.com/sasha-s/go-deadlock"

	"github.com/huyangv/vmqant/log"
)

type evtKey string

var lock deadlock.RWMutex
var evts = &evt{
	runner: make([]interface{}, 0),
}

type evt struct {
	runner []interface{}
}

func (e *evt) push(v interface{}) {
	e.runner = append(e.runner, v)
}

// Require 注册监听脚本,注册后有相关的调用call就会被同步拉起,不要注册不相关的数据类型,因为没加入接口约束,默认超时时间1s
func Require(runner interface{}) bool {
	return RequireWithTimeOut(runner, time.Second*1)
}

func RequireWithTimeOut(runner interface{}, timeout time.Duration) bool {
	lock.RLock()
	defer lock.RUnlock()

	v := reflect.TypeOf(runner)
	// 如果值是指针就需要转换
	if v.Kind() == reflect.Pointer {
		// 获取指针指向的对象值
		v = v.<PERSON>em()
	}
	if v.Kind() != reflect.Struct {
		log.Error("注册了一个非结构体")
		return false
	}

	evts.push(runner)
	return true
}

// Dispatch 无法感知运行结果并且有超时控制,所以不要阻塞监听器逻辑
func Dispatch(funName evtKey, args ...interface{}) {
	// sTime := time.Now()
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*1)
	go func() {
		defer cancel()
		for _, base := range evts.runner {
			v := reflect.TypeOf(base)
			// 如果值是指针就需要转换
			if v.Kind() == reflect.Pointer {
				// 获取指针指向的对象值
				v = v.Elem()
			}
			if v.Kind() != reflect.Struct {
				return
			}
			obj := reflect.ValueOf(base)
			method := obj.MethodByName(string(funName))
			if method.IsValid() {
				inputs := make([]reflect.Value, len(args))
				for i, _ := range args {
					inputs[i] = reflect.ValueOf(args[i])
				}
				method.Call(inputs)
			}
		}
		return
	}()
	// 等待完成或者超时
	<-ctx.Done()
	if err := ctx.Err(); err != nil && err.Error() == "context deadline exceeded" {
		log.Error("events_v2 Call ：执行超时,funName: %s", funName)
	}
	// log.Debug("events_v2 Call: %s - during :%fs", funName, time.Since(sTime).Seconds())
}
