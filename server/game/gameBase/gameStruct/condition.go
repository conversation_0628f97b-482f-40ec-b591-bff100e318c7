package gameStruct

import (
	"world/common/pbBase"
	"world/base/cfg"
	"world/common/pbBase/ConditionType"

	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

// NewCondition 创建条件
func NewCondition(typ ConditionType.Type, id int, num int) *Condition {
	return NewEmptyCondition().Init(typ, id, num)
}

// NewEmptyCondition 创建空条件
func NewEmptyCondition() *Condition {
	return &Condition{
		typ: ConditionType.None,
	}
}

type Condition struct {
	typ   ConditionType.Type `marshal:"type,omitempty"` // 条件类型
	id    int                `marshal:"id,omitempty"`
	num   int                `marshal:"num,omitempty"`
	extra string             `marshal:"extra,omitempty"`
}

// Init
/*
 * @description 使用新数据设置条件
 * @param typ
 * @param id
 * @param num
 * @return *Condition
 */
func (c *Condition) Init(typ ConditionType.Type, id int, num int) *Condition {
	c.typ = typ
	c.id = id
	c.num = num
	return c
}

// SetType 设置条件类型
func (c *Condition) SetType(typ ConditionType.Type) *Condition { c.typ = typ; return c }

// SetId 设置条件id
func (c *Condition) SetId(id int) *Condition { c.id = id; return c }

// SetNum 设置条件数量
func (c *Condition) SetNum(num int) *Condition { c.num = num; return c }

// SetExtra 设置额外信息
func (c *Condition) SetExtra(extra string) *Condition { c.extra = extra; return c }

func (c *Condition) GetType() ConditionType.Type { return c.typ }
func (c *Condition) GetId() int                  { return c.id }
func (c *Condition) GetNum() int                 { return c.num }
func (c *Condition) GetExtra() string            { return c.extra }

// 是不是需要记录的条件类型
func (c *Condition) isNeedRecord() bool { return c.typ == ConditionType.KillMonster }

func (c *Condition) ToPb() *pbBase.Condition {
	return &pbBase.Condition{
		Type:  c.typ,
		Id:    int32(c.id),
		Num:   int32(c.num),
		Extra: c.extra,
	}
}

// MergeConditions
/*
 * @description 合并condition
 * @param orgCons 原始
 * @param newConds 新加
 * @return []*Condition
 */
func MergeConditions(orgCons []*Condition, newConds []*Condition) []*Condition {
	if orgCons == nil {
		orgCons = make([]*Condition, 0)
	}
	for _, cond := range newConds {
		c, _ := lo.Find(orgCons, func(c *Condition) bool {
			return c.typ == cond.typ && cast.ToString(c.id) == cast.ToString(cond.id)
		})
		if c == nil {
			orgCons = append(orgCons, &Condition{
				typ: cond.typ,
				id:  cond.id,
				num: cond.num,
			})
		} else {
			c.num += cond.num
		}
	}
	return orgCons
}

// isNeedRecord 是否需要记录
//
// Parameters:
//   - typ ConditionType.Type
//
// Returns:
//   - bool
func isNeedRecord(typ ConditionType.Type) bool {
	if typ == ConditionType.KillMonster {
		return true
	}
	return false
}

// ConfigConditionConvert
/*
 * @description 用于转换配置数据中的ConfigCondition
 * @param inputConfigConditions
 * @return *configConditionConvertResult
 */
func ConfigConditionConvert(inputConfigConditions ...*cfg.ConfigCondition) *configConditionConvertResult {
	conditions := make([]*Condition, 0)
	for _, inputConfigCondition := range inputConfigConditions {
		typ := ConditionType.Type(inputConfigCondition.Type)
		if typ == ConditionType.None {
			log.Error("未知的条件类型！")
		}
		num := inputConfigCondition.Num
		conditions = MergeConditions(conditions, []*Condition{
			{
				id:  inputConfigCondition.Id,
				num: num,
				typ: typ,
			},
		})
	}
	return &configConditionConvertResult{conditions: conditions}
}

// RewardItemConvert 用于配置中，将 RewardItem 转换成 Condition (类型只会是物品)
//
// Parameters:
//   - inputRewardItems ...*cfg.RewardItem
//
// Returns:
//   - *configConditionConvertResult
func RewardItemConvert(inputRewardItems ...*cfg.RewardItem) *configConditionConvertResult {
	conditions := make([]*Condition, 0)
	for _, inputRewardItem := range inputRewardItems {
		conditions = MergeConditions(conditions, []*Condition{
			{
				id:  inputRewardItem.Id,
				num: inputRewardItem.Quantity,
				typ: ConditionType.HaveItem,
			},
		})
	}
	return &configConditionConvertResult{conditions: conditions}
}

type configConditionConvertResult struct {
	conditions []*Condition
}

// One 获取单个结果 如果有多个结果则返回第一个
func (c *configConditionConvertResult) One() *Condition {
	if len(c.conditions) == 0 {
		return nil
	}
	return c.conditions[0]
}

// All 获取所有结果
func (c *configConditionConvertResult) All() []*Condition {
	return c.conditions
}
