package gameStruct

import (
	"world/common/pbBase"
	"world/base/cfg"
	"world/common/pbBase/ConditionType"
	ut "world/utils"

	"github.com/samber/lo"
)

func NewTask(id int) *Task { return &Task{id: id, acceptedTime: ut.Now()} }

type Task struct {
	id           int   `marshal:"id,omitempty"`           // 任务id
	acceptedTime int64 `marshal:"acceptedTime,omitempty"` // 领取时间
}

func (t *Task) GetId() int             { return t.id }
func (t *Task) GetAcceptedTime() int64 { return t.acceptedTime }

// GetCfg 获取配置
func (t *Task) GetCfg() *cfg.Mission[int] {
	bean, _ := cfg.ContainerMission.GetBeanByUnique(t.id)
	return bean
}

// GetNeedRecordCondition
/*
 * @description 获取任务配置中需要进行单独记录的条件
 * @return []*cfg.ConfigCondition
 */
func (t *Task) GetNeedRecordCondition() []*cfg.ConfigCondition {
	bean := t.GetCfg()
	if bean == nil {
		return nil
	}
	if bean.SubmitCondition == nil || len(bean.SubmitCondition) == 0 {
		return nil
	}

	filter := lo.Filter(bean.SubmitCondition, func(c *cfg.ConfigCondition, i int) bool {
		return isNeedRecord(ConditionType.Type(c.Type))
	})
	if len(filter) == 0 {
		return nil
	}
	return filter
}

// HasNeedRecordCondition
/*
 * @description 任务有没有需要进行单独记录的条件
 * @return []*cfg.ConfigCondition
 */
func (t *Task) HasNeedRecordCondition() bool {
	return t.GetNeedRecordCondition() != nil
}

func (t *Task) ToPb(taskModule *TaskModule) *pbBase.TaskInfo {
	info := &pbBase.TaskInfo{
		Id: int32(t.id),
	}
	filter := t.GetNeedRecordCondition()
	if len(filter) > 0 {
		list := make([]*pbBase.Condition, 0)
		result := ConfigConditionConvert(filter...)
		for _, condition := range result.All() {
			switch condition.GetType() {
			// 杀怪任务的记录数据
			case ConditionType.KillMonster:
				val := taskModule.GetKillRecord(condition.GetId())
				condition.SetNum(val)
			}
			list = append(list, condition.ToPb())
		}
		info.Cond = list
	}
	return info
}
