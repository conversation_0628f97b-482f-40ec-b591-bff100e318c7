package gameStruct

import (
	"world/common/pbBase"
	"world/common/pbBase/MyDefine"
	"world/common/pbBase/SKILL_TYPE"
	ut "world/utils"

	"github.com/samber/lo"
)

func NewSkillModule(sp, cnt int) *SkillModule {
	return &SkillModule{
		sp:                sp,
		list:              make(map[int]*Skill),
		cnt:               cnt,
		formationSkill:    nil,
		activeAutoSkillID: -1,
		autoSkillID:       make([]int, 4),
	}
}

func NewSkillModuleByPb(skillPb *pbBase.SkillData) *SkillModule {
	if skillPb == nil {
		return nil
	}
	list := skillPb.GetList()
	newMap := make(map[int]*Skill)
	for k, v := range list {
		newMap[int(k)] = NewSkillByPb(v)
	}
	return &SkillModule{
		sp:                int(skillPb.GetSp()),
		list:              newMap,
		cnt:               int(skillPb.GetCnt()),
		formationSkill:    nil,
		activeAutoSkillID: int(skillPb.GetActiveAutoSkillId()),
		autoSkillID:       ut.ToInt(skillPb.GetAutoSkillId()),
	}
}

type SkillModule struct {
	GameModule        `marshal:"-"`
	sp                int            `marshal:"sp,omitempty"`                // 未使用的技能点
	list              map[int]*Skill `marshal:"list,omitempty"`              // 技能列表
	cnt               int            `marshal:"cnt,omitempty"`               // 可学习技能数量
	formationSkill    *Skill         `marshal:"formationSkill,omitempty"`    // 阵法技能
	activeAutoSkillID int            `marshal:"activeAutoSkillId,omitempty"` // 自动释放的主动技能id
	autoSkillID       []int          `marshal:"autoSkillId,omitempty"`       // 自动释放的自动技能id
}

func (s *SkillModule) GetSp() int                { return s.sp }
func (s *SkillModule) GetCnt() int               { return s.cnt }
func (s *SkillModule) GetList() map[int]*Skill   { return s.list }
func (s *SkillModule) GetFormationSkill() *Skill { return s.formationSkill }
func (s *SkillModule) GetActiveAutoSkillID() int { return s.activeAutoSkillID }
func (s *SkillModule) GetAutoSkillID() []int     { return s.autoSkillID }
func (s *SkillModule) GetSkill(id int) *Skill    { return s.list[id] }

func (s *SkillModule) AddSp(sp int) *SkillModule                { s.sp += sp; return s }
func (s *SkillModule) AddSkill(skill *Skill) *SkillModule       { s.list[skill.id] = skill; return s }
func (s *SkillModule) RemoveSkill(id int) *SkillModule          { delete(s.list, id); return s }
func (s *SkillModule) SetActiveAutoSkillID(id int) *SkillModule { s.activeAutoSkillID = id; return s }

// GetSkillById 使用id获取技能
//
// Parameters:
//   - id int
//
// Returns:
//   - *Skill
func (s *SkillModule) GetSkillById(id int) *Skill { return s.list[id] }

// GetFreeSlot 获取空闲技能槽位
//
// Returns:
//   - int
func (s *SkillModule) GetFreeSlot() int {
	return s.cnt - lo.CountBy(lo.Values(s.list), func(v *Skill) bool { return v.isLearnByBook })
}

// ToPb 转换成pb数据
//
// Returns:
//   - *pbBase.SkillData
func (s *SkillModule) ToPb() *pbBase.SkillData {
	if s == nil {
		return nil
	}
	list := make(map[int32]*pbBase.SkillInfo)
	for k, v := range s.list {
		list[int32(k)] = v.ToPb()
	}
	// 兼容
	if s.activeAutoSkillID == 0 {
		s.activeAutoSkillID = -1
	}
	if len(s.autoSkillID) == 0 {
		s.autoSkillID = make([]int, 4)
	}
	return &pbBase.SkillData{
		Sp:                int32(s.sp),
		List:              list,
		Cnt:               int32(s.cnt),
		ActiveAutoSkillId: int32(s.activeAutoSkillID),
		AutoSkillId:       ut.ToInt32(s.autoSkillID),
	}
}

// GetSkillPowerValue 获取某个属性类型的技能增加
//
// Parameters:
//   - typ SKILL_TYPE.SKILL_TYPE 技能类型
//   - powerType MyDefine.POWER 属性定义
//
// Returns:
//   - int
func (s *SkillModule) GetSkillPowerValue(typ SKILL_TYPE.SKILL_TYPE, powerType MyDefine.POWER) int {
	val := 0
	if len(s.list) == 0 {
		return val
	}
	for _, v := range s.list {
		if v != nil && v.GetJson().Type == typ {
			val += v.GetPowerValue(powerType)
		}
	}
	return 0 | val
}

// SetAutoSkillID 设置自动释放的技能 存在则取消 不存在则设置
//
// Parameters:
//   - id int
//
// Returns:
//   - bool 自动技能最多设置4个，超过就失败
func (s *SkillModule) SetAutoSkillID(id int) bool {
	_, index, exists := lo.FindIndexOf(s.autoSkillID, func(v int) bool { return v == id })
	if exists {
		s.autoSkillID[index] = 0
		return true
	}
	for i, v := range s.autoSkillID {
		if v == 0 {
			s.autoSkillID[i] = id
			return true
		}
	}
	return false
}
