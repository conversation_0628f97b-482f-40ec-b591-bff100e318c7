package gameStruct

import (
	"fmt"
	"world/base/cfg"
	"world/common/pbBase"
	"world/common/pbBase/MyDefine"
	ut "world/utils"

	"github.com/samber/lo"
)

func NewSkill(id, baseLevel, addLevel int, isLearnByBook bool) *Skill {
	return &Skill{id: id, baseLevel: baseLevel, addLevel: addLevel, isLearnByBook: isLearnByBook}
}

func NewSkillByPb(skillPb *pbBase.SkillInfo) *Skill {
	if skillPb == nil {
		return nil
	}
	return &Skill{
		id:            int(skillPb.GetId()),
		baseLevel:     int(skillPb.GetBaseLevel()),
		addLevel:      int(skillPb.GetAddLevel()),
		isLearnByBook: skillPb.GetIsLearn(),
	}
}

type Skill struct {
	id            int  `marshal:"id,omitempty"`
	baseLevel     int  `marshal:"baseLevel,omitempty"`     // 这个增加等级通常是装备附加的
	addLevel      int  `marshal:"addLevel,omitempty"`      // 这个增加等级通常是装备附加的
	isLearnByBook bool `marshal:"isLearnByBook,omitempty"` // 是不是学习而来 主要是针对宠物技能书
}

func (s *Skill) GetId() int          { return s.id }
func (s *Skill) GetBaseLevel() int   { return s.baseLevel }
func (s *Skill) GetAddLevel() int    { return s.addLevel }
func (s *Skill) IsLearnByBook() bool { return s.isLearnByBook }

func (s *Skill) SetBaseLevel(level int) *Skill   { s.baseLevel = level; return s }
func (s *Skill) AddBxLevel(level int) *Skill     { s.baseLevel += level; return s }
func (s *Skill) AddExLevel(level int) *Skill     { s.addLevel += level; return s }
func (s *Skill) SetIsLearnByBook(is bool) *Skill { s.isLearnByBook = is; return s }

// IsCanLearn 判断技能是否可以学习 主要是看超出配置等级没
func (s *Skill) IsCanLearn() bool {
	if !s.isLearnByBook {
		return false
	}
	return s.GetJson(true).ConfigMaxLevel > s.baseLevel
}

// GetJson 获取技能配置
//
// Parameters:
//   - ignoreAdd bool 是否忽略额外增加的等级,通常情况下这个值是false
//
// Returns:
//   - *cfg.Skill[string]
func (s *Skill) GetJson(ignoreAdd ...bool) *cfg.Skill[string] {
	ignore := false
	if len(ignoreAdd) > 0 {
		ignore = ignoreAdd[0]
	}
	maxLv := cfg.GetSkillConfigMaxLevel(s.id)
	lv := s.baseLevel + lo.If(ignore, 0).Else(s.addLevel)
	lv = ut.Min(maxLv, lv)
	bean, _ := cfg.ContainerSkill.GetBeanByUnique(fmt.Sprintf("%d-%d", s.id, lv))
	return bean
}

// ToPb 转换成pb数据
//
// Returns:
//   - *pbBase.SkillInfo
func (s *Skill) ToPb() *pbBase.SkillInfo {
	if s == nil {
		return nil
	}
	return &pbBase.SkillInfo{
		Id:        int32(s.id),
		BaseLevel: int32(s.baseLevel),
		AddLevel:  int32(s.addLevel),
		IsLearn:   s.isLearnByBook,
	}
}

// GetPowerValue 获取技能的某个属性值
//
// Parameters:
//   - powerType MyDefine.POWER 属性类型
//
// Returns:
//   - int
func (s *Skill) GetPowerValue(powerType MyDefine.POWER) int {
	val := 0
	json := s.GetJson()
	if json.Power1 == powerType {
		val += json.PowerValue1
	}
	if json.Power2 == powerType {
		val += json.PowerValue2
	}
	if json.Power3 == powerType {
		val += json.PowerValue3
	}
	return val
}
