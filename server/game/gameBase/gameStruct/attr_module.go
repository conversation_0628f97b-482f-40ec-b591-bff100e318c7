package gameStruct

import (
	"world/base/cfg"
	"world/common/pbBase"
	"world/common/pbBase/Camp"
	"world/common/pbBase/Job"
	"world/common/pbBase/ModelConst"
	"world/common/pbBase/Sex"
)

func EmptyAttrModule() *AttrModule { return &AttrModule{} }

// 从pb中创建
func NewAttrModuleByPb(attrPb *pbBase.AttrData) *AttrModule {
	if attrPb == nil {
		return nil
	}
	return &AttrModule{
		icon1:    attrPb.GetIcon1(),
		icon2:    attrPb.GetIcon2(),
		icon3:    attrPb.GetIcon3(),
		status:   attrPb.GetStatus(),
		level:    int(attrPb.GetLevel()),
		level2:   int(attrPb.GetLevel2()),
		exp:      int(attrPb.GetExp()),
		exp2:     int(attrPb.GetExp2()),
		hp:       int(attrPb.GetHp()),
		mp:       int(attrPb.GetMp()),
		vipLv:    int(attrPb.GetVipLv()),
		vipLvMax: int(attrPb.GetVipLvMax()),
		cp:       int(attrPb.GetCp()),
		str:      int(attrPb.GetStr()),
		agi:      int(attrPb.GetAgi()),
		con:      int(attrPb.GetCon()),
		ilt:      int(attrPb.GetIlt()),
		wis:      int(attrPb.GetWis()),
	}
}

type AttrModule struct {
	GameModule       `marshal:"-"`
	icon1            int64        `marshal:"icon1,omitempty"`    // icon1
	icon2            int64        `marshal:"icon2,omitempty"`    // icon2
	icon3            int64        `marshal:"icon3,omitempty"`    // icon3
	status           int64        `marshal:"status,omitempty"`   // 状态数据
	level            int          `marshal:"level,omitempty"`    // 等级
	level2           int          `marshal:"level2,omitempty"`   // 传奇等级
	exp              int          `marshal:"exp,omitempty"`      // 普通经验
	exp2             int          `marshal:"exp2,omitempty"`     // 传奇经验
	hp               int          `marshal:"hp,omitempty"`       // 当前血
	mp               int          `marshal:"mp,omitempty"`       // 当前蓝
	vipLv            int          `marshal:"vipLv,omitempty"`    // 当前vip等级
	vipLvMax         int          `marshal:"vipLvMax,omitempty"` // 历史最高vip等级
	cp               int          `marshal:"cp,omitempty"`       // 未分配的属性点
	str              int          `marshal:"str,omitempty"`      // 力量
	agi              int          `marshal:"agi,omitempty"`      // 敏捷
	con              int          `marshal:"con,omitempty"`      // 体质
	ilt              int          `marshal:"ilt,omitempty"`      // 智力
	wis              int          `marshal:"wis,omitempty"`      // 感知
	hpMax            int          `marshal:"-"`                  // 最大血
	mpMax            int          `marshal:"-"`                  // 最大蓝
	power            *cfg.Power   `marshal:"-"`                  //
	titlePower1      *cfg.Power   `marshal:"-"`                  // 称号属性1
	titlePower2      *cfg.Power   `marshal:"-"`                  // 称号属性2
	fightPowerList   []*cfg.Power `marshal:"-"`                  //
	speed            int          `marshal:"-"`                  // 出手速度
	atkTime          int          `marshal:"-"`                  // 攻击次数
	atkStr           int          `marshal:"-"`                  // 劈砍攻击力
	atkAgi           int          `marshal:"-"`                  // 穿刺攻击力
	atkMagic         int          `marshal:"-"`                  // 魔法攻击力
	defStr           int          `marshal:"-"`                  // 劈砍防御力
	defAgi           int          `marshal:"-"`                  // 穿刺防御力
	defMagic         int          `marshal:"-"`                  // 魔法防御力
	dodge            int          `marshal:"-"`                  // 闪避
	hitRate          int          `marshal:"-"`                  // 命中
	hitMagic         int          `marshal:"-"`                  // 魔法命中
	critical         int          `marshal:"-"`                  // 暴击
	forceHit         int          `marshal:"-"`                  // 强制命中
	wil              int          `marshal:"-"`                  // 状态抵抗
	tough            int          `marshal:"-"`                  // 伤害减免
	block            int          `marshal:"-"`                  // 格挡
	brkArmor         int          `marshal:"-"`                  // 破甲
	magicPenetration int          `marshal:"-"`                  // 魔法穿透
	insight          int          `marshal:"-"`                  // 洞察
	defField         int          `marshal:"-"`                  // 法力护盾
	back             int          `marshal:"-"`                  // 反伤
	magicBack        int          `marshal:"-"`                  // 魔法反伤
	lifeAbsorption   int          `marshal:"-"`                  // 生命吸收
	manaAbsorption   int          `marshal:"-"`                  // 法力吸收
	healRecovery     int          `marshal:"-"`                  // 生命恢复
	manaRecovery     int          `marshal:"-"`                  // 法力恢复
	ignoreBack       int          `marshal:"-"`                  // 忽视反伤
	ignoreMagicBack  int          `marshal:"-"`                  // 忽视魔法反伤
	ignoreBlock      int          `marshal:"-"`                  // 忽视格挡
	ignoreInsight    int          `marshal:"-"`                  // 忽视洞察
	ignoreWil        int          `marshal:"-"`                  // 忽视意志
	ignoreTouch      int          `marshal:"-"`                  // 无视伤害减免
	ignoreCritical   int          `marshal:"-"`                  // 忽视暴击
	keepOutAtkTime   int          `marshal:"-"`                  // 免伤护盾
	criticalDmg      int          `marshal:"-"`                  // 暴击伤害
	recovery         int          `marshal:"-"`                  // 恢复
	argo             int          `marshal:"-"`                  // 仇恨值
}

// ToSimplePb
/*
 * @description  只返回重要信息
 * @return *pbBase.AttrData
 */
func (a *AttrModule) ToSimplePb() *pbBase.AttrData {
	return &pbBase.AttrData{
		Icon1:  a.icon1,
		Icon2:  a.icon2,
		Icon3:  a.icon3,
		Status: a.status,
		Level:  int32(a.level),
		VipLv:  int32(a.vipLv),
	}
}

// ToPb
/*
 * @description 转换为pb
 * @return *pbBase.AttrData
 */
func (a *AttrModule) ToPb() *pbBase.AttrData {
	if a == nil {
		return nil
	}
	data := a.ToSimplePb()
	data.Exp = int32(a.exp)
	data.Exp2 = int32(a.exp2)
	data.Level2 = int32(a.level2)
	data.Cp = int32(a.cp)
	data.Str = int32(a.str)
	data.Agi = int32(a.agi)
	data.Con = int32(a.con)
	data.Ilt = int32(a.ilt)
	data.Wis = int32(a.wis)
	data.Hp = int32(a.hp)
	data.Mp = int32(a.mp)
	return data
}

// SetSex 设置性别
func (a *AttrModule) SetSex(sex Sex.Type) *AttrModule {
	val := int64(sex)
	lenSex := int64(ModelConst.LEN_SEX)
	offsetSex := int64(ModelConst.OFFSET_SEX)
	a.icon1 &= ^(lenSex << offsetSex)
	a.icon1 |= (val & lenSex) << offsetSex
	return a
}

// SetJob 设置职业
func (a *AttrModule) SetJob(job Job.Type) *AttrModule {
	val := int64(job)
	lenJob := int64(ModelConst.LEN_JOB)
	offsetJob := int64(ModelConst.OFFSET_JOB)
	a.icon1 &= ^(lenJob << offsetJob)
	a.icon1 |= (val & lenJob) << offsetJob
	return a
}

// GetJob 获取职业
func (a *AttrModule) GetJob() int64 {
	return a.icon1 >> int64(ModelConst.OFFSET_JOB) & int64(ModelConst.LEN_JOB)
}

// SetRace 设置脸型
func (a *AttrModule) SetRace(race Camp.Type) *AttrModule {
	val := int64(race)
	lenRace := int64(ModelConst.LEN_RACE)
	offsetRace := int64(ModelConst.OFFSET_RACE)
	a.icon1 &= ^(lenRace << offsetRace)
	a.icon1 |= (val & lenRace) << offsetRace
	return a
}

// SetHand 设置手样式
func (a *AttrModule) SetHand(hand int64) *AttrModule {
	lenHandStyle := int64(ModelConst.LEN_HAND_STYLE)
	offsetHandStyle := int64(ModelConst.OFFSET_HAND_STYLE)
	a.icon1 &= ^(lenHandStyle << offsetHandStyle)
	a.icon1 |= (hand & lenHandStyle) << offsetHandStyle
	return a
}

// SetFeet 设置脚样式
func (a *AttrModule) SetFeet(feet int64) *AttrModule {
	lenFeetStyle := int64(ModelConst.LEN_FEET_STYLE)
	offsetFeetStyle := int64(ModelConst.OFFSET_FEET_STYLE)
	a.icon1 &= ^(lenFeetStyle << offsetFeetStyle)
	a.icon1 |= (feet & lenFeetStyle) << offsetFeetStyle
	return a
}

// SetHairStyle 设置头发样式
func (a *AttrModule) SetHairStyle(hair int64) *AttrModule {
	lenHairStyle := int64(ModelConst.LEN_HAIR_STYLE)
	offsetHairStyle := int64(ModelConst.OFFSET_HAIR_STYLE)
	a.icon1 &= ^(lenHairStyle << offsetHairStyle)
	a.icon1 |= (hair & lenHairStyle) << offsetHairStyle
	return a
}

// SetHairColor 设置头发颜色
func (a *AttrModule) SetHairColor(color int64) *AttrModule {
	lenHairColor := int64(ModelConst.LEN_HAIR_COLOR)
	offsetHairColor := int64(ModelConst.OFFSET_HAIR_COLOR)
	a.icon1 &= ^(lenHairColor << offsetHairColor)
	a.icon1 |= (color & lenHairColor) << offsetHairColor
	return a
}

// SetFaceStyle 设置脸样式
func (a *AttrModule) SetFaceStyle(faceStyle int64) *AttrModule {
	lenFaceStyle := int64(ModelConst.LEN_FACE_STYLE)
	offsetFaceStyle := int64(ModelConst.OFFSET_FACE_STYLE)
	a.icon1 &= ^(lenFaceStyle << offsetFaceStyle)
	a.icon1 |= (faceStyle & lenFaceStyle) << offsetFaceStyle
	return a
}

// IsStatusBit
/*
 * @description 判断玩家状态是否包含状态
 * @param bit
 */
func (a *AttrModule) IsStatusBit(bit ModelConst.Type) bool {
	return 0 != (a.status & int64(bit))
}

// SetStatusBit
/*
 * @description 设置玩家状态
 * @param bit
 */
func (a *AttrModule) SetStatusBit(bit ModelConst.Type) {
	a.status |= int64(bit)
}

// ClearStatusBit
/*
 * @description 清除玩家状态
 * @param bit
 */
func (a *AttrModule) ClearStatusBit(bit ModelConst.Type) {
	a.status &= ^int64(bit)
}

// GetMaxExp
/*
 * @description 获取当前等级下 普通经验最大值
 * @return int
 */
func (a *AttrModule) GetMaxExp() int {
	bean, _ := cfg.ContainerLevelExp.GetBeanByUnique(a.level)
	if bean == nil {
		return 0
	}
	return bean.Exp
}

// GetMaxExp2 传奇暂定
func (a *AttrModule) GetMaxExp2() int {
	return a.GetMaxExp() * 2
}

// IsMaxLv 普通等级是不是满了
func (a *AttrModule) IsMaxLv() bool {
	return a.level >= cfg.LevelMax()
}

// IsCqMaxLv 传奇等级是不是满了
func (a *AttrModule) IsCqMaxLv() bool {
	return a.IsMaxLv() && a.level2 >= cfg.LevelMax()
}

/* 获取值 */
func (a *AttrModule) GetLevel() int                   { return a.level }
func (a *AttrModule) GetLevel2() int                  { return a.level2 }
func (a *AttrModule) GetVipLv() int                   { return a.vipLv }
func (a *AttrModule) GetVipLvMax() int                { return a.vipLvMax }
func (a *AttrModule) GetHp() int                      { return a.hp }
func (a *AttrModule) GetMp() int                      { return a.mp }
func (a *AttrModule) GetCp() int                      { return a.cp }
func (a *AttrModule) GetExp() int                     { return a.exp }
func (a *AttrModule) GetExp2() int                    { return a.exp2 }
func (a *AttrModule) GetStr() int                     { return a.str }
func (a *AttrModule) GetAgi() int                     { return a.agi }
func (a *AttrModule) GetCon() int                     { return a.con }
func (a *AttrModule) GetIlt() int                     { return a.ilt }
func (a *AttrModule) GetWis() int                     { return a.wis }
func (a *AttrModule) GetHpMax() int                   { return a.hpMax }
func (a *AttrModule) GetMpMax() int                   { return a.mpMax }
func (a *AttrModule) GetPower() *cfg.Power            { return a.power }
func (a *AttrModule) GetTitlePower1() *cfg.Power      { return a.titlePower1 }
func (a *AttrModule) GetTitlePower2() *cfg.Power      { return a.titlePower2 }
func (a *AttrModule) GetFightPowerList() []*cfg.Power { return a.fightPowerList }
func (a *AttrModule) GetSpeed() int                   { return a.speed }
func (a *AttrModule) GetAtkTime() int                 { return a.atkTime }
func (a *AttrModule) GetAtkStr() int                  { return a.atkStr }
func (a *AttrModule) GetAtkAgi() int                  { return a.atkAgi }
func (a *AttrModule) GetAtkMagic() int                { return a.atkMagic }
func (a *AttrModule) GetDefStr() int                  { return a.defStr }
func (a *AttrModule) GetDefAgi() int                  { return a.defAgi }
func (a *AttrModule) GetDefMagic() int                { return a.defMagic }
func (a *AttrModule) GetDodge() int                   { return a.dodge }
func (a *AttrModule) GetHitRate() int                 { return a.hitRate }
func (a *AttrModule) GetHitMagic() int                { return a.hitMagic }
func (a *AttrModule) GetCritical() int                { return a.critical }
func (a *AttrModule) GetForceHit() int                { return a.forceHit }
func (a *AttrModule) GetWil() int                     { return a.wil }
func (a *AttrModule) GetTough() int                   { return a.tough }
func (a *AttrModule) GetBlock() int                   { return a.block }
func (a *AttrModule) GetBrkArmor() int                { return a.brkArmor }
func (a *AttrModule) GetMagicPenetration() int        { return a.magicPenetration }
func (a *AttrModule) GetInsight() int                 { return a.insight }
func (a *AttrModule) GetDefField() int                { return a.defField }
func (a *AttrModule) GetBack() int                    { return a.back }
func (a *AttrModule) GetMagicBack() int               { return a.magicBack }
func (a *AttrModule) GetLifeAbsorption() int          { return a.lifeAbsorption }
func (a *AttrModule) GetManaAbsorption() int          { return a.manaAbsorption }
func (a *AttrModule) GetHealRecovery() int            { return a.healRecovery }
func (a *AttrModule) GetManaRecovery() int            { return a.manaRecovery }
func (a *AttrModule) GetIgnoreBack() int              { return a.ignoreBack }
func (a *AttrModule) GetIgnoreMagicBack() int         { return a.ignoreMagicBack }
func (a *AttrModule) GetIgnoreBlock() int             { return a.ignoreBlock }
func (a *AttrModule) GetIgnoreInsight() int           { return a.ignoreInsight }
func (a *AttrModule) GetIgnoreWil() int               { return a.ignoreWil }
func (a *AttrModule) GetIgnoreTouch() int             { return a.ignoreTouch }
func (a *AttrModule) GetIgnoreCritical() int          { return a.ignoreCritical }
func (a *AttrModule) GetKeepOutAtkTime() int          { return a.keepOutAtkTime }
func (a *AttrModule) GetCriticalDmg() int             { return a.criticalDmg }
func (a *AttrModule) GetRecovery() int                { return a.recovery }
func (a *AttrModule) GetArgo() int                    { return a.argo }

/* 设置值 */
func (a *AttrModule) SetHp(hp int) *AttrModule       { a.hp = hp; return a }
func (a *AttrModule) SetMp(mp int) *AttrModule       { a.mp = mp; return a }
func (a *AttrModule) SetLevel(level int) *AttrModule { a.level = level; return a }
func (a *AttrModule) SetCp(cp int) *AttrModule       { a.cp = cp; return a }
func (a *AttrModule) SetExp(exp int) *AttrModule     { a.exp = exp; return a }
func (a *AttrModule) SetExp2(exp int) *AttrModule    { a.exp2 = exp; return a }
func (a *AttrModule) SetStr(str int) *AttrModule     { a.str = str; return a }
func (a *AttrModule) SetCon(con int) *AttrModule     { a.con = con; return a }
func (a *AttrModule) SetAgi(agi int) *AttrModule     { a.agi = agi; return a }
func (a *AttrModule) SetIlt(ilt int) *AttrModule     { a.ilt = ilt; return a }
func (a *AttrModule) SetWis(wis int) *AttrModule     { a.wis = wis; return a }

/* 增加值 */
func (a *AttrModule) AddExp(exp int)   { a.exp += exp }
func (a *AttrModule) AddExp2(exp int)  { a.exp2 += exp }
func (a *AttrModule) AddLevel(lv int)  { a.level += lv }
func (a *AttrModule) AddLevel2(lv int) { a.level2 += lv }
func (a *AttrModule) AddCp(cp int)     { a.cp += cp }
