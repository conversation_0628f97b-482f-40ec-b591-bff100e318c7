package gameStruct

import (
	"fmt"
	"world/base/cfg"
	"world/common/pbBase"
	"world/common/pbBase/ModelConst"
	"world/common/pbBase/MyDefine"
	"world/common/pbBase/SKILL_TYPE"
	ut "world/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
)

// 从pb中创建
func NewPetByPb(petPb *pbBase.PetData) *Pet {
	if petPb == nil {
		return nil
	}
	return &Pet{
		cfgId:     int(petPb.GetCfgId()),
		name:      petPb.GetName(),
		grow:      int(petPb.GetGrow()),
		learn:     int(petPb.GetLearn()),
		growLevel: int(petPb.GetGrowLevel()),
		growExp:   int(petPb.GetGrowExp()),
		attr:      NewAttrModuleByPb(petPb.GetAttr()),
		skill:     NewSkillModuleByPb(petPb.GetSkill()),
		age:       petPb.GetAge(),
	}
}

type Pet struct {
	GameVo       `marshal:"-"`
	cfgId        int          `marshal:"cfgId,omitempty"`     // 配置id
	name         string       `marshal:"name,omitempty"`      // 自定义名称
	grow         int          `marshal:"grow,omitempty"`      // 成长
	learn        int          `marshal:"learn,omitempty"`     // 领悟
	growLevel    int          `marshal:"growLevel,omitempty"` // 成长等级
	growExp      int          `marshal:"growExp,omitempty"`   // 成长经验
	attr         *AttrModule  `marshal:"attr,omitempty"`      // 属性
	skill        *SkillModule `marshal:"skill,omitempty"`     // 技能模块
	age          int64        `marshal:"age,omitempty"`       // 寿命
	owner        GameVo       `marshal:"-"`                   // 所属玩家
	item         *Item        `marshal:"-"`                   // 物品载体
	potencySkill []*Skill     `marshal:"-"`                   // 潜能技能，用于替换，只存在于内存
}

func (p *Pet) IsPlayer() bool           { return false }
func (p *Pet) IsPet() bool              { return true }
func (p *Pet) AttrModule() *AttrModule  { return p.attr }
func (p *Pet) SetCfgId(cfgId int) *Pet  { p.cfgId = cfgId; return p }
func (p *Pet) SetName(name string) *Pet { p.name = name; return p }

// 宠物暂时没用背包
func (p *Pet) BagModule() *BagModule     { return nil }
func (p *Pet) SkillModule() *SkillModule { return p.skill }
func (p *Pet) SetOwner(owner GameVo)     { p.owner = owner }
func (p *Pet) GetOwner() GameVo          { return p.owner }
func (p *Pet) GetAge() int               { return int(ut.Max(0, int64(p.age-ut.Now())/ut.TIME_DAY)) }
func (p *Pet) GetId() int64              { return lo.If(p.IsPet(), p.item.GetPetId()).Else(-1) }
func (p *Pet) bindItem(item *Item)       { p.item = item }
func (p *Pet) GetGrowLevel() int         { return p.growLevel }
func (p *Pet) GetLearn() int             { return p.learn }
func (p *Pet) GetGrow() int              { return p.grow }
func (p *Pet) GetGrowExp() int           { return p.growExp }
func (p *Pet) GetPotencySkill() []*Skill { return p.potencySkill }

// 清空成长经验
func (p *Pet) ClearGrowExp() { p.growExp = 0 }

// 增加成长经验
func (p *Pet) AddGrowExp(v int) { p.growExp += v }

// 增加成长等级
func (p *Pet) AddGrowLevel(v int) { p.growLevel += v }

// 设置寿命
func (p *Pet) SetAge(day int)             { p.age = ut.Now() + int64(day)*ut.TIME_DAY }
func (p *Pet) SetPotencySkill(v []*Skill) { p.potencySkill = v }

// 宠物暂时没有套装
func (p *Pet) GetItemSetData() []int { return nil }
func (p *Pet) Get(typ ModelConst.Type) int {
	switch typ {
	case ModelConst.PET_COLOR:
		bean, _ := cfg.ContainerPet.GetBeanByUnique(p.cfgId)
		return int(bean.Grade)
	case ModelConst.PET_GRADE:
		bean, _ := cfg.ContainerPet.GetBeanByUnique(p.cfgId)
		return int(bean.Grade)
	case ModelConst.PET_GROW_LEVEL:
		return p.growLevel
	case ModelConst.ATK_TYPE:
		bean, _ := cfg.ContainerPet.GetBeanByUnique(p.cfgId)
		return bean.AtkType
	case ModelConst.HIT_RATE:
		bean, _ := cfg.ContainerPet.GetBeanByUnique(p.cfgId)
		return GetPetPowerValue(p, bean.HitRate, p.AttrModule().GetHitRate(), MyDefine.POWER_HITRATE, MyDefine.POWER_HITRATE_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), MyDefine.POWER_NONE)
	case ModelConst.HIT_MAGIC:
		bean, _ := cfg.ContainerPet.GetBeanByUnique(p.cfgId)
		return GetPetPowerValue(p, bean.HitRate, p.AttrModule().GetHitMagic(), MyDefine.POWER_MAGIC_HITRATE, MyDefine.POWER_MAGIC_HITRATE_PERCENT, SKILL_TYPE.PASSIVE, int(ModelConst.MIN_HIT_MAGIC), int(ModelConst.MAX_BASE_ATTRIBUTE), MyDefine.POWER_NONE)
	case ModelConst.LEFT_ATK_TIME:
		fallthrough
	case ModelConst.ATK_TIME:
		bean, _ := cfg.ContainerPet.GetBeanByUnique(p.cfgId)
		atkTime := ut.SumValue(bean.PetAtkTime+1, p.AttrModule().GetAtkTime(), int(ModelConst.MIN_HIT_TIME), int(ModelConst.MAX_HIT_TIME))
		return atkTime + p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_ALL_ATK_TIME)
	case ModelConst.RIGHT_ATK_TIME:
		return 0
	case ModelConst.LEFT_ATK_MIN:
		bean, _ := cfg.ContainerPet.GetBeanByUnique(p.cfgId)
		atkMin := GetPetPowerValue(p, bean.AtkMin, 0, MyDefine.POWER_NONE, MyDefine.POWER_NONE, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), MyDefine.POWER_PET_DAMAGE)
		return ut.SumValue(atkMin, 0, 0, int(ModelConst.MAX_ATK))
	case ModelConst.LEFT_ATK_MAX:
		bean, _ := cfg.ContainerPet.GetBeanByUnique(p.cfgId)
		atkMax := GetPetPowerValue(p, bean.AtkMax, 0, MyDefine.POWER_NONE, MyDefine.POWER_NONE, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), MyDefine.POWER_PET_DAMAGE)
		return ut.SumValue(atkMax, 0, 0, int(ModelConst.MAX_ATK))
	case ModelConst.RIGHT_ATK_MIN:
		return 0
	case ModelConst.RIGHT_ATK_MAX:
		return 0
	case ModelConst.HPMAX:
		hpMax := 65*p.Get(ModelConst.CON)/10 + 3*p.Get(ModelConst.STR) + 100 + 20*(p.Get(ModelConst.LEVEL)-1)
		hpMax = GetPetPowerValue(p, hpMax, 0, MyDefine.POWER_HPMAX, MyDefine.POWER_HPMAX_PERCENT, SKILL_TYPE.PASSIVE, 1, int(ModelConst.MAX_PLAYER_HP), MyDefine.POWER_PET_HPMAX_PERCENT)
		return hpMax
	case ModelConst.MPMAX:
		mpMax := 5*p.Get(ModelConst.ILT) + 2*p.Get(ModelConst.WIS) + 40 + 5*(p.Get(ModelConst.LEVEL)-1)
		mpMax = GetPetPowerValue(p, mpMax, 0, MyDefine.POWER_MPMAX, MyDefine.POWER_MPMAX_PERCENT, SKILL_TYPE.PASSIVE, 1, int(ModelConst.MAX_PLAYER_MP), MyDefine.POWER_PET_MPMAX_PERCENT)
		return mpMax
	case ModelConst.STR: // 力量
		bean, _ := cfg.ContainerPet.GetBeanByUnique(p.cfgId)
		return GetPetBaseValue(p, bean.Str*p.growLevel, MyDefine.POWER_STR, MyDefine.POWER_STR_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), MyDefine.POWER_PET_STR_PERCENT)
	case ModelConst.CON: // 体质
		bean, _ := cfg.ContainerPet.GetBeanByUnique(p.cfgId)
		return GetPetBaseValue(p, bean.Con*p.growLevel, MyDefine.POWER_CON, MyDefine.POWER_CON_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), MyDefine.POWER_PET_CON_PERCENT)
	case ModelConst.AGI: // 敏捷
		bean, _ := cfg.ContainerPet.GetBeanByUnique(p.cfgId)
		return GetPetBaseValue(p, bean.Agi*p.growLevel, MyDefine.POWER_AGI, MyDefine.POWER_AGI_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), MyDefine.POWER_PET_AGI_PERCENT)
	case ModelConst.ILT: // 智力
		bean, _ := cfg.ContainerPet.GetBeanByUnique(p.cfgId)
		return GetPetBaseValue(p, bean.Ilt*p.growLevel, MyDefine.POWER_ILT, MyDefine.POWER_ILT_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), MyDefine.POWER_PET_ILT_PERCENT)
	case ModelConst.WIS: // 感知
		bean, _ := cfg.ContainerPet.GetBeanByUnique(p.cfgId)
		return GetPetBaseValue(p, bean.Wis*p.growLevel, MyDefine.POWER_WIS, MyDefine.POWER_WIS_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), MyDefine.POWER_PET_WIS_PERCENT)
	}
	return baseGet(p, typ)
}

func (p *Pet) InitAllModule() {
	p.initAttrModule().BindVo(p)
	p.initSkillModule().BindVo(p)
}

// GetJson 获取宠物配置
func (p *Pet) GetJson() *cfg.Pet[int] {
	bean, _ := cfg.ContainerPet.GetBeanByUnique(p.cfgId)
	return bean
}

func (p *Pet) initAttrModule() *AttrModule {
	if p.attr == nil {
		p.attr = EmptyAttrModule().SetLevel(1).SetHp(100).SetMp(50)
	}
	return p.attr
}
func (p *Pet) initSkillModule() *SkillModule {
	if p.skill == nil {
		p.skill = NewSkillModule(10, 14)
	}
	return p.skill
}

func (p *Pet) ToPb() *pbBase.PetData {
	if p == nil {
		return nil
	}
	info := &pbBase.PetData{
		CfgId:     int32(p.cfgId),
		Name:      p.name,
		Grow:      int32(p.grow),
		Learn:     int32(p.learn),
		GrowLevel: int32(p.growLevel),
		Attr:      p.attr.ToPb(),
		Skill:     p.skill.ToPb(),
		Age:       p.age,
		GrowExp:   int32(p.growExp),
		Id:        p.GetId(),
	}
	return info
}

func (p *Pet) ToCrossSimplePb() *pbBase.CrossSimplePet {
	if p == nil {
		return nil
	}
	return &pbBase.CrossSimplePet{
		CfgId: int32(p.cfgId),
		Id:    p.GetId(),
		Name:  p.name,
		Age:   p.age,
	}
}

// 宠物重生
func (p *Pet) Rebirth() {
	p.age = 100*ut.TIME_DAY + ut.Now()
	p.growLevel = 1
	p.growExp = 0
	p.attr = nil
	p.skill = nil
	p.InitAllModule()
	// 固定天赋技能
	bean, _ := cfg.ContainerPet.GetBeanByUnique(p.cfgId)
	for _, v := range bean.BornSkill {
		skillBean, _ := cfg.ContainerSkill.GetBeanByUnique(fmt.Sprintf("%d-%d", v.Id, 1))
		if skillBean == nil {
			panic(fmt.Sprintf("找不到技能 : %d", v.Id))

		}
		p.skill.AddSkill(NewSkill(v.Id, 1, 0, false))
	}

	ResumeHPMP(p)
}

// 宠物洗髓
func (p *Pet) Create(plr *Player) {
	bean, _ := cfg.ContainerPet.GetBeanByUnique(p.cfgId)
	// 重塑成长和领悟值
	p.learn = bean.RandomGetLearn()
	p.grow = bean.RandomGetGrow()
	// 重生其他属性
	p.Rebirth()
	p.SetOwner(plr)
}

// GetTalentSkill 获取天赋技能 2个
func (p *Pet) GetTalentSkill() []*Skill {
	bean, _ := cfg.ContainerPet.GetBeanByUnique(p.cfgId)
	ary := make([]*Skill, 0)
	for _, v := range bean.BornSkill {
		s := p.skill.GetSkill(v.Id)
		if s == nil {
			log.Error(fmt.Sprintf("宠物不存在天赋技能？？？？%d", v.Id))
			continue
		}
		ary = append(ary, s)
	}
	return ary
}

// GetLearnSkill 获取2个领悟技能,宠物有2个升级领悟技能和2个潜能领悟技能
//
// Parameters:
//   - potency bool 是不是潜能
//
// Returns:
//   - []*Skill
func (p *Pet) GetLearnSkill(potency bool) []*Skill {
	bean, _ := cfg.ContainerPet.GetBeanByUnique(p.cfgId)
	ary := make([]*Skill, 0)
	for _, v := range bean.LearnSkill {
		s := p.skill.GetSkill(v.Id)
		if s == nil {
			continue
		}
		if potency && !s.IsLearnByBook() {
			continue
		}
		ary = append(ary, s)
	}
	return ary
}
