package gameStruct

import (
	"world/common/pbBase"
	ut "world/utils"

	"github.com/samber/lo"
)

type TaskModule struct {
	GameModule `marshal:"-"`
	taskStatus *ut.BitMap  `marshal:"taskStatus,omitempty"` // 任务数据bitmap
	tasks      []*Task     `marshal:"tasks,omitempty"`      // 当前进行中的任务
	killRecord map[int]int `marshal:"killRecord,omitempty"` // 任务杀怪记录
}

func (t *TaskModule) GetTasks() []*Task             { return t.tasks }
func (t *TaskModule) AddTask(task *Task)            { t.tasks = append(t.tasks, task) }
func (t *TaskModule) GetKillRecord(monster int) int { v := t.killRecord[monster]; return v }

// AddKillRecord 增加杀怪数
//
// Parameters:
//   - monster int 怪物id
//   - add int 增加的数量
//   - max int 最大数量
func (t *TaskModule) AddKillRecord(monster int, add, max int) {
	max = ut.Max(max, 0)
	t.killRecord[monster] = ut.Min(add+t.<PERSON>(monster), max)
}

// IsTaskFinished
/*
 * @description 判断任务是否完成
 * @param id 任务id
 * @return bool
 */
func (t *TaskModule) IsTaskFinished(id int) bool { return t.taskStatus.Get(id) }

// SetTaskStatus
/*
 * @description 设置任务状态
 * @param id 任务id
 * @param done true完成，false未完成
 */
func (t *TaskModule) SetTaskStatus(id int, done bool) {
	if done {
		t.taskStatus.Set(id)
	} else {
		t.taskStatus.Clear(id)
	}
}

func (t *TaskModule) ToPb() *pbBase.TaskData {
	self := t
	return &pbBase.TaskData{
		Tasks:      lo.Map(t.tasks, func(t *Task, i int) *pbBase.TaskInfo { return t.ToPb(self) }),
		TaskStatus: t.taskStatus.Bits,
	}
}

// GetTask
/*
 * @description 从任务数据中获取任务
 * @param taskId
 * @return *Task
 */
func (t *TaskModule) GetTask(taskId int) *Task {
	task, _ := lo.Find(t.tasks, func(task *Task) bool {
		return task.id == taskId
	})
	return task
}

// DeleteTask
/*
 * @description 从任务数据中删除任务,返回删除的任务
 * @param taskId
 * @return *Task
 */
func (t *TaskModule) DeleteTask(taskId int) *Task {
	delTask, _ := lo.Find(t.tasks, func(task *Task) bool {
		return task.id == taskId
	})
	if delTask != nil {
		t.tasks = lo.Filter(t.tasks, func(task *Task, i int) bool {
			return task.id != delTask.id
		})
	}
	return delTask
}
