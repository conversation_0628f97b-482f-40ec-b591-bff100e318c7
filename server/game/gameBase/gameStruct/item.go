package gameStruct

import (
	"world/base/cfg"
	"world/common/pbBase"
	"world/common/pbBase/EQP"
	"world/common/pbBase/EQUIP_POS"
	"world/common/pbBase/ITEM_CLASS"
	"world/common/pbBase/ITEM_STATUS"
	"world/common/pbBase/ITEM_TYPE"
	ITEM "world/common/pbBase/Item"
	"world/common/pbBase/MyDefine"
	"world/game/gameBase/types/PlayerBag"
	ut "world/utils"
	"world/utils/bit"

	"github.com/samber/lo"
)

// 从pb中创建
func NewItemByPb(itemPb *pbBase.ItemData) *Item {
	return &Item{
		id:            int(itemPb.GetId()),
		slotPos:       int(itemPb.GetSlotPos()),
		quantity:      int(itemPb.GetQuantity()),
		status:        int(itemPb.GetStatus()),
		power1:        cfg.CreatePowerByPb(itemPb.GetPower1()),
		power2:        cfg.CreatePowerByPb(itemPb.GetPower2()),
		power3:        cfg.CreatePowerByPb(itemPb.GetPower3()),
		bindPower1:    cfg.CreatePowerByPb(itemPb.GetBindPower1()),
		bindPower2:    cfg.CreatePowerByPb(itemPb.GetBindPower2()),
		power4:        cfg.CreatePowerByPb(itemPb.GetPower4()),
		power5:        cfg.CreatePowerByPb(itemPb.GetPower5()),
		power6:        cfg.CreatePowerByPb(itemPb.GetPower6()),
		power7:        cfg.CreatePowerByPb(itemPb.GetPower7()),
		enchantPower1: cfg.CreatePowerByPb(itemPb.GetEnchantPower1()),
		enchantPower2: cfg.CreatePowerByPb(itemPb.GetEnchantPower2()),
		expireTime:    itemPb.GetExpireTime(),
		durability:    int(itemPb.GetDurability()),
		attachDone:    int(itemPb.GetAttachDone()),
		attachPower:   cfg.CreatePowerByPb(itemPb.GetAttachPower()),
		star:          int(itemPb.GetStar()),
		upgradeStar:   int(itemPb.GetUpgradeStar()),
		petId:         itemPb.GetPetId(),
		petItem:       NewPetByPb(itemPb.GetPetItem()),
	}
}

type Item struct {
	id             int        `marshal:"id,omitempty"`             // 物品配置id
	slotPos        int        `marshal:"slotPos,omitempty"`        // 位置
	quantity       int        `marshal:"quantity,omitempty"`       // 数量
	status         int        `marshal:"status,omitempty"`         // bit数据
	power1         *cfg.Power `marshal:"power1,omitempty"`         // 基础属性1 获取请使用GetPower1
	power2         *cfg.Power `marshal:"power2,omitempty"`         // 基础属性2 获取请使用GetPower2
	power3         *cfg.Power `marshal:"power3,omitempty"`         // 基础属性3 获取请使用GetPower3
	bindPower1     *cfg.Power `marshal:"bindPower1,omitempty"`     // 绑定属性1类型 绑定、鉴定 获取请使用GetBindPower1
	bindPower2     *cfg.Power `marshal:"bindPower2,omitempty"`     // 绑定属性2类型 绑定、鉴定 获取请使用GetBindPower2
	power4         *cfg.Power `marshal:"power4,omitempty"`         // 进阶属性1 获取请使用GetPower4
	power5         *cfg.Power `marshal:"power5,omitempty"`         // 进阶属性2 获取请使用GetPower5
	power6         *cfg.Power `marshal:"power6,omitempty"`         // 进阶属性3 获取请使用GetPower6
	power7         *cfg.Power `marshal:"power7,omitempty"`         // 进阶属性4 获取请使用GetPower7
	enchantPower1  *cfg.Power `marshal:"enchantPower1,omitempty"`  // 附魔属性1 获取请使用GetEnchantPower1
	enchantPower2  *cfg.Power `marshal:"enchantPower2,omitempty"`  // 附魔属性2 获取请使用GetEnchantPower2
	expireTime     int64      `marshal:"expireTime,omitempty"`     // 过期时间
	durability     int        `marshal:"durability,omitempty"`     // 耐久度，装备特有
	attachDone     int        `marshal:"attachDone,omitempty"`     // 宝石镶嵌数量
	attachPower    *cfg.Power `marshal:"attachPower,omitempty"`    // 镶嵌属性
	star           int        `marshal:"star,omitempty"`           // 普通升星数量
	upgradeStar    int        `marshal:"upgradeStar,omitempty"`    // 进阶升星数量
	illusionItemId int        `marshal:"illusionItemId,omitempty"` // 幻化id
	petId          int64      `marshal:"petId,omitempty"`          // 作为宠物时的唯一id
	petItem        *Pet       `marshal:"petItem,omitempty"`        // 作为宠物时对应的宠物
}

// ToPb
/*
 * @description 转换为pb
 * @return *pbBase.ItemData
 */
func (i *Item) ToPb() *pbBase.ItemData {
	if i == nil {
		return nil
	}
	extra := make(map[string]string)

	return &pbBase.ItemData{
		Id:            int32(i.id),
		SlotPos:       int32(i.slotPos),
		Quantity:      int32(i.quantity),
		Status:        int32(i.status),
		Power1:        i.power1.ToPb(),
		Power2:        i.power2.ToPb(),
		Power3:        i.power3.ToPb(),
		BindPower1:    i.bindPower1.ToPb(),
		BindPower2:    i.bindPower2.ToPb(),
		Power4:        i.power4.ToPb(),
		Power5:        i.power5.ToPb(),
		Power6:        i.power6.ToPb(),
		Power7:        i.power7.ToPb(),
		EnchantPower1: i.enchantPower1.ToPb(),
		ExpireTime:    i.expireTime,
		Durability:    int32(i.durability),
		AttachDone:    int32(i.attachDone),
		AttachPower:   i.attachPower.ToPb(),
		Star:          int32(i.star),
		UpgradeStar:   int32(i.upgradeStar),
		PetId:         i.petId,
		Extra:         extra,
		PetItem:       i.petItem.ToPb(),
	}
}

// Config
/*
 * @description 获取配置数据
 * @return *cfg.Item[int]
 */
func (i *Item) Config() *cfg.Item[int] {
	bean, _ := cfg.ContainerItem.GetBeanByUnique(i.id)
	return bean
}

// 获取id
func (i *Item) GetId() int { return i.id }

// 设置id
func (i *Item) SetId(v int) { i.id = v }

// 获取位置
func (i *Item) GetSlotPos() int { return i.slotPos }

// 设置位置
func (i *Item) SetSlotPos(v int) { i.slotPos = v }

// 获取耐久度
func (i *Item) GetDurability() int { return i.durability }

// 设置耐久度
func (i *Item) SetDurability(v int) { i.durability = v }

// 获取镶嵌数量
func (i *Item) GetAttachDone() int { return i.attachDone }

// 设置镶嵌数量
func (i *Item) SetAttachDone(v int) { i.attachDone = v }

// 增加镶嵌数量
func (i *Item) AddAttachDone(v int) { i.attachDone += v }

// 获取数量
func (i *Item) GetQuantity() int { return i.quantity }

// 设置数量
func (i *Item) SetQuantity(v int) { i.quantity = v }

// 增加数量
func (i *Item) AddQuantity(v int) { i.quantity = ut.Max(0, i.quantity+v) }

// 获取过期时间
func (i *Item) GetExpireTime() int64 { return i.expireTime }

// 设置过期时间
func (i *Item) SetExpireTime(v int64) {
	i.expireTime = v
	i.CheckTimeItem()
}

// 获取强化星级
func (i *Item) GetStar() int { return i.star }

// 增加强化星级
func (i *Item) AddStar(v int) { i.star += v }

// 获取进阶强化星级
func (i *Item) GetUpgradeStar() int { return i.upgradeStar }

// 增加进阶强化星级
func (i *Item) AddUpgradeStar(v int) { i.upgradeStar += v }

// 获取幻化id
func (i *Item) GetIllusionItemId() int { return i.illusionItemId }

// 设置幻化id
func (i *Item) SetIllusionItemId(v int) { i.illusionItemId = v }

// 获取宠物
func (i *Item) GetPetItem() *Pet { return i.petItem }

// 设置宠物
func (i *Item) SetPetItem(v *Pet, petId int64) { i.petItem = v; i.petId = petId }

// 获取宠物id 如果这是一个宠物物品的话
func (i *Item) GetPetId() int64 { return i.petId }

// 基础能力1
func (i *Item) GetPower1() *cfg.Power {
	i.power1 = i.Config().Power1.CloneTo(i.power1)
	return i.power1
}
func (i *Item) SetPower1(v *cfg.Power)                 { i.power1 = v }
func (i *Item) SetPower1ByData(data *pbBase.PowerData) { i.power1 = cfg.SetPowerByPb(i.power1, data) }

// 基础能力2
func (i *Item) GetPower2() *cfg.Power {
	i.power2 = i.Config().Power2.CloneTo(i.power2)
	return i.power2
}
func (i *Item) SetPower2(v *cfg.Power)                 { i.power2 = v }
func (i *Item) SetPower2ByData(data *pbBase.PowerData) { i.power2 = cfg.SetPowerByPb(i.power2, data) }

// 基础能力3
func (i *Item) GetPower3() *cfg.Power {
	i.power3 = i.Config().Power3.CloneTo(i.power3)
	return i.power3
}
func (i *Item) SetPower3(v *cfg.Power)                 { i.power3 = v }
func (i *Item) SetPower3ByData(data *pbBase.PowerData) { i.power3 = cfg.SetPowerByPb(i.power3, data) }

// 绑定能力1 一般只有装备才有
func (i *Item) GetBindPower1() *cfg.Power {
	i.bindPower1 = i.Config().BindPower1.CloneTo(i.bindPower1)
	return i.bindPower1
}
func (i *Item) SetBindPower1(v *cfg.Power) { i.bindPower1 = v }
func (i *Item) SetBindPower1ByData(data *pbBase.PowerData) {
	i.bindPower1 = cfg.SetPowerByPb(i.bindPower1, data)
}

// 绑定能力2 一般只有装备才有
func (i *Item) GetBindPower2() *cfg.Power {
	i.bindPower2 = i.Config().BindPower2.CloneTo(i.bindPower2)
	return i.bindPower2
}
func (i *Item) SetBindPower2(v *cfg.Power) { i.bindPower2 = v }
func (i *Item) SetBindPower2ByData(data *pbBase.PowerData) {
	i.bindPower2 = cfg.SetPowerByPb(i.bindPower2, data)
}

// 扩展属性  只有装备才有 装备没有那就是没有对应的模块操作
func (i *Item) GetPower4() *cfg.Power  { return i.power4 }
func (i *Item) SetPower4(v *cfg.Power) { i.power4 = v }

func (i *Item) GetPower5() *cfg.Power  { return i.power5 }
func (i *Item) SetPower5(v *cfg.Power) { i.power5 = v }

func (i *Item) GetPower6() *cfg.Power  { return i.power6 }
func (i *Item) SetPower6(v *cfg.Power) { i.power6 = v }

func (i *Item) GetPower7() *cfg.Power  { return i.power7 }
func (i *Item) SetPower7(v *cfg.Power) { i.power7 = v }

func (i *Item) GetEnchantPower1() *cfg.Power { return i.enchantPower1 }
func (i *Item) GetEnchantPower2() *cfg.Power { return i.enchantPower2 }

func (i *Item) GetAttachPower() *cfg.Power  { return i.attachPower }
func (i *Item) SetAttachPower(v *cfg.Power) { i.attachPower = v }

// IsStackAble
/*
 * @description 物品是否能堆积
 * @return bool
 */
func (i *Item) IsStackAble() bool {
	if i.IsTimeItem() {
		return false
	}
	if i.IsShopLocked() {
		return false
	}
	return i.Config().StackNum > 1
}

// IsTimeItem
/*
 * @description 是否是期限物品
 * @return bool
 */
func (i *Item) IsTimeItem() bool {
	return i.Is(int(ITEM_STATUS.TIME_ITEM))
}

// GetItemClass
/*
 * @description 获取道具的类型定义
 * @return pbGame.ItemClass
 */
func (i *Item) GetItemClass() ITEM_CLASS.Type {
	cfg := i.Config()
	if cfg.Type == ITEM_TYPE.LN_STONE {
		return ITEM_CLASS.LN_STONE
	} else if cfg.Type == ITEM_TYPE.ADD_PET_LIFE {
		return ITEM_CLASS.PET_ADD_LIFE
	} else if cfg.Type == ITEM_TYPE.PET {
		return ITEM_CLASS.PET
	} else if cfg.Type == ITEM_TYPE.GEM {
		return ITEM_CLASS.GEM
	}
	// 检查是否为任务、建筑材料或特殊类型
	if cfg.Type == ITEM_TYPE.TASK || cfg.Type == ITEM_TYPE.BUILD_MATERIAL || cfg.Type == ITEM_TYPE.SPECIAL {
		return ITEM_CLASS.QUEST
	}
	// 检查是否为武器
	if cfg.Type >= ITEM_TYPE.WEAPON_ONEHAND_SWORD && cfg.Type <= ITEM_TYPE.WEAPON_ONEHAND_HAND {
		return ITEM_CLASS.WEAPON
	}
	if cfg.Type >= ITEM_TYPE.WEAPON_BALL && cfg.Type <= ITEM_TYPE.WEAPON_TWOHAND_FAN {
		return ITEM_CLASS.WEAPON
	}
	// 检查是否为战斗使用或非战斗使用物品，或技能书
	if cfg.Type >= ITEM_TYPE.BATTLE_USE && cfg.Type <= ITEM_TYPE.NOT_BATTLE_USE {
		return ITEM_CLASS.USE_ITEM
	}
	if cfg.Type == ITEM_TYPE.SKILL_BOOK {
		return ITEM_CLASS.USE_ITEM
	}
	// 检查是否为其他特殊类型的物品
	if cfg.Type == ITEM_TYPE.BOX_CHOOSE_ONE {
		return ITEM_CLASS.BOX_CHOOSE_ONE
	} else if cfg.Type == ITEM_TYPE.BLOOD_BOTTLE {
		return ITEM_CLASS.BLOOD_BOTTLE
	} else if i.GetIsItemPetEquip() {
		return ITEM_CLASS.PET_EQUIP
	} else if cfg.Type == ITEM_TYPE.PET_EQUIP_EXP_BOOK {
		return ITEM_CLASS.PET_EQUIP_EXP_BOOK
	} else if cfg.Type == ITEM_TYPE.SEAL {
		return ITEM_CLASS.SEAL
	} else if cfg.Type == ITEM_TYPE.ENERGY_ESSENCE {
		return ITEM_CLASS.ENERGY_ESSENCE
	}
	return ITEM_CLASS.ARMOR
}

// GetIsItemPetEquip 是不是宠物装备
func (i *Item) GetIsItemPetEquip() bool { return i.Config().Type == ITEM_TYPE.PET_EQUIP }

// 物品是否绑定
func (i *Item) IsBinded() bool { return i.IsAutoBinding() || bit.IsBit(i.status, ITEM_STATUS.BIND) }

// 物品是不是自动绑定
func (i *Item) IsAutoBinding() bool { return i.Config().IsAutoBinding() }

// IsAttachBroken 宝石镶嵌是否破损
func (i *Item) IsAttachBroken() bool { return bit.IsBit(i.status, ITEM_STATUS.ATTACK_BROKEN) }

func (i *Item) IsEqual(item *Item) bool {
	return item != nil && i == item
}

// IsEquipClass 是不是装备类型
func (i *Item) IsEquipClass() bool {
	clazz := i.GetClass()
	if clazz == ITEM_CLASS.ARMOR {
		return true
	}
	if clazz == ITEM_CLASS.WEAPON {
		return true
	}
	return false
}

// IsPet 是不是宠物类型
func (i *Item) IsPet() bool {
	clazz := i.GetClass()
	return clazz == ITEM_CLASS.PET
}

// IsGem 是不是宝石
func (i *Item) IsGem() bool {
	return i.Config().Type == ITEM_TYPE.GEM
}

// GetClass 道具具体类型
func (i *Item) GetClass() ITEM_CLASS.Type {
	itemType := i.Config().Type
	switch itemType {
	case ITEM_TYPE.PET:
		return ITEM_CLASS.PET
	case ITEM_TYPE.GEM:
		return ITEM_CLASS.GEM
	case ITEM_TYPE.TASK:
		fallthrough
	case ITEM_TYPE.BUILD_MATERIAL:
		fallthrough
	case ITEM_TYPE.SPECIAL:
		return ITEM_CLASS.QUEST
	case ITEM_TYPE.SKILL_BOOK:
		return ITEM_CLASS.USE_ITEM
	case ITEM_TYPE.BLOOD_BOTTLE:
		return ITEM_CLASS.BLOOD_BOTTLE
	case ITEM_TYPE.PET_EQUIP:
		return ITEM_CLASS.PET_EQUIP
	case ITEM_TYPE.PET_EQUIP_EXP_BOOK:
		return ITEM_CLASS.PET_EQUIP_EXP_BOOK
	case ITEM_TYPE.SEAL:
		return ITEM_CLASS.SEAL
	default:
		if itemType >= ITEM_TYPE.WEAPON_ONEHAND_SWORD && itemType <= ITEM_TYPE.WEAPON_ONEHAND_HAND {
			return ITEM_CLASS.WEAPON
		}
		if itemType >= ITEM_TYPE.WEAPON_BALL && itemType <= ITEM_TYPE.WEAPON_TWOHAND_FAN {
			return ITEM_CLASS.WEAPON
		}
		if itemType >= ITEM_TYPE.BATTLE_USE && itemType <= ITEM_TYPE.NOT_BATTLE_USE {
			return ITEM_CLASS.USE_ITEM
		}
		return ITEM_CLASS.ARMOR
	}
}

// GetEquipFixPos 获取装备类型的道具的固定装备位置
func (i *Item) GetEquipFixPos() []EQUIP_POS.Type {
	pos := make([]EQUIP_POS.Type, 0)
	switch i.Config().Type {
	case ITEM_TYPE.PET:
		pos = append(pos, EQUIP_POS.PET_POS)
	case ITEM_TYPE.ARMOR_HEAD:
		pos = append(pos, EQUIP_POS.ARMOR_HEAD_POS)
	case ITEM_TYPE.ARMOR_CLOTHES:
		pos = append(pos, EQUIP_POS.ARMOR_CLOTHES_POS)
	case ITEM_TYPE.ARMOR_TROUSERS:
		pos = append(pos, EQUIP_POS.ARMOR_TROUSERS_POS)
	case ITEM_TYPE.ARMOR_SHOULDER:
		pos = append(pos, EQUIP_POS.ARMOR_SHOULDER_POS)
	case ITEM_TYPE.ARMOR_WAIST:
		pos = append(pos, EQUIP_POS.ARMOR_WAIST_POS)
	case ITEM_TYPE.ARMOR_BACK:
		pos = append(pos, EQUIP_POS.ARMOR_BACK_POS)
	case ITEM_TYPE.ARMOR_SHOES:
		pos = append(pos, EQUIP_POS.ARMOR_SHOES_POS)
	case ITEM_TYPE.ARMOR_HAND:
		pos = append(pos, EQUIP_POS.ARMOR_HAND_POS)
	case ITEM_TYPE.ARMOR_NECKLACE:
		pos = append(pos, EQUIP_POS.ARMOR_NECKLACE_POS)
	case ITEM_TYPE.ARMOR_RING:
		pos = append(pos, EQUIP_POS.ARMOR_RING_LEFT_POS)
		pos = append(pos, EQUIP_POS.ARMOR_RING_RIGHT_POS)
	case ITEM_TYPE.ARMOR_AMULET:
		pos = append(pos, EQUIP_POS.ARMOR_AMULET_POS)
	case ITEM_TYPE.ARMOR_FASHION:
		pos = append(pos, EQUIP_POS.ARMOR_FASHION_POS)
	case ITEM_TYPE.WEAPON_ONEHAND_SWORD:
		fallthrough
	case ITEM_TYPE.WEAPON_ONEHAND_BLADE:
		fallthrough
	case ITEM_TYPE.WEAPON_ONEHAND_HEAVY:
		fallthrough
	case ITEM_TYPE.WEAPON_ONEHAND_CROSSBOW:
		fallthrough
	case ITEM_TYPE.WEAPON_ONEHAND_HAND:
		fallthrough
	case ITEM_TYPE.WEAPON_ONEHAND_GUN:
		fallthrough
	case ITEM_TYPE.WEAPON_ONEHAND_HAMMER:
		// 单手武器
		pos = append(pos, EQUIP_POS.WEAPON_LEFT_POS)
		pos = append(pos, EQUIP_POS.WEAPON_RIGHT_POS)
	case ITEM_TYPE.WEAPON_TWOHAND_SWORD:
		fallthrough
	case ITEM_TYPE.WEAPON_TWOHAND_BLADE:
		fallthrough
	case ITEM_TYPE.WEAPON_TWOHAND_HEAVY:
		fallthrough
	case ITEM_TYPE.WEAPON_TWOHAND_STAFF:
		fallthrough
	case ITEM_TYPE.WEAPON_TWOHAND_LANCE:
		fallthrough
	case ITEM_TYPE.WEAPON_TWOHAND_CROSSBOW:
		fallthrough
	case ITEM_TYPE.WEAPON_TWOHAND_BOW:
		fallthrough
	case ITEM_TYPE.WEAPON_BALL:
		fallthrough
	case ITEM_TYPE.WEAPON_TWOHAND_GUN:
		fallthrough
	case ITEM_TYPE.WEAPON_TWOHAND_HAMMER:
		// 双持武器
		pos = append(pos, EQUIP_POS.WEAPON_LEFT_POS)
	case ITEM_TYPE.ARMOR_TRANSPORT:
		pos = append(pos, EQUIP_POS.ARMOR_TRANSPORT_POS)
	}
	return pos
}

// IsTwoHandWeapon 是否是双持武器
func (i *Item) IsTwoHandWeapon() bool {
	if i.GetClass() != ITEM_CLASS.WEAPON {
		return false
	}
	return len(i.GetEquipFixPos()) == 2
}

// IsBound 物品是否绑定
func (i *Item) IsBound() bool {
	return i.Config().IsAutoBinding() || (i.status&int(ITEM_STATUS.BIND)) > 0
}

// SetBind 绑定物品
func (i *Item) SetBind() { i.SetBit(true, ITEM_STATUS.BIND) }

// IsSelling  摆摊出售中
func (i *Item) IsSelling() bool { return (i.status & int(ITEM_STATUS.SELLING)) > 0 }

// SetSelling  摆摊出售
func (i *Item) SetSelling() { i.SetBit(true, ITEM_STATUS.SELLING) }

// SetIsIdentifyItem 设置可鉴定
func (i *Item) SetIsIdentifyItem() { i.SetBit(true, ITEM_STATUS.CAN_IDENTIFY) }

// SetAttachBroken 宝石镶嵌是否破损
func (i *Item) SetAttachBroken(is bool) { i.SetBit(is, ITEM_STATUS.ATTACK_BROKEN) }

// IsIdentifyItem 是否可鉴定
func (i *Item) IsIdentifyItem() bool { return i.Is(int(ITEM_STATUS.CAN_IDENTIFY)) }

// IsShopLocked 商店锁定状态
func (i *Item) IsShopLocked() bool { return i.Is(int(ITEM_STATUS.SHOP_LOCKED)) }

// IsNotOperate 是否处于不可操作状态
func (i *Item) IsNotOperate() bool { return i.IsShopLocked() || i.IsSelling() }

// IsEquited 物品是否已装备
//
// Returns:
//   - bool
func (i *Item) IsEquited() bool {
	if i.slotPos >= PlayerBag.EQUIP_POS_START && i.slotPos < PlayerBag.EQUIP_POS_END {
		return true
	}
	return false
}

func (i *Item) IsUpgrade() bool {
	return i.power4 != nil || i.power5 != nil || i.power6 != nil || i.power7 != nil
}

// 物品能否镶嵌宝石
func (i *Item) IsCanAttach() bool { return i.Config().AttachCount > 0 }

// SetBit 设置状态
func (i *Item) SetBit(val bool, bit ITEM_STATUS.Type) {
	if val {
		i.status |= int(bit)
		return
	}
	i.status &= ^int(bit)
}

// Is 检查状态
func (i *Item) Is(status int) bool {
	return (i.status & status) > 0
}

// CheckTimeItem
/*
 * @description 检查是否过期 改变物品状态
 */
func (i *Item) CheckTimeItem() {
	if i.IsExpired() {
		i.SetBit(true, ITEM_STATUS.TIME_OUT)
		return
	}
	i.SetBit(false, ITEM_STATUS.TIME_OUT)
}

// IsExpired
/*
 * @description 是否过期
 * @return bool
 */
func (i *Item) IsExpired() bool {
	if i.expireTime <= 0 {
		return false
	}
	return i.expireTime < ut.Now()
}

// SetIsTimeTime
/*
 * @description 设置为时效物品
 */
func (i *Item) SetIsTimeTime() {
	i.SetBit(true, ITEM_STATUS.TIME_ITEM)
}

// IsTimeItemTimeOut 物品是否过期
//
// Returns:
//   - bool
func (i *Item) IsTimeItemTimeOut() bool {
	return i.Is(int(ITEM_STATUS.TIME_OUT))
}

// IsVipItem 物品是不是vip专用
//
// Returns:
//   - bool
func (i *Item) IsVipItem() bool {
	return i.Config().VipLevelReq > 0
}

// IsVipItemTimeOut 物品是因为vip过期而过期
//
// Returns:
//   - bool
func (i *Item) IsVipItemTimeOut() bool {
	return i.Is(int(ITEM_STATUS.VIP_TIME_OUT))
}

// GetItemSetID 获取物品套装id
//
// Returns:
//   - int
func (i *Item) GetItemSetID() int {
	setId := i.Config().ItemSet
	if setId == 0 {
		return 0
	}
	return 255 & setId
}

// GetItemHitRate 获取装备物品命中率
//
// Returns:
//   - int
func (i *Item) GetItemHitRate() int {
	hit := i.Config().HitRate
	if hit == 0 {
		return 0
	}
	return 255 & hit
}

// GetWeaponFlashType 根据装备镶嵌的数量  获取宝石闪光效果
//
// Returns:
//   - int
func (i *Item) GetWeaponFlashType() int {
	if i.attachDone >= 19 {
		return 4
	}
	if i.attachDone >= 15 {
		return 3
	}
	if i.attachDone >= 10 {
		return 2
	}
	if i.attachDone >= 7 {
		return 1
	}
	return 0
}

// GetPowerValue 获取道具对于指定属性的加成
//
// Parameters:
//   - powerType MyDefine.POWER
//
// Returns:
//   - int
func (i *Item) GetPowerValue(powerType MyDefine.POWER) int {
	val := 0
	// 宠物装备不用看耐久 和过期这些
	if !i.GetIsItemPetEquip() {
		if i.durability <= 0 {
			return 0
		}
		// 过期
		if i.IsTimeItem() && i.IsTimeItemTimeOut() {
			return 0
		}
		// vip过期
		if i.IsVipItem() && i.IsVipItemTimeOut() {
			return 0
		}
	}
	// 1,2,3号属性位置
	val += i.GetPower1AndPower2(powerType)
	json := i.Config()
	// 绑定属性位置
	if i.IsBinded() {
		if i.bindPower1 != nil && i.bindPower1.IsTypeEqual(powerType) {
			val += GetPowerValueAdd(int(i.bindPower1.GetValue()), json.AscensionStar, i.star, i.IsBinded(), i.isAscension(i.bindPower1.GetType()))
		}
		if i.bindPower2 != nil && i.bindPower2.IsTypeEqual(powerType) {
			val += GetPowerValueAdd(int(i.bindPower2.GetValue()), json.AscensionStar, i.star, i.IsBinded(), i.isAscension(i.bindPower2.GetType()))
		}
	}
	// 镶嵌
	if i.attachPower != nil && i.attachPower.IsTypeEqual(powerType) {
		val += int(i.attachPower.GetValue())
	}
	// 附魔
	if i.enchantPower1 != nil && i.enchantPower1.IsTypeEqual(powerType) {
		val += int(i.enchantPower1.GetValue())
	}
	if i.enchantPower2 != nil && i.enchantPower2.IsTypeEqual(powerType) {
		val += int(i.enchantPower2.GetValue())
	}
	// 宠物装备
	if i.GetIsItemPetEquip() {
		if i.power4 != nil && i.power4.IsTypeEqual(powerType) {
			val += int(i.power4.GetValue())
		}
		if i.power5 != nil && i.power5.IsTypeEqual(powerType) {
			val += int(i.power5.GetValue())
		}
		if i.power6 != nil && i.power6.IsTypeEqual(powerType) {
			val += int(i.power6.GetValue())
		}
		if i.power7 != nil && i.power7.IsTypeEqual(powerType) {
			val += int(i.power7.GetValue())
		}
	} else {
		// 进阶
		if i.power4 != nil && i.power4.IsTypeEqual(powerType) {
			val += GetPowerValueAdd(int(i.power4.GetValue()), json.UpgradeAscensionStar, i.upgradeStar, i.IsBinded(), i.isAscension(i.power4.GetType()))
		}
		if i.power5 != nil && i.power5.IsTypeEqual(powerType) {
			val += GetPowerValueAdd(int(i.power5.GetValue()), json.UpgradeAscensionStar, i.upgradeStar, i.IsBinded(), i.isAscension(i.power5.GetType()))
		}
		if i.power6 != nil && i.power6.IsTypeEqual(powerType) {
			val += GetPowerValueAdd(int(i.power6.GetValue()), json.UpgradeAscensionStar, i.upgradeStar, i.IsBinded(), i.isAscension(i.power6.GetType()))
		}
		if i.power7 != nil && i.power7.IsTypeEqual(powerType) {
			val += GetPowerValueAdd(int(i.power7.GetValue()), json.UpgradeAscensionStar, i.upgradeStar, i.IsBinded(), i.isAscension(i.power7.GetType()))
		}
	}

	return val
}

// GetPower1AndPower2 计算道具基础的 1，2，3号属性位置对于某个属性的增加
//
// Parameters:
//   - powerType MyDefine.POWER
//
// Returns:
//   - int
func (i *Item) GetPower1AndPower2(powerType MyDefine.POWER) int {
	val := 0
	json := i.Config()
	if i.GetPower1() != nil && i.GetPower1().IsTypeEqual(powerType) {
		val += GetPowerValueAdd(int(i.GetPower1().GetValue()), json.AscensionStar, i.star, i.IsBinded(), i.isAscension(i.GetPower1().GetType()))
	}
	if i.GetPower2() != nil && i.GetPower2().IsTypeEqual(powerType) {
		val += GetPowerValueAdd(int(i.GetPower2().GetValue()), json.AscensionStar, i.star, i.IsBinded(), i.isAscension(i.GetPower2().GetType()))
	}
	if i.GetPower3() != nil && i.GetPower3().IsTypeEqual(powerType) {
		val += GetPowerValueAdd(int(i.GetPower3().GetValue()), json.AscensionStar, i.star, i.IsBinded(), i.isAscension(i.GetPower3().GetType()))
	}

	return val
}

// GetPowerValueAdd 计算升星属性
//
// Parameters:
//   - value int 属性值
//   - ascensionStar int 升星倍率
//   - star int 升星数量
//   - isBinded bool 是否绑定
//   - isAscension bool 是否进阶属性
//
// Returns:
//   - int
func GetPowerValueAdd(value int, ascensionStar int, star int, isBinded bool, isAscension bool) int {
	val := value
	if !isBinded || !isAscension {
		return val
	}
	if ascensionStar <= 0 || star <= 0 {
		return val
	}
	val = value
	if val < 0 {
		val = -val
	}
	add := (val*ascensionStar*star + 500) / 1000
	return ut.Min(val+add, 32767)
}

// Get 获取装备的部分定义属性
//
// Parameters:
//   - typ EQP.EQP
//
// Returns:
//   - int
func (i *Item) Get(typ EQP.Type) int {
	itemConfig := i.Config()
	if typ == EQP.ITEM_TYPE {
		if i.durability > 0 {
			return int(itemConfig.Type)
		}
		return int(MyDefine.BACK_ERROR_DUR)
	}
	if !i.GetIsItemPetEquip() {
		if i.durability <= 0 {
			return 0
		}
		if i.IsTimeItem() && i.IsTimeItemTimeOut() {
			return 0
		}
		if i.IsVipItem() && i.IsVipItemTimeOut() {
			return 0
		}
	}
	switch typ {
	case EQP.ATK_MIN:
		return GetPowerValueAdd(itemConfig.AtkMin, itemConfig.AscensionStar, i.star, i.IsBinded(), true)
	case EQP.ATK_MAX:
		return GetPowerValueAdd(itemConfig.AtkMax, itemConfig.AscensionStar, i.star, i.IsBinded(), true)
	case EQP.HIT_TIME:
		return lo.If(itemConfig.Type == ITEM_TYPE.WEAPON_ONEHAND_HAND, -1).Else(itemConfig.AtkTime)
	case EQP.ATK_TYPE:
		return lo.If(itemConfig.Type == ITEM_TYPE.WEAPON_ONEHAND_HAND, -1).Else(int(itemConfig.AtkType))
	case EQP.DEF_STR:
		return GetPowerValueAdd(itemConfig.DefStr, itemConfig.AscensionStar, i.star, i.IsBinded(), true)
	case EQP.DEF_AGI:
		return GetPowerValueAdd(itemConfig.DefAgi, itemConfig.AscensionStar, i.star, i.IsBinded(), true)
	case EQP.DEF_MAGIC:
		return GetPowerValueAdd(itemConfig.DefMag, itemConfig.AscensionStar, i.star, i.IsBinded(), true)
	case EQP.HIT_RATE:
		return i.GetItemHitRate()
	case EQP.ITEM_SET_ID:
		return i.GetItemSetID()
	}
	return 0
}

// 判断当前时机能否使用
func (i *Item) IsCanUse(timeEvt ITEM.Type) bool {
	itemType := i.Config().Type
	switch timeEvt {
	case ITEM.CAN_USE_WORLD:
		return itemType == ITEM_TYPE.ANYTIME_USE || itemType == ITEM_TYPE.NOT_BATTLE_USE || itemType == ITEM_TYPE.SKILL_BOOK || itemType == ITEM_TYPE.ADD_PET_LIFE || itemType == ITEM_TYPE.LN_STONE
	case ITEM.CAN_USE_BATTLE:
		return itemType == ITEM_TYPE.ANYTIME_USE || itemType == ITEM_TYPE.BATTLE_USE
	case ITEM.CAN_USE_ALL:
		return true
	}
	return false
}

// 是不是改名卷轴
func (i *Item) IsChangeNameItem() bool { return i.id == int(MyDefine.CHANGE_NAME) }

// 是不是转职书
func (i *Item) IsChangeJobItem() bool {
	return i.id >= int(MyDefine.RANGER) && i.id <= int(MyDefine.BACKUP7)
}

// 是不是宠物蛋
func (i *Item) IsPetEgg() bool {
	return i.GetPower1() != nil && i.GetPower1().IsTypeEqual(MyDefine.POWER_PET_EGG)
}

// 是不是宝箱
func (i *Item) IsChestItem() bool {
	return i.GetPower1() != nil && (i.GetPower1().IsTypeEqual(MyDefine.POWER_CHEST_LV1) || i.GetPower1().IsTypeEqual(MyDefine.POWER_CHEST_LV2) || i.GetPower1().IsTypeEqual(MyDefine.POWER_CHEST_LV3) || i.GetPower1().IsTypeEqual(MyDefine.POWER_CHEST_LV4) || i.GetPower1().IsTypeEqual(MyDefine.POWER_COLOR_BOX))
}

// 是不是国家指令书
func (i *Item) IsCountryBook() bool { return i.id == int(MyDefine.COMMAND_BOOK) }

// 是不是开通仓库格子的物品
func (i *Item) IsOpenStoreItem() bool {
	return i.GetPower1() != nil && i.GetPower1().IsTypeEqual(MyDefine.POWER_OPEN_STORE)
}

// 是不是宠物潜能石
func (i *Item) IsPetAddSkill() bool { return i.id == int(MyDefine.PET_ADD_SKILL) }

// 是不是宠物返老还童石
func (i *Item) IsPetAgeItem() bool { return i.id == int(MyDefine.PET_AGE) }

// 是不是野外修理卷
func (i *Item) IsRepairItem() bool { return i.id == int(MyDefine.REPAIR) }

// 是不是宠物经验书
func (i *Item) IsPetExpItem() bool {
	return i.GetPower1() != nil && i.GetPower1().IsTypeEqual(MyDefine.POWER_PET_ADD_EXP)
}

// 是不是角色经验书
func (i *Item) IsPlayerExpItem() bool {
	return i.GetPower1() != nil && i.GetPower1().IsTypeEqual(MyDefine.POWER_ADD_EXP)
}

// 是不是角色称号物品
func (i *Item) IsTitleItem() bool {
	return i.GetPower1() != nil && i.GetPower1().IsTypeEqual(MyDefine.POWER_POWER_TITLE)
}

// 是不是重置宠物的道具 (宠物洗髓石,宠物重生石头)
func (i *Item) IsPetResetItem() bool {
	return i.id == int(MyDefine.PET_RESET) || i.id == int(MyDefine.PET_RESET_2)
}

// 是不是变性卷轴
func (i *Item) IsChangeSexItem() bool { return i.id == int(MyDefine.CHANGE_SEX) }

// 是不是属性点道具
func (i *Item) IsCpPointAddItem() bool { return i.id == int(MyDefine.CP_POINT_ADD) }

// 是不是技能点道具
func (i *Item) IsSpPointAddItem() bool { return i.id == int(MyDefine.SP_POINT_ADD) }

// 是不是繁荣度道具
func (i *Item) IsProsperityDegreePointAddItem() bool {
	return i.id == int(MyDefine.PROSPERITY_DEGREE_POINT_ADD)
}

// 是不是增加角色技能槽的道具
func (i *Item) IsSkillPlayerItem() bool {
	return i.id == int(MyDefine.SKILL_PLAYER) || i.id == int(MyDefine.SKILL_PLAYER_2) || i.id == int(MyDefine.SKILL_PLAYER_3)
}

// 是不是增加宠物技能槽的道具
func (i *Item) IsSkillPetItem() bool {
	return i.id == int(MyDefine.SKILL_PET) || i.id == int(MyDefine.SKILL_PET_2) || i.id == int(MyDefine.SKILL_PET_3)
}

// 是不是增加背包格子的道具
func (i *Item) IsAddBagSizeItem() bool { return i.id == int(MyDefine.ADD_BAG_SIZE) }

// 是不是增加宠物寿命的道具
func (i *Item) IsPetAddLife() bool { return i.Config().Type == ITEM_TYPE.ADD_PET_LIFE }

// 是不是普通鉴定卷轴
func (i *Item) IsIdentifyScrollItem() bool {
	return i.id == int(MyDefine.IDENTIFY_SCROLL) || i.id == int(MyDefine.IDENTIFY_SCROLL_BIND)
}

// 是不是高级鉴定卷轴
func (i *Item) IsHighIdentifyScrollItem() bool {
	return i.id == int(MyDefine.HIGH_IDENTIFY_SCROLL) || i.id == int(MyDefine.HIGH_IDENTIFY_SCROLL_BIND)
}

// 是不是进阶鉴定卷轴
func (i *Item) IsUpgradeIdentifyScrollItem() bool {
	return i.id == int(MyDefine.UPGRADE_IDENTIFY_SCROLL) || i.id == int(MyDefine.UPGRADE_IDENTIFY_SCROLL_BIND)
}

// 是不是进阶升星卷轴
func (i *Item) IsUpgradeIntensifyScroll() bool {
	return i.id == int(MyDefine.UPGRADE_INTENSIFY_SCROLL) || i.id == int(MyDefine.UPGRADE_INTENSIFY_SCROLL_BIND)
}

// 是不是装备强化卷
func (i *Item) IsStarScroll() bool {
	return i.id == int(MyDefine.STAR_SCROLL)
}

// isAscension 判断一个属性能不能被升星加成
//
// Parameters:
//   - typ MyDefine.POWER
//
// Returns:
//   - bool
func (i *Item) isAscension(typ MyDefine.POWER) bool {
	switch typ {
	case MyDefine.POWER_RECOVER:
		break
	case MyDefine.POWER_SKILL_DAMAGE:
		break
	case MyDefine.POWER_SKILL_HITRATE:
		break
	case MyDefine.POWER_SELF_CRITICAL:
		break
	case MyDefine.POWER_SKILL_HIT_FORCE:
		break
	case MyDefine.POWER_SKILL_MAGIC_PENETRATION:
		break
	case MyDefine.POWER_SKILL_BRK_ARMOR:
		break
	case MyDefine.POWER_SKILL_REMOVE_STATUS:
		break
	case MyDefine.POWER_PET_DAMAGE:
		break
	case MyDefine.POWER_PET_HPMAX_PERCENT:
		break
	case MyDefine.POWER_PET_MPMAX_PERCENT:
		break
	case MyDefine.POWER_PET_STR_PERCENT:
		break
	case MyDefine.POWER_PET_CON_PERCENT:
		break
	case MyDefine.POWER_PET_AGI_PERCENT:
		break
	case MyDefine.POWER_PET_ILT_PERCENT:
		break
	case MyDefine.POWER_PET_WIS_PERCENT:
		break
	case MyDefine.POWER_RECOVER_PERCENT:
		break
	case MyDefine.POWER_OPEN_STORE:
		break
	case MyDefine.POWER_CHEST_LV1:
		break
	case MyDefine.POWER_CHEST_LV2:
		break
	case MyDefine.POWER_CHEST_LV3:
		break
	case MyDefine.POWER_COLOR_BOX:
		break
	case MyDefine.POWER_REQ_SLOT:
		break
	case MyDefine.POWER_TO_WORLDMAP:
		break
	case MyDefine.POWER_TO_GXGY:
		break
	case MyDefine.POWER_EXPIRE_TIME:
		break
	case MyDefine.POWER_RESET_MISSION:
		break
	case MyDefine.POWER_CHEST_KEY_LEVEL:
		break
	case MyDefine.POWER_PET_EGG:
		break
	case MyDefine.POWER_COSTUME:
		break
	case MyDefine.POWER_TRANSPORT:
		break
	case MyDefine.POWER_POWER_TITLE:
		break
	case MyDefine.POWER_GUARD_STR_ATTACK:
		break
	case MyDefine.POWER_GUARD_AGI_ATTACK:
		break
	case MyDefine.POWER_GUARD_MAGIC_ATTACK:
		break
	case MyDefine.POWER_GUARD_CURSE_ATTACK:
		break
	case MyDefine.POWER_GUARD_ALL_ATTACK:
		break
	case MyDefine.POWER_PET_ADD_EXP:
		break
	case MyDefine.POWER_ADD_EXP:
		break
	case MyDefine.POWER_IDENTIFY:
		break
	case MyDefine.POWER_COMPOSITE:
		break
	case MyDefine.POWER_SKILL_HP:
		break
	case MyDefine.POWER_SKILL_HP_PERCENT:
		break
	case MyDefine.POWER_SKILL_MP:
		break
	case MyDefine.POWER_SKILL_MP_PERCENT:
		break
	case MyDefine.POWER_SKILL_LIFE_ABSORPTION:
		break
	case MyDefine.POWER_SKILL_MANA_ABSORPTION:
		break
	case MyDefine.POWER_SKILL_TARGET_BACK:
		break
	case MyDefine.POWER_SKILL_TARGET_MAGIC_BACK:
		break
	case MyDefine.POWER_SKILL_TARGET_BLOCK:
		break
	case MyDefine.POWER_SKILL_TARGET_INSIGHT:
		break
	case MyDefine.POWER_SKILL_TARGET_WIL:
		break
	case MyDefine.POWER_SKILL_TARGET_TOUCH:
		break
	case MyDefine.POWER_GRARD_MASTER_STR_ATTACK:
		break
	case MyDefine.POWER_GRARD_MASTER_AGI_ATTACK:
		break
	case MyDefine.POWER_GRARD_MASTER_MAGIC_ATTACK:
		break
	case MyDefine.POWER_GRARD_MASTER_CURSE_ATTACK:
		break
	case MyDefine.POWER_GRARD_MASTER_ALL_ATTACK:
		break
	case MyDefine.POWER_PET_GRARD_STR_ATTACK:
		break
	case MyDefine.POWER_PET_GRARD_AGI_ATTACK:
		break
	case MyDefine.POWER_PET_GRARD_MAGIC_ATTACK:
		break
	case MyDefine.POWER_PET_GRARD_CURSE_ATTACK:
		break
	case MyDefine.POWER_PET_GRARD_ALL_ATTACK:
		break
	case MyDefine.POWER_SKILL_SCROLL:
		break
	case MyDefine.POWER_SKILL_SCROLL_PET:
		break
	case MyDefine.POWER_NEW_GET_PET:
	case MyDefine.POWER_NEW_GET_ITEM:
	case MyDefine.POWER_ENCHANT_ITEM:
	case MyDefine.POWER_FORMATION_BOOK:
	case MyDefine.POWER_CHEST_LV4:
	case MyDefine.POWER_TURN_MONSTER_CARD:
	case MyDefine.POWER_SKILL_BOOK_PET:
		break
	default:
		return true
	}
	return false
}

// 能不能进行宝石替换
func (i *Item) IsReplaceInlayGem() bool {
	if i.attachPower == nil || i.attachPower.GetValue() < 20 {
		return false
	}
	switch i.attachPower.GetType() {
	case MyDefine.POWER_STR:
		fallthrough
	case MyDefine.POWER_CON:
		fallthrough
	case MyDefine.POWER_AGI:
		fallthrough
	case MyDefine.POWER_ILT:
		fallthrough
	case MyDefine.POWER_WIS:
		return true
	}
	return false
}

func (i *Item) Clone() *Item {
	it := &Item{
		id:          i.id,
		slotPos:     i.slotPos,
		quantity:    i.quantity,
		status:      i.status,
		durability:  i.durability,
		expireTime:  i.expireTime,
		attachDone:  i.attachDone,
		star:        i.star,
		upgradeStar: i.upgradeStar,
		petId:       i.petId,
		petItem:     i.petItem,
	}

	if i.power1 != nil {
		it.power1 = i.power1.Clone()
	}
	if i.power2 != nil {
		it.power2 = i.power2.Clone()
	}
	if i.bindPower1 != nil {
		it.bindPower1 = i.bindPower1.Clone()
	}
	if i.bindPower2 != nil {
		it.bindPower2 = i.bindPower2.Clone()
	}
	if i.power3 != nil {
		it.power3 = i.power3.Clone()
	}
	if i.power4 != nil {
		it.power4 = i.power4.Clone()
	}
	if i.power5 != nil {
		it.power5 = i.power5.Clone()
	}
	if i.power6 != nil {
		it.power6 = i.power6.Clone()
	}
	if i.power7 != nil {
		it.power7 = i.power7.Clone()
	}
	if i.enchantPower1 != nil {
		it.enchantPower1 = i.enchantPower1.Clone()
	}
	if i.enchantPower2 != nil {
		it.enchantPower2 = i.enchantPower2.Clone()
	}
	if i.attachPower != nil {
		it.attachPower = i.attachPower.Clone()
	}

	return it
}
