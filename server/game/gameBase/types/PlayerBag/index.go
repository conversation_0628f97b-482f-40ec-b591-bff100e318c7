package PlayerBag

const (
	EQUIP_POS_START            = 0                                                 // 装备开始位置
	EQUIP_POS_END              = 30                                                // 装备结束位置
	ARMOR_NECKLACE_POS         = 0                                                 // 链
	PET_POS                    = 1                                                 // 宠
	ARMOR_RING_LEFT_POS        = 2                                                 // 戒-左
	ARMOR_FASHION_POS          = 3                                                 // 时装
	ARMOR_BACK_POS             = 4                                                 // 背部
	ARMOR_CLOTHES_POS          = 5                                                 // 衣
	ARMOR_HAND_POS             = 6                                                 // 手
	ARMOR_HEAD_POS             = 7                                                 // 头
	WEAPON_LEFT_POS            = 8                                                 // 武-左
	ARMOR_SHOES_POS            = 9                                                 // 鞋
	WEAPON_RIGHT_POS           = 10                                                // 武-右
	ARMOR_WAIST_POS            = 11                                                // 腰
	ARMOR_TROUSERS_POS         = 12                                                // 裤
	ARMOR_SHOULDER_POS         = 13                                                // 肩
	ARMOR_AMULET_POS           = 14                                                // 护
	ARMOR_TRANSPORT_POS        = 15                                                // 骑
	ARMOR_RING_RIGHT_POS       = 16                                                // 戒-右
	SPIRIT_POS                 = 17                                                // VIP
	BLOOD_BOTTLE_POS           = 18                                                // 血
	MAX_BAG_NUM                = 2                                                 //
	EACH_BAG_SIZE              = 30                                                //
	BAG_START                  = EQUIP_POS_END                                     // 背包实际开始位置
	MAX_BAG_SIZE               = 600                                               //
	MAX_STORE_SIZE             = 60                                                // 个人仓库
	STORE_START                = 630                                               // 个人仓库开始位置（30装备位置 + 600背包位置）
	STORE_END                  = STORE_START + MAX_STORE_SIZE                      // 个人仓库结束位置
	PET_BAG_SIZE               = 8                                                 // 宠物拥有数量上限
	MAX_VIPSTORE_SIZE          = 90                                                // vip仓库大小
	MAX_VIP7STORE_SIZE         = 102                                               // vip7仓库大小
	MAX_VIP8STORE_SIZE         = 114                                               // vip8仓库大小
	MAX_VIP9STORE_SIZE         = 126                                               // vip9仓库大小
	MAX_HIGHT_VIP_5_STORE_SIZE = 20                                                // vip高级仓库 等级5仓库大小
	MAX_HIGHT_VIP_6_STORE_SIZE = 40                                                // vip高级仓库 等级6仓库大小
	MAX_HIGHT_VIP_7_STORE_SIZE = 60                                                // vip高级仓库 等级7仓库大小
	MAX_HIGHT_VIP_8_STORE_SIZE = 80                                                // vip高级仓库 等级8仓库大小
	MAX_HIGHT_VIP_9_STORE_SIZE = 100                                               // vip高级仓库 等级9仓库大小
	VIPSTORE_START             = STORE_END                                         // vip仓库开始位置
	VIPSTORE_END               = VIPSTORE_START + MAX_VIPSTORE_SIZE                // vip仓库结束位置
	VIP7STORE_END              = VIPSTORE_START + MAX_VIP7STORE_SIZE               // vip7仓库结束位置
	VIP8STORE_END              = VIPSTORE_START + MAX_VIP8STORE_SIZE               // vip8仓库结束位置
	VIP9STORE_END              = VIPSTORE_START + MAX_VIP9STORE_SIZE               // vip9仓库结束位置
	HIGHT_VIPSTORE_START       = VIP9STORE_END                                     // vip高级仓库开始位置
	HIGHT_VIP_5_STORE_END      = HIGHT_VIPSTORE_START + MAX_HIGHT_VIP_5_STORE_SIZE // vip5高级仓库结束位置
	HIGHT_VIP_6_STORE_END      = HIGHT_VIPSTORE_START + MAX_HIGHT_VIP_6_STORE_SIZE // vip6高级仓库结束位置
	HIGHT_VIP_7_STORE_END      = HIGHT_VIPSTORE_START + MAX_HIGHT_VIP_7_STORE_SIZE // vip7高级仓库结束位置
	HIGHT_VIP_8_STORE_END      = HIGHT_VIPSTORE_START + MAX_HIGHT_VIP_8_STORE_SIZE // vip8高级仓库结束位置
	HIGHT_VIP_9_STORE_END      = HIGHT_VIPSTORE_START + MAX_HIGHT_VIP_9_STORE_SIZE // vip9高级仓库结束位置
	DUR_CHANGE_TYPE_POINT      = 1
	DUR_CHANGE_TYPE_PERCENT    = 2
	DEAD_DUR_DOWN              = 10
	DUR_DOWN_POINT             = 1
	CLEAR_BAG_SELLING_STATUS   = 0
	CLEAR_BAG_MAIL_SELECT      = 1
	CLEAR_BAG_SHOP_LOCKED      = 2
	CLEAR_BAG_INTEGRAL         = 3
)

type Type int

const (
	EQUIP             = Type(1) // 装备栏
	BAG               = Type(2) // 背包
	STORAGE           = Type(3) // 个人仓库
	VIP_STORAGE       = Type(4) // vip仓库
	HIGHT_VIP_STORAGE = Type(5) // vip高级仓库
)
