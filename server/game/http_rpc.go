package game

import (
	"world/http/h"
)

func (this *Game) InitHttpRpc() {
}

func (this *Game) kickPlayer(uid string) (ret map[string]interface{}, err string) {
	//mgr := mgr.GlobalGetPlayerManager()
	//lock := mgr.LockByPid(uid)
	//defer ut.Unlock(lock)
	//plr, exists := mgr.TryGetPlayerByUid(uid)
	//if !exists {
	//	return h.ResponseErrorNoDataWithDesc("该玩家不存在.")
	//}
	//mgr.GlobalGetPlayerManager().UserKickOffline(plr, true)
	return h.ResponseSuccessNoDataWithDesc("操作成功!")
}
