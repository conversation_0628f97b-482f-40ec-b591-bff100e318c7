package pet

import (
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sPetSkillBookLearnMessageHandler 宠物使用技能书
func C2sPetSkillBookLearnMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_PetSkillBookLearnMessage) protoreflect.ProtoMessage {
		slotPos := int(msg.GetSlotPos())
		petItemSlotPos := int(msg.GetPetItemSlotPos())

		code, skill := gameMgr.Pet().PetSkillBookLearn(player, slotPos, petItemSlotPos)
		return &pbGame.S2C_PetSkillBookLearnMessage{
			Code:  code,
			Skill: skill.ToPb(),
		}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
