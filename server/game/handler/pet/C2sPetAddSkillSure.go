package pet

import (
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sPetAddSkillSureMessageHandler 宠物潜能石结果替换确认
func C2sPetAddSkillSureMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_PetAddSkillSureMessage) protoreflect.ProtoMessage {
		petId := msg.GetPetId()
		return &pbGame.S2C_PetAddSkillSureMessage{
			Code: gameMgr.Pet().ApplyPotencySkill(player, petId),
		}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
