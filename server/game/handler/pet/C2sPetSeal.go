package pet

import (
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sPetSealMessageHandler 宠物封印
func C2sPetSealMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_PetSealMessage) protoreflect.ProtoMessage {
		petId := msg.GetPetId()
		slotPos := int(msg.GetSlotPos())
		typ := int(msg.GetType())
		code, item := gameMgr.Pet().SealPet(player, petId, slotPos, typ)
		return &pbGame.S2C_PetSealMessage{
			Code: code,
			Book: item.ToPb(),
		}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
