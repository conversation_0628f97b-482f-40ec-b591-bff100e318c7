package common

import (
	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"
)

// C2sGmExecuteMessageHandler 执行gm
func C2sGmExecuteMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(plr *gameStruct.Player, msg *pbGame.C2S_GmExecuteMessage) protoreflect.ProtoMessage {
		execute, err := gameMgr.Gm().Execute(msg.GetCmd(), plr, false)
		if err != nil {
			execute = err.Error()
		}
		return &pbGame.S2C_GmExecuteMessage{Reply: execute}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
