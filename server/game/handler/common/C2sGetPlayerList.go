package common

import (
	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
	"world/common/pbGame"
	"world/game/gameMgr"
	ut "world/utils"
)

// C2sGetPlayerListMessageHandler 拉取角色列表
func C2sGetPlayerListMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(session gate.Session, msg *pbGame.C2S_GetPlayerListMessage) protoreflect.ProtoMessage {
		id := session.GetUserID()
		if !ut.IsEmpty(id) {
			list, lastRoleId := gameMgr.Player().GetRoleList(id)
			return &pbGame.S2C_GetPlayerListMessage{
				List:       list,
				LastRoleId: lastRoleId,
			}
		}
		return nil
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
