package common

import (
	"world/common/pbBase/Response"
	"world/common/pbGame"
	"world/game/gameMgr"
	ut "world/utils"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sCreateRoleMessageHandler 创建角色
func C2sCreateRoleMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(session gate.Session, msg *pbGame.C2S_CreateRoleMessage) protoreflect.ProtoMessage {
		id := session.GetUserID()
		if ut.IsEmpty(id) {
			return &pbGame.S2C_CreateRoleMessage{
				Code: Response.ErrPleaseLoginFirst,
			}
		}
		name := msg.GetName()
		if ut.IsEmpty(name) {
			return &pbGame.S2C_CreateRoleMessage{
				Code: Response.ErrPlayerName,
			}
		}
		code, info := gameMgr.Player().CreateRole(id, msg.Sex, msg.Job, msg.Race, name, msg.GetModel())
		return &pbGame.S2C_CreateRoleMessage{
			Code: code,
			Role: info,
		}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
