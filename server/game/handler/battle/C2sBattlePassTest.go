package battle

import (
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sBattlePassTestMessageHandler 战斗测试
func C2sBattlePassTestMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_BattlePassTestMessage) protoreflect.ProtoMessage {

		code := gameMgr.Battle().OnClientBattlePassTest(player, int(msg.GetGroupId()))

		return &pbGame.S2C_BattlePassTestMessage{Code: code}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
