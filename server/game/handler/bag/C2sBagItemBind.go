package bag

import (
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sBagItemBindMessageHandler 物品绑定
func C2sBagItemBindMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_BagItemBindMessage) protoreflect.ProtoMessage {
		return &pbGame.S2C_BagItemBindMessage{
			Code: gameMgr.Item().DoItemBind(player, int(msg.Item.Id), int(msg.Item.SlotPos)),
		}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
