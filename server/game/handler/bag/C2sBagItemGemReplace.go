package bag

import (
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sBagItemGemReplaceMessageHandler 宝石替换
func C2sBagItemGemReplaceMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_BagItemGemReplaceMessage) protoreflect.ProtoMessage {

		return &pbGame.S2C_BagItemGemReplaceMessage{
			Code: gameMgr.Item().DoItemGemReplace(player, int(msg.GetItemSlotPos()), int(msg.GetGemSlotPos())),
		}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
