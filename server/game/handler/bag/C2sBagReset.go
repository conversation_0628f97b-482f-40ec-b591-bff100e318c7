package bag

import (
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameBase/types/PlayerBag"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sBagResetMessageHandler 整理物品
func C2sBagResetMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_BagResetMessage) protoreflect.ProtoMessage {
		code := gameMgr.Item().BagRest(player, msg.GetType())
		replay := &pbGame.S2C_BagResetMessage{
			Code:  code,
			Store: nil,
		}
		if code == 0 {
			replay.Store = player.BagModule().ToPb(PlayerBag.BAG).Store
		}
		return replay
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
