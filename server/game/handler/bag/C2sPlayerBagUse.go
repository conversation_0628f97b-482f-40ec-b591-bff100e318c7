package bag

import (
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/types/known/anypb"
)

// C2sPlayerBagUseMessageHandler 使用物品
func C2sPlayerBagUseMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_PlayerBagUseMessage) protoreflect.ProtoMessage {
		useType := msg.UseType
		itemSlotPos := int(msg.ItemSlotPos)
		itemId := int(msg.ItemId)
		useNum := int(msg.UseNum)
		extraId := int(msg.ExtraId)

		code, val := gameMgr.Item().DoWorldUseItemAction(player, useType, itemSlotPos, itemId, useNum, extraId)

		var vAny *anypb.Any
		if val != nil {
			vAny, _ = anypb.New(val)
		}

		return &pbGame.S2C_PlayerBagUseMessage{
			Code: code,
			Any:  vAny,
		}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
