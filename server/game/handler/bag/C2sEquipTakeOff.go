package bag

import (
	"world/common/pbBase/Response"
	"world/common/pbGame"
	"world/game/gameBase/event"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sEquipTakeOffMessageHandler 脱下装备
func C2sEquipTakeOffMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_EquipTakeOffMessage) protoreflect.ProtoMessage {
		itemId := int(msg.GetItemId())
		pos := int(msg.GetSlotPos())

		code := gameMgr.Item().DoEquipTakeOff(player, itemId, pos)
		if code == Response.NoError {
			// 发送事件
			event.Dispatch(event.EvtAfterEquipChanged, player, int(itemId))
		}
		return &pbGame.S2C_EquipTakeOffMessage{Code: code}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
