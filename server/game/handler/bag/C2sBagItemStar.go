package bag

import (
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sBagItemStarMessageHandler 装备物品升星
func C2sBagItemStarMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_BagItemStarMessage) protoreflect.ProtoMessage {

		code, result := gameMgr.Item().DoItemStar(player, int(msg.GetItem().GetId()), int(msg.GetItem().GetSlotPos()), msg.GetIsUpgrade())
		return &pbGame.S2C_BagItemStarMessage{
			Code:   code,
			Result: result,
		}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
