package bag

import (
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sBagItemIdentifyAnswerMessageHandler 鉴定响应
func C2sBagItemIdentifyAnswerMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_BagItemIdentifyAnswerMessage) protoreflect.ProtoMessage {

		code := gameMgr.Item().DoItemIdentifyAnswer(player, int(msg.GetId()), int(msg.GetSlotPos()))
		return &pbGame.S2C_BagItemIdentifyAnswerMessage{Code: code}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
