package bag

import (
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sBagItemEnchaseMessageHandler 宝石镶嵌
func C2sBagItemEnchaseMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_BagItemEnchaseMessage) protoreflect.ProtoMessage {

		code, isBroken := gameMgr.Item().DoItemEnchase(player, int(msg.ItemSlotPos), int(msg.GemSlotPos))
		return &pbGame.S2C_BagItemEnchaseMessage{
			Code:     code,
			IsBroken: isBroken,
		}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
