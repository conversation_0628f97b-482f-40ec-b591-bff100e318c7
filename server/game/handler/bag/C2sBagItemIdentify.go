package bag

import (
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sBagItemIdentifyMessageHandler 装备物品鉴定
func C2sBagItemIdentifyMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_BagItemIdentifyMessage) protoreflect.ProtoMessage {

		code, item := gameMgr.Item().DoItemIdentify(player, int(msg.GetItem().GetId()), int(msg.GetItem().GetSlotPos()), msg.GetIsUpgrade())
		return &pbGame.S2C_BagItemIdentifyMessage{
			Code: code,
			Item: item.ToPb(),
		}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
