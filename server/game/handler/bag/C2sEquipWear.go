package bag

import (
	"world/common/pbBase/Response"
	"world/common/pbGame"
	"world/game/gameBase/event"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sEquipWearMessageHandler 穿戴装备
func C2sEquipWearMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_EquipWearMessage) protoreflect.ProtoMessage {
		itemId := msg.GetItemId()
		pos := msg.GetSlotPos()

		code := gameMgr.Item().DoEquipWear(player, int(itemId), int(pos))
		if code == Response.NoError {
			// 发送事件
			event.Dispatch(event.EvtAfterEquipChanged, player, int(itemId))
		}
		return &pbGame.S2C_EquipWearMessage{Code: code}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
