package bag

import (
	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"
)

// C2sBagItemSellMessageHandler 物品操作
func C2sBagItemSellMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_BagItemSellMessage) protoreflect.ProtoMessage {
		typ := int(msg.GetType())
		return &pbGame.S2C_BagItemSellMessage{Code: gameMgr.Item().DoItemSell(player, typ, msg.GetList())}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
