package gameMap

import (
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sExitTeamMessageHandler 队员退出队伍
func C2sExitTeamMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_ExitTeamMessage) protoreflect.ProtoMessage {

		code := gameMgr.Team().ExitTeam(player, false)
		return &pbGame.S2C_ExitTeamMessage{Code: code}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
