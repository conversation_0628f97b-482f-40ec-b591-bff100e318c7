package gameMap

import (
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sTeamJoinMessageHandler 加入队伍
func C2sTeamJoinMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_TeamJoinMessage) protoreflect.ProtoMessage {

		return &pbGame.S2C_TeamJoinMessage{Code: gameMgr.Team().ApplyJoinTeam(player, int(msg.GetOtherId()))}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
