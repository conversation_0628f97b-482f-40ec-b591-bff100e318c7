package gameMap

import (
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sDisbandTeamMessageHandler 解散队伍
func C2sDisbandTeamMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_DisbandTeamMessage) protoreflect.ProtoMessage {

		code := gameMgr.Team().DisbandTeam(player)
		return &pbGame.S2C_DisbandTeamMessage{Code: code}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
