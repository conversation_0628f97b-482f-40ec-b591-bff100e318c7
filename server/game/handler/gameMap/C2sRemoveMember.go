package gameMap

import (
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sRemoveMemberMessageHandler 剔除队伍成员
func C2sRemoveMemberMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_RemoveMemberMessage) protoreflect.ProtoMessage {

		code := gameMgr.Team().RemoveMember(player, int(msg.GetMemberId()))
		return &pbGame.S2C_RemoveMemberMessage{Code: code}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
