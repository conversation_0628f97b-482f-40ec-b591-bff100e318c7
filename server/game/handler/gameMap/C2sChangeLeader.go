package gameMap

import (
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sChangeLeaderMessageHandler 切换队长
func C2sChangeLeaderMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_ChangeLeaderMessage) protoreflect.ProtoMessage {

		code := gameMgr.Team().ChangeLeader(player, int(msg.GetMemberId()))
		return &pbGame.S2C_ChangeLeaderMessage{Code: code}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
