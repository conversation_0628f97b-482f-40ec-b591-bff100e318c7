package gameMap

import (
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sPlayerMoveMessageHandler 玩家移动
func C2sPlayerMoveMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_PlayerMoveMessage) protoreflect.ProtoMessage {
		x := int(msg.GetPos().GetX())
		y := int(msg.GetPos().GetY())
		// TODO 判断是否可以移动
		gameMgr.Map().PlayerMove(player, x, y)
		return &pbGame.S2C_PlayerMoveMessage{}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
