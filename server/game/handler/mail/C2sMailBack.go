package mail

import (
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sMailBackMessageHandler 拒收邮件
func C2sMailBackMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_MailBackMessage) protoreflect.ProtoMessage {
		id := msg.GetId()

		return &pbGame.S2C_MailBackMessage{
			Code: gameMgr.Mail().BackMail(player, id),
		}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
