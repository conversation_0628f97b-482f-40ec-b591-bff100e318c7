package mail

import (
	"world/common/pbCross"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// S2rNotifyNewMailMessageHandler 收到新邮件
func S2rNotifyNewMailMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(msg *pbCross.S2R_NotifyNewMailMessage) protoreflect.ProtoMessage {
		code := gameMgr.Mail().ProcessNewMail(msg.GetMail())
		return &pbCross.R2S_SimpleResponseMessage{Code: code}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
