package mail

import (
	"world/common/pbBase"
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"github.com/samber/lo"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sMailAttachAllMessageHandler 全部领取
func C2sMailAttachAllMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_MailAttachAllMessage) protoreflect.ProtoMessage {
		typ := msg.GetType()
		page := msg.GetPage()
		// pageSize := 50
		code, error, money1Change, money3Change, readCnt, r, status := gameMgr.Mail().AttachAllMail(player, int(page), typ)
		return &pbGame.S2C_MailAttachAllMessage{
			Code:     code,
			Error:    error,
			Money1:   int32(money1Change),
			Money3:   int32(money3Change),
			TotalCnt: int32(readCnt),
			Rewards:  lo.Map(r, func(item *gameStruct.Item, index int) *pbBase.ItemData { return item.ToPb() }),
			Status:   status,
		}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
