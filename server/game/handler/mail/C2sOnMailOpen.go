package mail

import (
	"world/common/pbBase"
	"world/common/pbBase/MailDefine"
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"

	"github.com/huyangv/vmqant/module"
	"github.com/samber/lo"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sOnMailOpenMessageHandler 打开邮件界面时获取部分信息
func C2sOnMailOpenMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_OnMailOpenMessage) protoreflect.ProtoMessage {
		cache := player.GetMailCache()
		info := cache.GetNumInfo()
		outInfo := make(map[MailDefine.MAIL_TYPE]*pbBase.MailSimpleNumInfo)

		for typ, stats := range info {
			to, ok := gameStruct.CanMergeMailType[typ]
			if !ok {
				outInfo[typ] = &pbBase.MailSimpleNumInfo{
					Type:   typ,
					Unread: int32(stats.GetUnreadTotal()),
					Read:   int32(stats.GetReadTotal()),
				}
				continue
			}
			realType := to
			if int32(typ) < int32(to) {
				realType = typ
			}
			v := outInfo[realType]
			if v == nil {
				v = &pbBase.MailSimpleNumInfo{
					Type:   realType,
					Unread: 0,
					Read:   0,
				}
				outInfo[realType] = v
			}
			v.Unread += int32(stats.GetUnreadTotal())
			v.Read += int32(stats.GetReadTotal())
		}

		return &pbGame.S2C_OnMailOpenMessage{SimpleNumInfo: lo.Values(outInfo)}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
