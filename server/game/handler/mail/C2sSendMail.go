package mail

import (
	"context"
	"world/common/pbBase/Response"
	"world/common/pbGame"
	"world/db"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sSendMailMessageHandler 发送邮件
func C2sSendMailMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_SendMailMessage) protoreflect.ProtoMessage {
		receiverId := msg.GetToId()
		receiverName := msg.GetToName()
		content := msg.GetContent()
		money1 := msg.GetMoney1()
		money3 := msg.GetMoney3()
		reqMoney1 := msg.GetReqMoney1()
		reqMoney3 := msg.GetReqMoney3()
		appendix := msg.GetAppendix()

		var code Response.Code
		if receiverId > 0 {
			cmd := db.GetRedis().HGet(context.Background(), db.RedisKeyName(), cast.ToString(receiverId))
			code = gameMgr.Mail().PlayerMail(player, int(receiverId), content, cmd.Val(), int(money1), int(money3), int(reqMoney1), int(reqMoney3), appendix)
		} else {
			code = gameMgr.Mail().PlayerMailByName(player, receiverName, content, int(money1), int(money3), int(reqMoney1), int(reqMoney3), appendix)
		}
		return &pbGame.S2C_SendMailMessage{Code: code}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
