package mail

import (
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sMailDeleteMessageHandler 删除邮件
func C2sMailDeleteMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_MailDeleteMessage) protoreflect.ProtoMessage {
		id := msg.GetId()
		typ := msg.GetType()
		return &pbGame.S2C_MailDeleteMessage{Code: gameMgr.Mail().DeleteMail(player, id, typ)}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
