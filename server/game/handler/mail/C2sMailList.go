package mail

import (
	"world/common/pbBase"
	"world/common/pbBase/Response"
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"github.com/samber/lo"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sMailListMessageHandler 获取邮件列表
func C2sMailListMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_MailListMessage) protoreflect.ProtoMessage {
		page := int(msg.GetPage())
		// pageSize := int(msg.GetPageSize()) // 客户端传的值，但服务器固定使用50
		typ := msg.GetType()
		// 参数验证
		if page < 1 {
			page = 1
		}
		// 调用邮件管理器获取邮件列表
		mails, totalCount, code := gameMgr.Mail().GetPlayerMailList(player, typ, page)
		if code != Response.NoError {
			return &pbGame.S2C_MailListMessage{
				Code:       code,
				MailList:   []*pbBase.Mail{},
				TotalCount: 0,
			}
		}

		// 转换为protobuf格式
		mailList := lo.Map(mails, func(mail *gameStruct.Mail, index int) *pbBase.Mail { return mail.ToPbSimple() })

		return &pbGame.S2C_MailListMessage{
			Code:       Response.NoError,
			MailList:   mailList,
			TotalCount: int32(totalCount),
		}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
