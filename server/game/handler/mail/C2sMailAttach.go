package mail

import (
	"world/common/pbBase"
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"github.com/samber/lo"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sMailAttachMessageHandler 获取邮件附件
func C2sMailAttachMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_MailAttachMessage) protoreflect.ProtoMessage {
		mailId := msg.GetId()
		selected := msg.GetSelected()
		code, money1Change, money3Change, r := gameMgr.Mail().AttachMail(player, mailId, msg.GetType(), int(selected))
		return &pbGame.S2C_MailAttachMessage{
			Code:    code,
			Money1:  int32(money1Change),
			Money3:  int32(money3Change),
			Rewards: lo.Map(r, func(item *gameStruct.Item, index int) *pbBase.ItemData { return item.ToPb() }),
		}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
