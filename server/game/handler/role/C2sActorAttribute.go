package role

import (
	"world/common/pbBase/ModelConst"
	"world/common/pbBase/Response"
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"
	ut "world/utils"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sActorAttributeMessageHandler 用户加点操作
func C2sActorAttributeMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_ActorAttributeMessage) protoreflect.ProtoMessage {

		info := ut.ParseMapKV(msg.GetInfo(), func(key int32, value int32) (ModelConst.Type, int32) {
			return ModelConst.Type(key), value
		})

		code := gameMgr.Player().AddAttributePoint(player, info)
		replay := &pbGame.S2C_ActorAttributeMessage{Code: code}
		if code == Response.NoError {
			replay.Cp = int32(player.AttrModule().GetCp())
			replay.Hp = int32(player.AttrModule().GetHp())
			replay.Mp = int32(player.AttrModule().GetMp())
			replay.Str = int32(player.AttrModule().GetStr())
			replay.Con = int32(player.AttrModule().GetCon())
			replay.Agi = int32(player.AttrModule().GetAgi())
			replay.Ilt = int32(player.AttrModule().GetIlt())
			replay.Wis = int32(player.AttrModule().GetWis())
			replay.Money1 = int32(player.BagModule().GetMoney1())
			replay.Money2 = int32(player.BagModule().GetMoney2())
			replay.Money3 = int32(player.BagModule().GetMoney3())
		}
		return replay
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
