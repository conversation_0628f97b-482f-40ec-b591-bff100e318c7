package role

import (
	"world/common/pbBase/ModelConst"
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sLearnSkillByShopMessageHandler 学习技能
func C2sLearnSkillByShopMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_LearnSkillByShopMessage) protoreflect.ProtoMessage {
		shopId := int(msg.GetShopId())
		skillId := int(msg.GetSkillId())
		return &pbGame.S2C_LearnSkillByShopMessage{
			Code:   gameMgr.Skill().LeanSkillByShopId(player, player, shopId, skillId, int(msg.GetSkillLevel())),
			Money1: int32(player.Get(ModelConst.MONEY1)),
			Money2: int32(player.Get(ModelConst.MONEY2)),
			Money3: int32(player.Get(ModelConst.MONEY3)),
		}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
