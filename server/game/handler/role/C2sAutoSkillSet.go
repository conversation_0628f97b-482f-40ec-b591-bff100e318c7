package role

import (
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sAutoSkillSetMessageHandler 自动释放的技能设置
func C2sAutoSkillSetMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_AutoSkillSetMessage) protoreflect.ProtoMessage {
		return &pbGame.S2C_AutoSkillSetMessage{
			Code: gameMgr.Skill().SetSkillAuto(player, msg.GetIsPet(), msg.GetIsActive(), int(msg.GetSkillId())),
		}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
