package node

import (
	"world/common/pbCross"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// S2rBroadcastDisbandTeamMessageHandler 广播解散队伍
func S2rBroadcastDisbandTeamMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(msg *pbCross.S2R_BroadcastDisbandTeamMessage) protoreflect.ProtoMessage {

		gameMgr.Team().OnSyncDisbandTeam(int(msg.MapId), int(msg.Leader))
		return nil
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
