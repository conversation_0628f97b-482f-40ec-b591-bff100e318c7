package node

import (
	"world/common/pbCross"
	"world/game/gameMgr"
	ut "world/utils"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// S2rOnLeaveMessageHandler session离线
func S2rOnLeaveMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(session gate.Session, msg *pbCross.S2R_OnLeaveMessage) protoreflect.ProtoMessage {
		uid := session.GetUserID()
		lock := gameMgr.Player().LockByUid(uid)
		defer ut.Unlock(lock)
		plr, exists := gameMgr.Player().TryGetPlayerByUid(uid)
		if exists {
			gameMgr.Player().Clear(plr)
		} else {
			// 没有角色在线  就有可能是还没进游戏服
			gameMgr.Player().DelPlayerNodeId(uid)
		}
		return nil
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
