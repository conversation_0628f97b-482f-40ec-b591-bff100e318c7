package node

import (
	"world/common/pbCross"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// S2rNotifyPlayerEnterMapMessageHandler 通知，玩家进入某个地图
func S2rNotifyPlayerEnterMapMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(msg *pbCross.S2R_NotifyPlayerEnterMapMessage) protoreflect.ProtoMessage {

		fromMapId := int(msg.GetFromMapId())
		toMapId := int(msg.GetToMapId())
		toMapX := int(msg.GetToMapX())
		toMapY := int(msg.GetToMapY())
		scenePlr, sceneMember := gameMgr.Map().OnSyncPlayerEnterMap(fromMapId, toMapId, toMapX, toMapY, msg.GetPlr(), int(msg.GetLeaderId()))
		return &pbCross.R2S_ReplayPlayerEnterMapMessage{Plr: scenePlr, Members: sceneMember}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
