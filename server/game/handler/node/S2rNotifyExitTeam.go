package node

import (
	"world/common/pbCross"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// S2rNotifyExitTeamMessageHandler 发送到队长节点，退出队伍
func S2rNotifyExitTeamMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(msg *pbCross.S2R_NotifyExitTeamMessage) protoreflect.ProtoMessage {

		gameMgr.Team().ProcessTeamExit(int(msg.GetMapId()), int(msg.GetLeader()), int(msg.GetMember()))
		return nil
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
