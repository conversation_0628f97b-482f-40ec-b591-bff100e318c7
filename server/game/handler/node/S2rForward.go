package node

import (
	"world/common/pbCross"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// S2rForwardMessageHandler 将消息转发到玩家所在节点，然后通过玩家的session发送给客户端
func S2rForwardMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(msg *pbCross.S2R_ForwardMessage) protoreflect.ProtoMessage {

		var player *gameStruct.Player
		// 先通过玩家id找
		id := msg.GetId()
		if id != "" {
			player, _ = gameMgr.Player().TryGetPlayerByUid(id)
		}
		// 如果玩家id找不到，再通过玩家gameId找
		gameId := int(msg.GetGameId())
		if player == nil && gameId > 0 {
			player, _ = gameMgr.Player().TryGetPlayerByGameId(gameId)
		}

		if player == nil {
			return &pbCross.R2S_SimpleResponseMessage{}
		}
		gameMgr.Sender().SendWithBytes(player.Session, msg.GetRouter(), msg.GetMsg(), false)
		return &pbCross.R2S_SimpleResponseMessage{}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
