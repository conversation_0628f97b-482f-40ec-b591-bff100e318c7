package node

import (
	"world/common/pbCross"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// S2rNotifyPlayerMoveMessageHandler 通知，玩家移动
func S2rNotifyPlayerMoveMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(msg *pbCross.S2R_NotifyPlayerMoveMessage) protoreflect.ProtoMessage {

		gameMgr.Map().OnSyncPlayerMove(msg)
		return &pbCross.R2S_SimpleResponseMessage{}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
