package node

import (
	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
	"world/common/pbCross"
	"world/game/gameMgr"
)

// S2rIsPlayerOnlineMessageHandler 查询玩家是不是在节点中在线
func S2rIsPlayerOnlineMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(session gate.Session, msg *pbCross.S2R_IsPlayerOnlineMessage) protoreflect.ProtoMessage {
		uid := msg.GetUid()
		_, exists := gameMgr.Player().TryGetPlayerByUid(uid)
		return &pbCross.R2S_IsPlayerOnlineMessage{Is: exists}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
