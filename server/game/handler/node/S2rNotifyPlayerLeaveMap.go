package node

import (
	"world/common/pbCross"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// S2rNotifyPlayerLeaveMapMessageHandler 通知，玩家离开某个地图
func S2rNotifyPlayerLeaveMapMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(msg *pbCross.S2R_NotifyPlayerLeaveMapMessage) protoreflect.ProtoMessage {

		gameMgr.Map().OnSyncPlayerLeaveMap(msg)
		return &pbCross.R2S_SimpleResponseMessage{}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
