package node

import (
	"world/common/pbCross"
	"world/game/gameMgr"
	ut "world/utils"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// S2rBroadcastAcceptTaskMessageHandler 广播领取任务
func S2rBroadcastAcceptTaskMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(msg *pbCross.S2R_BroadcastAcceptTaskMessage) protoreflect.ProtoMessage {
		mapId := int(msg.GetMapId())
		leaderId := int(msg.GetLeader())
		npcId := int(msg.GetNpcId())
		taskId := int(msg.GetTaskId())

		team, exists := gameMgr.Team().GetTeam(mapId, leaderId)
		if !exists {
			return nil
		}
		result := map[int32]int32{}
		for _, member := range team.Members {
			plr, exists := gameMgr.Player().TryGetPlayerByGameId(member.GameId)
			if !exists {
				continue
			}
			// 加锁
			lock := gameMgr.Player().LockByUid(plr.GetId())
			defer ut.Unlock(lock)
			code, _ := gameMgr.Task().DoAcceptTask(plr, npcId, taskId)
			result[int32(member.GameId)] = int32(code)
		}
		return &pbCross.R2S_ReplayAcceptTaskMessage{
			Result: result,
		}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
