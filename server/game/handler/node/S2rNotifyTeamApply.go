package node

import (
	"world/common/pbCross"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// S2rNotifyTeamApplyMessageHandler 通知，玩家申请入队
func S2rNotifyTeamApplyMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(msg *pbCross.S2R_NotifyTeamApplyMessage) protoreflect.ProtoMessage {

		code := gameMgr.Team().ProcessTeamApply(int(msg.GetLeaderId()), msg.GetApplicant())
		return &pbCross.R2S_SimpleResponseMessage{Code: code}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
