package node

import (
	"world/common/pbCross"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// S2rBroadcastJoinTeamMessageHandler 广播加入队伍
func S2rBroadcastJoinTeamMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(msg *pbCross.S2R_BroadcastJoinTeamMessage) protoreflect.ProtoMessage {

		gameMgr.Team().OnSyncJoinTeam(int(msg.MapId), int(msg.Leader), int(msg.Member), msg.LockMember)
		return nil
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
