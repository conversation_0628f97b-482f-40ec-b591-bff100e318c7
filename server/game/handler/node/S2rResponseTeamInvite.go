package node

import (
	"world/common/pbCross"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// S2rResponseTeamInviteMessageHandler 最终响应入队邀请
func S2rResponseTeamInviteMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(msg *pbCross.S2R_ResponseTeamInviteMessage) protoreflect.ProtoMessage {

		gameMgr.Team().TeamInviteOk(int(msg.To), int(msg.Inviter), int(msg.LeaderId))
		return nil
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
