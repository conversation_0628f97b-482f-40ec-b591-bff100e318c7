package node

import (
	"world/common/pbCross"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// S2rResponseTeamApplyMessageHandler 最终响应入队申请
func S2rResponseTeamApplyMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(msg *pbCross.S2R_ResponseTeamApplyMessage) protoreflect.ProtoMessage {

		gameMgr.Team().TeamApplyOk(int(msg.Applicant), int(msg.LeaderId))
		return nil
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
