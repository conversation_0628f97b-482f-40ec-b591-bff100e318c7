package node

import (
	"context"
	"strings"
	"time"
	"world/base/enum/key"
	"world/base/env"
	"world/common/net_helper"
	"world/common/pbCross"
	"world/common/router"
	"world/db"
	"world/game/gameMgr"
	ut "world/utils"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// S2rOnPlayerLoginMessageHandler 用户选区登录后通知逻辑服
func S2rOnPlayerLoginMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(session gate.Session, msg *pbCross.S2R_OnPlayerLoginMessage) protoreflect.ProtoMessage {
		sTime := time.Now()
		uid := session.GetUserID()
		sid := session.Get(key.PlayerSid)
		if sid == "" || sid != cast.ToString(env.GetSid()) {
			return &pbCross.R2S_OnPlayerLoginMessage{Err: "区服id不匹配"}
		}
		// 分布式全局锁住玩家，防止多点上线
		lock := db.NewRedisLock(db.RedisLockUid(uid), time.Second*10)
		suc, err := lock.TryLock(context.TODO())
		if !suc || err != nil {
			log.Error("[%s] onPlayerLogin 玩家锁出错 error:%s", uid, err.Error())
			return &pbCross.R2S_OnPlayerLoginMessage{Err: err.Error()}
		}
		defer lock.Unlock(context.TODO())
		// 当前节点锁住玩家
		localLock := gameMgr.Player().SetLockByUid(uid)
		defer func() {
			// 解锁，然后移除，因为这个锁是用户的不是玩家的
			ut.Unlock(localLock)
			gameMgr.Player().RemoveLockByUid(uid)
		}()

		// 获取玩家所在节点
		nodeId := gameMgr.Player().GetPlayerNodeId(uid)
		if this.GetServerID() != nodeId {
			if !ut.IsEmpty(nodeId) {
				// 使用用户id让该id关联的玩家离线
				replay, err := net_helper.Invoke(this, nodeId, router.S2RKickPlayerForceByUidMessage, &pbCross.S2R_KickPlayerForceByUidMessage{Uid: uid})
				if err != nil {
					if replay != nil {
						r := &pbCross.R2S_KickPlayerForceMessage{}
						proto.Unmarshal(replay, r)
						if r.Is {

						}
					} else {
						str := err.Error()
						// nofound 可能是上一次的节点没被正确删除
						if !strings.Contains(str, "nofound") {
							return &pbCross.R2S_OnPlayerLoginMessage{Err: str}
						}
					}
				}
			}

			err := gameMgr.Player().SetPlayerNodeId(uid, this.GetServerID())
			if err != nil {
				log.Error("[%s] onPlayerLogin SetPlayerNodeId error:%s", uid, err.Error())
				return &pbCross.R2S_OnPlayerLoginMessage{Err: err.Error()}
			}
		} else {
			// 检查本节点是否在线
			plr, exists := gameMgr.Player().TryGetPlayerByUid(uid)
			if exists {
				gameMgr.Player().UserKickOffline(plr, true)
			}
		}

		log.Info("玩家:%s进入%s区", uid, sid)
		log.Info("onPlayerLogin: %s, during:%fs", uid, time.Since(sTime).Seconds())
		return &pbCross.R2S_OnPlayerLoginMessage{}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
