package task

import (
	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"
)

// C2sAcceptTaskMessageHandler 领取任务
func C2sAcceptTaskMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_AcceptTaskMessage) protoreflect.ProtoMessage {
		code, _ := gameMgr.Task().DoAcceptTask(player, int(msg.GetNpcId()), int(msg.GetTaskId()))
		return &pbGame.S2C_AcceptTaskMessage{Code: code}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
