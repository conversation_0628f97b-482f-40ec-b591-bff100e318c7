package task

import (
	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"
)

// C2sDeleteTaskMessageHandler 放弃任务
func C2sDeleteTaskMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_DeleteTaskMessage) protoreflect.ProtoMessage {
		return &pbGame.S2C_DeleteTaskMessage{
			Code: gameMgr.Task().DoDeleteTask(player, int(msg.GetTaskId())),
		}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
