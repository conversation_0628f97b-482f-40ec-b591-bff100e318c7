package function_handle

import (
	"context"
	"fmt"
	"time"
	"world/base/cfg"
	"world/common/pbBase/Response"
	"world/common/pbBase/MyDefine"
	"world/db"
	"world/game/gameBase/gameStruct"
	ut "world/utils"
	"world/utils/array"

	"github.com/huyangv/vmqant/log"
	"github.com/redis/go-redis/v9"
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

// LimitSimulation 限制模拟状态
type LimitSimulation struct {
	PersonalLimits map[int]int // limitIndex -> 当前已消耗数量
	ServerLimits   map[int]int // limitIndex -> 当前已消耗数量
}

// ChestKeyCost 宝箱钥匙消耗信息
type ChestKeyCost struct {
	ItemId int // 钥匙物品ID
	Count  int // 消耗数量
}

// ChestLimitRecord 限制记录信息
type ChestLimitRecord struct {
	LimitIndex  int
	Count       int
	RefreshTime int64
}

// ChestOpenResult 宝箱开启结果
type ChestOpenResult struct {
	Rewards              []*gameStruct.Condition // 所有奖励
	PersonalLimitRecords []*ChestLimitRecord     // 个人限制记录
	ServerLimitRecords   []*ChestLimitRecord     // 服务器限制记录
}

// checkAndGetChestKeyCost 检查宝箱钥匙消耗并返回消耗信息
func CheckAndGetChestKeyCost(plr *gameStruct.Player, item *gameStruct.Item, useNum int) ([]*ChestKeyCost, Response.Code) {
	bag := plr.BagModule()
	costItemIdAry := make([]int, 0)
	costItemNum := 0
	var numNotEnoughCode Response.Code

	switch item.GetPower1().GetType() {
	case MyDefine.POWER_CHEST_LV1:
		return nil, Response.NoError // LV1宝箱不需要钥匙
	case MyDefine.POWER_CHEST_LV2:
		costItemIdAry = append(costItemIdAry, int(MyDefine.UNIVERSAL_KEY_BIND))
		costItemIdAry = append(costItemIdAry, int(MyDefine.UNIVERSAL_KEY))
		costItemNum = 1
		numNotEnoughCode = Response.E3559
	case MyDefine.POWER_CHEST_LV3:
		costItemIdAry = append(costItemIdAry, int(MyDefine.SECRET_KEY_BIND))
		costItemIdAry = append(costItemIdAry, int(MyDefine.SECRET_KEY))
		costItemNum = 1
		numNotEnoughCode = Response.E3560
	case MyDefine.POWER_CHEST_LV4:
		log.Error("暂未实现开启lv4宝箱类型")
		return nil, Response.E3557
	case MyDefine.POWER_COLOR_BOX:
		log.Error("暂未实现开启color box宝箱类型")
		return nil, Response.E3557
	}

	totalCostNum := costItemNum * useNum
	if totalCostNum <= 0 {
		return nil, Response.NoError
	}

	// 检查钥匙数量是否足够
	sum := lo.SumBy(costItemIdAry, func(itemId int) int { return bag.GetItemNumById(itemId) })
	if sum < totalCostNum {
		return nil, numNotEnoughCode
	}

	// 计算实际消耗分配 优先消耗了绑定钥匙
	keyCosts := make([]*ChestKeyCost, 0)
	remainingCost := totalCostNum

	for _, itemId := range costItemIdAry {
		if remainingCost <= 0 {
			break
		}
		available := bag.GetItemNumById(itemId)
		if available > 0 {
			costCount := ut.Min(available, remainingCost)
			keyCosts = append(keyCosts, &ChestKeyCost{
				ItemId: itemId,
				Count:  costCount,
			})
			remainingCost -= costCount
		}
	}

	return keyCosts, Response.NoError
}

// CommitChestLimits 一次性提交所有宝箱限制
func CommitChestLimits(plr *gameStruct.Player, chestId int, result *ChestOpenResult) error {
	// 提交个人限制
	for _, record := range result.PersonalLimitRecords {
		if record.Count > 0 {
			plr.AddChestLimit(chestId, record.LimitIndex, record.RefreshTime, record.Count)
		}
	}

	// 提交服务器限制
	if len(result.ServerLimitRecords) > 0 {
		// 构造批量更新的数据
		serverLimitUpdates := make(map[int]int)
		serverLimitItems := make([]*cfg.RewardItem, 0)

		for _, record := range result.ServerLimitRecords {
			serverLimitUpdates[record.LimitIndex] = record.Count
			// 创建临时的RewardItem用于传递refreshTime
			item := &cfg.RewardItem{
				LimitIndex:  record.LimitIndex,
				RefreshTime: record.RefreshTime,
			}
			serverLimitItems = append(serverLimitItems, item)
		}

		err := BatchAddServerChestLimit(chestId, serverLimitUpdates, serverLimitItems)
		if err != nil {
			log.Error("批量更新服务器宝箱限制失败: %v", err)
			return err
		}
	}

	return nil
}

// generateChestRewards 生成宝箱奖励（但不记录限制）
func GenerateChestRewards(plr *gameStruct.Player, chestCfg *cfg.Chest[int], totalCnt, useNum int) (*ChestOpenResult, error) {
	result := &ChestOpenResult{
		Rewards:              make([]*gameStruct.Condition, 0),
		PersonalLimitRecords: make([]*ChestLimitRecord, 0),
		ServerLimitRecords:   make([]*ChestLimitRecord, 0),
	}

	// 如果没有随机奖池，只处理固定奖励
	if len(chestCfg.RdReward) == 0 {
		for idx := 0; idx < useNum; idx++ {
			if len(chestCfg.Reward) > 0 {
				fixedRewards := gameStruct.RewardItemConvert(chestCfg.Reward...).All()
				result.Rewards = append(result.Rewards, fixedRewards...)
			}
		}
		return result, nil
	}

	// 创建限制模拟状态
	simulation := &LimitSimulation{
		PersonalLimits: make(map[int]int),
		ServerLimits:   make(map[int]int),
	}

	if chestCfg.HasServerLimit() {
		// 预先分析所有限制项并批量获取服务器限制数据
		serverLimitItems := make([]*cfg.RewardItem, 0)
		serverLimitFields := make([]string, 0)
		for _, item := range chestCfg.RdReward {
			if item.LimitByServer() {
				serverLimitItems = append(serverLimitItems, item)
				serverLimitFields = append(serverLimitFields, fmt.Sprintf("%d", item.LimitIndex))
			}
		}
		// 批量获取服务器限制数据
		serverLimitCounts := make(map[int]int)
		if len(serverLimitFields) > 0 {
			counts, err := BatchCheckServerChestLimit(chestCfg.Id, serverLimitFields, serverLimitItems)
			if err != nil {
				log.Error("批量检查服务器宝箱限制失败: %v", err)
				// 发生错误时，保守处理，认为所有服务器限制物品都已达到上限
				for _, item := range serverLimitItems {
					serverLimitCounts[item.LimitIndex] = item.LimitNum
				}
			} else {
				serverLimitCounts = counts
			}
		}
		// 初始化服务器限制模拟状态
		for limitIndex, count := range serverLimitCounts {
			simulation.ServerLimits[limitIndex] = count
		}
	}

	// 循环生成每个宝箱的奖励
	for idx := 0; idx < useNum; idx++ {
		// 添加固定奖励
		if len(chestCfg.Reward) > 0 {
			fixedRewards := gameStruct.RewardItemConvert(chestCfg.Reward...).All()
			result.Rewards = append(result.Rewards, fixedRewards...)
		}

		// 生成随机奖励
		cnt := totalCnt - len(chestCfg.Reward)
		if cnt > 0 {
			randomRewards, err := GenerateRandomRewards(plr, chestCfg, cnt, simulation, result)
			if err != nil {
				return nil, err
			}
			result.Rewards = append(result.Rewards, randomRewards...)
		}
	}

	return result, nil
}

// GenerateRandomRewards 生成随机奖励
func GenerateRandomRewards(plr *gameStruct.Player, chestCfg *cfg.Chest[int], cnt int, simulation *LimitSimulation, result *ChestOpenResult) ([]*gameStruct.Condition, error) {
	rewards := make([]*gameStruct.Condition, 0)

	// 复制奖池，避免修改原始数据
	rewardPool := array.SliceCopy(chestCfg.RdReward, 0, len(chestCfg.RdReward))

	for cnt > 0 {
		// 过滤奖池：移除已达到限制的物品
		filteredPool := FilterRewardPool(plr, chestCfg.Id, rewardPool, simulation)

		if len(filteredPool) == 0 {
			log.Error("宝箱奖励生成有错误，奖池数量与配置数量不符合！")
			break
		}

		// 随机选择一个奖励
		idx := ut.RandomIndexByWeight(filteredPool, func(item *cfg.RewardItem) int { return item.Rate })
		selectedItem := filteredPool[idx]

		// 添加奖励
		rewards = append(rewards, gameStruct.RewardItemConvert(selectedItem).One())

		// 更新模拟限制状态和结果记录
		if selectedItem.NeedRecord() {
			if selectedItem.LimitBySelf() {
				simulation.PersonalLimits[selectedItem.LimitIndex]++
				// 查找是否已有相同limitIndex的记录
				found := false
				for _, record := range result.PersonalLimitRecords {
					if record.LimitIndex == selectedItem.LimitIndex {
						record.Count++
						found = true
						break
					}
				}
				if !found {
					result.PersonalLimitRecords = append(result.PersonalLimitRecords, &ChestLimitRecord{
						LimitIndex:  selectedItem.LimitIndex,
						Count:       1,
						RefreshTime: selectedItem.RefreshTime,
					})
				}
			}
			if selectedItem.LimitByServer() {
				simulation.ServerLimits[selectedItem.LimitIndex]++
				// 查找是否已有相同limitIndex的记录
				found := false
				for _, record := range result.ServerLimitRecords {
					if record.LimitIndex == selectedItem.LimitIndex {
						record.Count++
						found = true
						break
					}
				}
				if !found {
					result.ServerLimitRecords = append(result.ServerLimitRecords, &ChestLimitRecord{
						LimitIndex:  selectedItem.LimitIndex,
						Count:       1,
						RefreshTime: selectedItem.RefreshTime,
					})
				}
			}
		}

		// 从奖池中移除已选择的物品
		// 需要在原始奖池中找到对应的物品并移除
		for j, poolItem := range rewardPool {
			if poolItem == selectedItem {
				rewardPool = append(rewardPool[:j], rewardPool[j+1:]...)
				break
			}
		}
		cnt--
	}

	return rewards, nil
}

// FilterRewardPool 过滤奖池，移除已达到限制的物品
func FilterRewardPool(plr *gameStruct.Player, chestId int, pool []*cfg.RewardItem, simulation *LimitSimulation) []*cfg.RewardItem {
	return lo.Filter(pool, func(item *cfg.RewardItem, index int) bool {
		if item.LimitBySelf() {
			currentCount := plr.CheckChestLimit(chestId, item.LimitIndex, item.RefreshTime)
			simulatedCount := simulation.PersonalLimits[item.LimitIndex]
			return currentCount+simulatedCount < item.LimitNum
		}
		if item.LimitByServer() {
			simulatedCount := simulation.ServerLimits[item.LimitIndex]
			return simulatedCount < item.LimitNum
		}
		return true
	})
}

// BatchCheckServerChestLimit 批量检查服务器宝箱限制
func BatchCheckServerChestLimit(chestId int, fields []string, items []*cfg.RewardItem) (map[int]int, error) {
	if len(fields) == 0 {
		return make(map[int]int), nil
	}

	key := db.RedisKeyServerChestLimit(chestId)
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()
	// 使用HMGet批量获取
	values, err := db.GetRedis().HMGet(ctx, key, fields...).Result()
	if err != nil {
		return nil, err
	}
	result := make(map[int]int)
	now := ut.Now()
	for i, value := range values {
		if i >= len(items) {
			break
		}
		item := items[i]
		limitIndex := item.LimitIndex
		if value == nil {
			result[limitIndex] = 0
			continue
		}
		encodedValue := cast.ToInt64(value)
		if encodedValue == 0 {
			result[limitIndex] = 0
			continue
		}

		lastResetTime, currentCount := ut.DecodeChestLimitValue(encodedValue)

		// 过期了直接返回0
		if now-lastResetTime > item.RefreshTime {
			result[limitIndex] = 0
		} else {
			result[limitIndex] = currentCount
		}
	}

	return result, nil
}

// BatchAddServerChestLimit 批量增加服务器宝箱限制计数
func BatchAddServerChestLimit(chestId int, updates map[int]int, items []*cfg.RewardItem) error {
	if len(updates) == 0 {
		return nil
	}
	key := db.RedisKeyServerChestLimit(chestId)
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()

	// 创建物品映射，方便查找refreshTime
	itemMap := make(map[int]*cfg.RewardItem)
	for _, item := range items {
		itemMap[item.LimitIndex] = item
	}
	// 使用事务确保原子性
	txf := func(tx *redis.Tx) error {
		// 批量获取当前值
		fields := make([]string, 0, len(updates))
		for limitIndex := range updates {
			fields = append(fields, fmt.Sprintf("%d", limitIndex))
		}
		values, err := tx.HMGet(ctx, key, fields...).Result()
		if err != nil {
			log.Error("BatchAddServerChestLimit - Redis事务中HMGet失败 - key:%s, error:%v", key, err)
			return err
		}
		now := ut.Now()
		pipe := tx.TxPipeline()

		for i, field := range fields {
			limitIndex := cast.ToInt(field)
			cnt := updates[limitIndex]
			item := itemMap[limitIndex]
			if item == nil {
				continue
			}
			currentCount := 0
			timestamp := now

			if i < len(values) && values[i] != nil {
				encodedValue := cast.ToInt64(values[i])
				if encodedValue > 0 {
					lastResetTime, oldCount := ut.DecodeChestLimitValue(encodedValue)
					// 检查是否过期 没过期则应用值
					if now-lastResetTime <= item.RefreshTime {
						currentCount = oldCount
						timestamp = lastResetTime
					}
				}
			}
			newCount := currentCount + cnt
			if newCount <= 0 {
				// 删除记录
				pipe.HDel(ctx, key, field)
			} else {
				// 更新记录
				newValue := ut.EncodeChestLimitValue(timestamp, newCount)
				pipe.HSet(ctx, key, field, newValue)
			}
		}
		_, err = pipe.Exec(ctx)
		if err != nil {
			log.Error("BatchAddServerChestLimit - Redis事务Pipeline执行失败 - key:%s, error:%v", key, err)
		}
		return err
	}
	// 执行事务，最多重试5次
	for retry := 0; retry < 5; retry++ {
		err := db.GetRedis().Watch(ctx, txf, key)
		if err == nil {
			return nil
		}
		log.Error("BatchAddServerChestLimit - Redis事务执行失败 - key:%s, retry:%d, error:%v", key, retry, err)
	}

	log.Error("BatchAddServerChestLimit - Redis事务重试5次后仍然失败 - key:%s", key)
	return fmt.Errorf("BatchAddServerChestLimit - Redis事务重试5次后仍然失败 - key:%s", key)
}
