package gameMgr

import (
	"world/base/cfg"
	"world/common/net_helper"
	"world/common/pbBase/ModelConst"
	"world/common/pbBase/Response"
	"world/common/pbCross"
	"world/common/pb_helper"
	"world/common/router"
	"world/game/gameBase/event"
	"world/game/gameBase/gameStruct"

	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	"github.com/samber/lo"
	"github.com/sasha-s/go-deadlock"
)

var taskManagerLock deadlock.Once
var taskManager *TaskManager

// Task 任务管理
func Task() *TaskManager {
	taskManagerLock.Do(func() {
		taskManager = &TaskManager{}
		event.Require(taskManager)
	})
	return taskManager
}

type TaskManager struct {
	app module.RPCModule
}

func (t *TaskManager) SetApp(app module.RPCModule) { t.app = app }

// DoAcceptTask
/*
 * @description 领取任务
 * @param plr
 * @param npcId npc id
 * @param taskId 任务id
 * @return pbBase.Code
 */
func (t *TaskManager) DoAcceptTask(plr *gameStruct.Player, npcId, taskId int) (Response.Code, *gameStruct.Task) {
	// 身上有没有这个任务
	task := plr.TaskModule().GetTask(taskId)
	if task != nil {
		return Response.YouHaveAlreadyReceivedThisTask, nil
	}
	// 身上最多10个任务
	if len(plr.TaskModule().GetTasks()) >= 10 {
		return Response.CanNotAcceptTaskBecauseSoManyTask, nil
	}
	// 检查任务id和npc的挂载关系
	mapBean, _ := cfg.ContainerMap.GetBeanByUnique(plr.GetMapId())
	npc := mapBean.NpcMap[npcId]
	if npc == nil {
		return Response.TheCurrentMapDoesNotHaveThisNpc, nil
	}
	taskCfg := npc.TaskMap[taskId]
	if taskCfg == nil {
		return Response.NPCDoesNotHaveThisTask, nil
	}

	if !Condition().CheckByConfig(plr, taskCfg.AcceptCondition...) {
		return Response.ConditionsAreNotMet, nil
	}

	task = gameStruct.NewTask(taskCfg.Id)

	event.Dispatch(event.EvtAfterAcceptTask, plr, task)
	plr.TaskModule().AddTask(task)

	// 通知队员
	team, exists := Team().GetTeam(plr.GetMapId(), plr.GetGameId())
	if exists && team.Leader.GameId == plr.GetGameId() {
		replay := net_helper.CallGameSync(t.app, router.S2RBroadcastAcceptTaskMessage, &pbCross.S2R_BroadcastAcceptTaskMessage{
			MapId:  int32(plr.GetMapId()),
			Leader: int32(plr.GetGameId()),
			NpcId:  int32(npcId),
			TaskId: int32(taskId),
		})

		for _, bytes := range replay {
			if bytes == nil {
				continue
			}
			msg := &pbCross.R2S_ReplayAcceptTaskMessage{}
			err := pb_helper.ProtoUnMarshal(bytes, msg)
			if err != nil {
				log.Error("DoAcceptTask 响应解析消息失败:%v", err)
				continue
			}
			for k, v := range msg.Result {
				if Response.Code(v) != Response.NoError {
					log.Error("DoAcceptTask 队员[%d]领取任务失败:%v", k, Response.Code(v))
				}
			}
		}
	}
	return Response.NoError, task
}

// DoSubmitTask
/*
 * @description 提交任务
 * @param plr
 * @param npcId npc id
 * @param taskId 任务id
 * @param itemId 选择的物品id
 * @return pbBase.Code
 */
func (t *TaskManager) DoSubmitTask(plr *gameStruct.Player, npcId, taskId, itemId int) Response.Code {
	mapBean, _ := cfg.ContainerMap.GetBeanByUnique(plr.GetMapId())
	taskCfg, _ := cfg.ContainerMission.GetBeanByUnique(taskId)
	npc := mapBean.NpcMap[npcId]
	if npc == nil || (taskCfg != nil && taskCfg.MapId != plr.GetMapId()) {
		return Response.TheCurrentMapDoesNotHaveThisNpc
	}

	if taskCfg == nil || taskCfg.NpcId != npcId {
		return Response.NPCDoesNotHaveThisTask
	}
	task := plr.TaskModule().GetTask(taskId)
	// 不是直接就能提交的任务，检查身上有没有这个任务
	if !taskCfg.IsDirectSubmitMission() && task == nil {
		return Response.TaskNotExist
	}
	// 检查任务条件
	if !Condition().CheckByConfig(plr, taskCfg.SubmitCondition...) {
		return Response.ConditionsAreNotMet
	}

	funs := make([]func(), 0)

	if taskCfg.Exp > 0 {
		funs = append(funs, func() { Player().AddValue(plr, ModelConst.EXP, taskCfg.Exp) })

		funs = append(funs, func() { Player().AddValue(plr.GetPet(), ModelConst.EXP, taskCfg.Exp) })
	}
	if taskCfg.Money2 > 0 {
		funs = append(funs, func() { Player().AddValue(plr, ModelConst.MONEY2, taskCfg.Money2) })
	}
	if taskCfg.Money3 > 0 {
		funs = append(funs, func() { Player().AddValue(plr, ModelConst.MONEY3, taskCfg.Money3) })
	}

	// 如果有物品奖励  要保证背包足够
	items := make([]*gameStruct.Condition, 0)

	// 物品奖励
	if taskCfg.HasRewardItems() {
		data := gameStruct.RewardItemConvert(taskCfg.RewardItems...)
		items = append(items, data.All()...)
	}
	if taskCfg.HasSelectItems() {
		data := gameStruct.RewardItemConvert(taskCfg.SelectItems...)
		ary := data.All()
		if len(ary) > 0 {
			selectedItem, _ := lo.Find(ary, func(item *gameStruct.Condition) bool { return itemId == item.GetId() })
			if selectedItem == nil {
				selectedItem = ary[0]
			}
			items = append(items, selectedItem)
		}
	}

	if len(items) > 0 {
		for _, item := range items {
			ii := Item().CreateItemById(item.GetId(), item.GetNum())
			for _, iitem := range ii {
				if plr.BagModule().AddItem1(iitem, true) < 0 {
					return Response.SubmitFailedBecauseBagIsFull
				}
			}
		}
	}

	for _, f := range funs {
		f()
	}

	Item().GrantRewards(plr, items)

	// 移除任务
	plr.TaskModule().DeleteTask(taskId)
	// 标记完成状态
	plr.TaskModule().SetTaskStatus(taskId, true)
	// 通知队员
	team, exists := Team().GetTeam(plr.GetMapId(), plr.GetGameId())
	if exists && team.Leader.GameId == plr.GetGameId() {
		replay := net_helper.CallGameSync(t.app, router.S2RBroadcastSubmitTaskMessage, &pbCross.S2R_BroadcastSubmitTaskMessage{
			MapId:  int32(plr.GetMapId()),
			Leader: int32(plr.GetGameId()),
			NpcId:  int32(npcId),
			TaskId: int32(taskId),
		})
		for _, bytes := range replay {
			if bytes == nil {
				continue
			}
			msg := &pbCross.R2S_ReplaySubmitTaskMessage{}
			err := pb_helper.ProtoUnMarshal(bytes, msg)
			if err != nil {
				log.Error("DoSubmitTask 响应解析消息失败:%v", err)
				continue
			}
			for k, v := range msg.Result {
				if Response.Code(v) != Response.NoError {
					log.Error("DoSubmitTask 队员[%d]提交任务失败:%v", k, Response.Code(v))
				}
			}
		}
	}
	return Response.NoError
}

// DoDeleteTask
/*
 * @description 删除任务
 * @param plr
 * @param taskId
 * @return pbBase.Code
 */
func (t *TaskManager) DoDeleteTask(plr *gameStruct.Player, taskId int) Response.Code {
	task := plr.TaskModule().GetTask(taskId)
	if task == nil {
		return Response.TaskNotExist
	}

	plr.TaskModule().DeleteTask(taskId)
	return Response.NoError
}

// AdvanceTask
/*
 * @description 推进任务
 * @param plr
 * @return bool 有进度增加返回true，否则返回false
 */
func (t *TaskManager) AdvanceTask(plr *gameStruct.Player, task *gameStruct.Task) bool {

	return true
}

// AfterAcceptTask  领取任务之后触发
func (t *TaskManager) AfterAcceptTask(plr *gameStruct.Player, task *gameStruct.Task) {

	t.AdvanceTask(plr, task)
}

// AfterBattle 战斗结束后增加杀怪数量
//
// Parameters:
//   - plr *gameStruct.Player
//   - battleGroupId int
func (t *TaskManager) AfterBattle(plr *gameStruct.Player, battleGroupId int) {
	bean, _ := cfg.ContainerGroup.GetBeanByUnique(battleGroupId)
	taskMod := plr.TaskModule()
	for _, task := range taskMod.GetTasks() {
		recordCond := task.GetNeedRecordCondition()
		for _, cond := range recordCond {
			monsterId := cond.Id
			if val := bean.GetGroupedMonsterById(monsterId); val > 0 {
				taskMod.AddKillRecord(monsterId, val, cond.Num)
			}
		}
	}
}
