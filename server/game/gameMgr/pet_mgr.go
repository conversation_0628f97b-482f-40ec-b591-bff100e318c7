package gameMgr

import (
	"fmt"
	"world/base/cfg"
	"world/common/pbBase/Response"
	"world/common/pbBase/ConditionType"
	"world/common/pbBase/ITEM_TYPE"
	"world/common/pbBase/MyDefine"
	"world/game/gameBase/event"
	"world/game/gameBase/gameStruct"
	ut "world/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"github.com/sasha-s/go-deadlock"
)

var petManagerLock deadlock.Once
var petManager *PetManager

// PetManager 宠物管理
func Pet() *PetManager {
	petManagerLock.Do(func() {
		petManager = &PetManager{}
		event.Require(petManager)
	})
	return petManager
}

type PetManager struct {
}

func (p *PetManager) CreatePet(plr *gameStruct.Player, petId int) (*gameStruct.Pet, Response.Code) {
	bean, _ := cfg.ContainerPet.GetBeanByUnique(petId)
	if bean == nil {
		return nil, Response.PetCfgNotExits
	}
	bag := plr.BagModule()
	if bag != nil {
		pets := bag.GetPetItemAry(nil)
		cnt := len(pets)
		if cnt > 0 {
			takeNum := cfg.ContainerMisc_C.GetObj().Pet.TakeNum
			if cnt >= takeNum {
				return nil, Response.PetTakeNumLimit
			}
		}
	}
	petVo := &gameStruct.Pet{}
	petVo.SetCfgId(petId).Create(plr)
	return petVo, Response.NoError
}

// 处理宠物升级后的操作，主要是成长等级和领悟技能
func (p *PetManager) DealPetUpgrade(pet *gameStruct.Pet, level int) {
	if pet.GetGrowLevel() >= 30 || level <= 0 {
		return
	}
	growExp := 0
	l := level
	for {
		if level <= 0 {
			break
		}
		level -= 1
		if ut.Chance(pet.GetGrow()) {
			growExp += 3
		} else if ut.Chance(pet.GetGrow()) {
			growExp += 2
		} else {
			growExp += 1
		}
	}

	log.Debug("宠物升级:%d,获得成长经验: %d", l, growExp)
	pet.AddGrowExp(growExp)
	for {
		if pet.GetGrowExp() < 10 {
			break
		}
		pet.AddGrowLevel(1)
		if pet.GetGrowLevel() >= 30 {
			pet.ClearGrowExp()
			break
		}
		pet.AddGrowExp(-10)
	}

	skill := pet.SkillModule()
	petCfg := pet.GetJson()
	for {
		if l <= 0 {
			break
		}
		l -= 1
		if !ut.Chance(ut.Max(5, int(float64(pet.GetLearn())*0.9))) {
			continue
		}
		// 领悟逻辑
		// 领悟的技能不足2，优先领悟新技能
		learnSkill := pet.GetLearnSkill(false)
		if len(learnSkill) < 2 {
			skillId := petCfg.RandomLearnSkill(lo.Map(learnSkill, func(s *gameStruct.Skill, _ int) int { return s.GetId() }))
			newSkill := gameStruct.NewSkill(skillId, 1, 0, false)
			skill.AddSkill(newSkill)
			continue
		}
		// 否则随机提升一个领悟技能
		sks := pet.GetTalentSkill()
		sks = append(sks, learnSkill...)
		sks = lo.Filter(sks, func(s *gameStruct.Skill, _ int) bool {
			if s.GetBaseLevel() >= s.GetJson(true).ConfigMaxLevel {
				return false
			}

			bs, _ := lo.Find(petCfg.BornSkill, func(bs *cfg.PetBornSkill) bool {
				return s.GetId() == bs.Id
			})
			if bs != nil && s.GetBaseLevel() >= bs.Level {
				return false
			}
			return true
		})
		if len(sks) <= 0 {
			continue
		}
		target := sks[ut.Random(0, len(sks)-1)]
		if target != nil {
			target.AddBxLevel(1)
		}
	}

	gameStruct.ResumeHPMP(pet)
}

// RandomPetPotencySkill 使用潜能石生成两个潜能技能
//
// Parameters:
//   - pet *gameStruct.Pet 宠物
//
// Returns:
//   - []*gameStruct.Skill
func (p *PetManager) RandomPetPotencySkill(pet *gameStruct.Pet) []*gameStruct.Skill {
	petCfg := pet.GetJson()
	// learnSkill := pet.GetLearnSkill(false)
	// exclude := lo.Map(learnSkill, func(s *gameStruct.Skill, _ int) int { return s.Id })

	exclude := make([]int, 0)
	// skill := pet.SkillModule()
	ary := make([]*gameStruct.Skill, 0)
	for i := 0; i < 2; i++ {
		skillId := petCfg.RandomLearnSkill(exclude)
		v := gameStruct.NewSkill(skillId, 1, 0, true)
		max := v.GetJson(true).ConfigMaxLevel
		for j := 1; j <= max; j++ {
			if ut.Chance(10 * j) {
				v.SetBaseLevel(j)
				break
			}
		}
		if v.GetBaseLevel() == 0 {
			v.SetBaseLevel(max)
		}

		ary = append(ary, v)
		exclude = append(exclude, skillId)
	}

	return ary
}

// 应用潜能结果
func (p *PetManager) ApplyPotencySkill(plr *gameStruct.Player, petId int64) Response.Code {
	pet := plr.GetPet(petId)
	if pet == nil {
		return Response.E3520
	}
	skill := pet.GetPotencySkill()
	if len(skill) <= 0 {
		return Response.E3521
	}

	old := pet.GetLearnSkill(true)
	for _, v := range old {
		pet.SkillModule().RemoveSkill(v.GetId())
	}

	for _, v := range skill {
		pet.SkillModule().AddSkill(v)
	}
	return Response.NoError
}

// SealPet 宠物封印
//
// Parameters:
//   - plr *gameStruct.Player
//   - petId int64 宠物id 用于检验
//   - slotPos int 物品位置
//   - typ int 封印类型，只能是0，1，2
//
// Returns:
//   - Response.Code
func (p *PetManager) SealPet(plr *gameStruct.Player, petId int64, slotPos, typ int) (Response.Code, *gameStruct.Item) {
	bag := plr.BagModule()
	item := bag.GetBagItem(slotPos)
	if plr.GetPetId() == petId {
		return Response.E3530, nil
	}
	if item == nil || item.GetPetItem() == nil || item.GetPetId() != petId {
		return Response.E3524, nil
	}
	// 获取可以封印出的技能
	learnSkill := item.GetPetItem().GetJson().LearnSkill

	// 概率数组
	sm := make([]int, 0)
	// 物品数组
	si := make([]*cfg.Item[int], 0)
	for _, ls := range learnSkill {
		if ls.Seal <= 0 {
			continue
		}
		it, _ := lo.Find(cfg.ContainerItem.GetData(), func(v *cfg.Item[int]) bool {
			return v.Type == ITEM_TYPE.SKILL_BOOK && v.Power1.IsTypeEqual(MyDefine.POWER_SKILL_BOOK_PET) && v.Power1.GetValue() == ls.Id && v.Grade == item.GetPetItem().GetJson().Grade
		})
		if it == nil {
			continue
		}
		si = append(si, it)
		sm = append(sm, ls.Seal)
	}

	// 宠物没有配置封印技能书，就不能封印
	if len(sm) <= 0 {
		return Response.E3526, nil
	}

	if typ != 0 && typ != 1 && typ != 2 {
		return Response.E3525, nil
	}
	sealCfg := cfg.ContainerMisc_C.GetObj().Pet.Seal[typ]
	// 需要注意，宠物的品质不等于物品的品质
	cost := (&gameStruct.Condition{}).
		SetType(ConditionType.Type(sealCfg.MoneyType)).
		SetId(0).
		SetNum(sealCfg.MoneyValue[item.GetPetItem().GetJson().Grade])
	// 检查消耗
	if !Condition().Check(plr, cost) {
		return Response.MoneyNotEnough, nil
	}
	// 生成封印书
	if !ut.Chance(sealCfg.Rate) {
		return Response.NoError, nil
	}

	index := ut.RandomIndexByWeight(sm, func(v int) int { return v })
	itemCfg := si[index]
	skillBookItem := Item().CreateItemByItemCfg(itemCfg, 1)[0]
	// 发放宠物书
	if bag.AddItem1(skillBookItem, true) < 0 {
		return Response.ErrBagIsFull, nil
	}
	// 扣除消耗

	bag.AddItem1(skillBookItem, false)
	// 删除宠物
	bag.RemoveBagItemByPos(slotPos, 1)
	Item().DeductCost(plr, cost)
	return Response.NoError, skillBookItem
}

// PetSkillBookLearn 宠物使用技能书
//
// Parameters:
//   - plr *gameStruct.Player
//   - slotPos int 技能书位置
//   - petItemSlotPos int 宠物位置
//
// Returns:
//   - Response.Code
//   - *gameStruct.Skill
func (p *PetManager) PetSkillBookLearn(plr *gameStruct.Player, slotPos, petItemSlotPos int) (Response.Code, *gameStruct.Skill) {
	bag := plr.BagModule()
	bookItem := bag.GetBagItem(slotPos)
	if bookItem == nil {
		return Response.E3512, nil
	}
	petItem := bag.GetItem(petItemSlotPos)
	if petItem == nil || petItem.GetPetItem() == nil {
		return Response.E3520, nil
	}
	pet := petItem.GetPetItem()
	// 要学习的技能id
	bookSkillId := bookItem.GetPower1().GetValue()
	if bookSkillId == 0 {
		return Response.E3529, nil
	}
	// 该技能最高学习等级
	bookSkillLevelMax := bookItem.GetPower2().GetValue()
	if bookSkillLevelMax == 0 {
		return Response.E3529, nil
	}
	// 不可以学习天生可领悟技能
	if lo.ContainsBy(pet.GetJson().LearnSkill, func(v *cfg.PetLearnSkill) bool { return v.Id == bookSkillId }) {
		return Response.E3531, nil
	}
	if lo.ContainsBy(pet.GetJson().BornSkill, func(v *cfg.PetBornSkill) bool { return v.Id == bookSkillId }) {
		return Response.E3531, nil
	}
	// 检查通过技能书学习的数量
	cnt := 0
	for _, v := range pet.SkillModule().GetList() {
		if v != nil && v.IsLearnByBook() {
			cnt++
		}
	}
	if cnt >= cfg.ContainerMisc_C.GetObj().Pet.BookLearnCntMax {
		return Response.E3532, nil
	}

	// 检查已经学习的技能
	skill := pet.SkillModule().GetSkillById(bookSkillId)
	bookSkillLevel := 1
	if skill != nil {
		bookSkillLevel = skill.GetBaseLevel() + 1
	}

	if bookSkillLevel > bookSkillLevelMax {
		return Response.E3528, nil
	}
	if bookSkillLevel >= cfg.GetSkillConfigMaxLevel(bookSkillId) {
		return Response.E3527, nil
	}
	skillCfg, _ := cfg.ContainerSkill.GetBeanByUnique(fmt.Sprintf("%d-%d", bookSkillId, bookSkillLevel))
	if skillCfg == nil {
		return Response.E3529, nil
	}

	code := Skill().DoLearnSkill(nil, pet, bookSkillId, bookSkillLevel, true)
	if code == Response.NoError {
		return Response.NoError, pet.SkillModule().GetSkillById(bookSkillId)
	}
	return code, nil
}
