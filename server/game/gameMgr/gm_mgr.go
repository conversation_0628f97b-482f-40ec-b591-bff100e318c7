package gameMgr

import (
	"errors"
	"strings"
	"world/base/env"
	"world/common/pbBase/ModelConst"
	"world/common/pbBase/Response"
	"world/common/pbGame"
	"world/common/router"
	"world/game/gameBase/gameStruct"
	ut "world/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
	"github.com/spf13/cast"
)

// GmExecutor gm执行代理
type GmExecutor func(player *gameStruct.Player, params ...string) (string, error)

var gmManagerLock deadlock.Once
var gmManager *GmManager

// Gm gm命令
func Gm() *GmManager {
	gmManagerLock.Do(func() {
		gmManager = &GmManager{
			cmdMap: map[string]GmExecutor{},
		}
		gmManager.init()
	})
	return gmManager
}

type GmManager struct {
	cmdMap map[string]GmExecutor
}

// Register 注册cmd脚本 cmd会自动加上@前缀 重复的注册会警告并覆盖
func (g *GmManager) Register(cmd string, executor GmExecutor) *GmManager {
	if ut.IsEmpty(cmd) {
		log.Error("尝试注册Gm执行器，但是Gm代码是空值.")
		return g
	}
	if executor == nil {
		log.Warning("%s:注册了一个空的Gm执行器", cmd)
	}
	if g.cmdMap[cmd] != nil {
		log.Warning("%s:Gm执行器被覆盖", cmd)
	}
	g.cmdMap[cmd] = executor
	return g
}

// Execute command的标准格式应该是@add-diamond 30 或者@add-test 2,1
func (g *GmManager) Execute(command string, player *gameStruct.Player, send bool) (string, error) {
	if player == nil {
		return "", errors.New("gm指令实现失败,玩家数据错误")
	}
	if !strings.HasPrefix(command, "@") {
		return "", errors.New("gm指令格式错误: " + command)
	}
	split := strings.Split(command, " ")
	if len(split) > 2 || len(split) < 1 {
		return "", errors.New("gm指令格式错误: " + command)
	}
	executor := g.cmdMap[split[0]]
	if executor == nil {
		return "", errors.New("gm指令执行失败,没有对应的执行器")
	}
	params := make([]string, 0)
	if len(split) == 2 {
		str := split[1]
		params = strings.Split(str, ",")
	}
	// 丢给解释器去执行
	s, e := executor(player, params...)
	if e == nil {
		log.Info("玩家:%s执行%s.", player.GetGameId(), command)
		if send {
			player.TellPlayerMsg(router.S2CGmExecuteMessage, &pbGame.S2C_GmExecuteMessage{
				Reply: command,
			})
		}
	}
	return s, e
}

// 考虑做成配置表的方式，动态加载
func (g *GmManager) init() {
	if !env.IsDebug() {
		return
	}
	log.Info("------注册gm命令列表------")
	g.Register("@add-currency", func(player *gameStruct.Player, params ...string) (string, error) {
		if len(params) != 2 {
			return "", errors.New("gm指令参数错误")
		}
		typ := cast.ToInt(params[0])
		num := cast.ToInt(params[1])
		Player().AddValue(player, ModelConst.Type(typ), num)
		return "", nil
	})
	// 增加经验值
	g.Register("@add-exp", func(player *gameStruct.Player, params ...string) (string, error) {
		exp := 0
		if len(params) > 0 {
			exp += cast.ToInt(params[0])
		}
		Player().AddValue(player, ModelConst.EXP, exp)
		return "", nil
	})
	// 增加道具
	g.Register("@add-item", func(player *gameStruct.Player, params ...string) (string, error) {
		if len(params) < 2 {
			return "", errors.New("gm指令参数错误")
		}
		id := cast.ToInt(params[0])
		num := cast.ToInt(params[1])
		Item().AddItem(player, id, num)
		return "", nil
	})
	g.Register("@get-attr", func(player *gameStruct.Player, params ...string) (string, error) {
		if len(params) < 1 {
			return "", errors.New("gm指令参数错误")
		}
		typ := cast.ToInt(params[0])
		return cast.ToString(player.Get(ModelConst.Type(typ))), nil
	})
	g.Register("@get-pet-attr", func(player *gameStruct.Player, params ...string) (string, error) {
		if len(params) < 1 {
			return "", errors.New("gm指令参数错误")
		}
		typ := cast.ToInt(params[0])
		pet := player.GetPet()
		if pet == nil {
			return "", errors.New("玩家没有携带宠物")
		}
		return cast.ToString(pet.Get(ModelConst.Type(typ))), nil
	})
	g.Register("@add-pet", func(player *gameStruct.Player, params ...string) (string, error) {
		if len(params) != 1 {
			return "", errors.New("gm指令参数错误")
		}
		petId := cast.ToInt(params[0])
		_, _, code := Item().CreatePetItem(player, petId, true)
		if code != Response.NoError {
			return "添加失败：" + code.String(), nil
		}

		return "", nil
	})
	g.Register("@add-pet-exp", func(player *gameStruct.Player, params ...string) (string, error) {
		if len(params) != 2 {
			return "", errors.New("gm指令参数错误")
		}
		exp := cast.ToInt(params[0])
		petUid := cast.ToInt64(params[1])
		ary := player.BagModule().GetPetItemAry(func(item *gameStruct.Item) bool {
			return item.GetPetId() == petUid
		})

		if len(ary) == 0 {
			return "宠物不存在", nil
		}
		Player().AddPlayerExp(exp, ary[0].GetPetItem())
		return "", nil
	})
}
