package gameMgr

import (
	"world/common/pbBase"
	"errors"
	"fmt"
	"runtime"
	"sync"
	"world/base/cfg"
	"world/base/env"
	"world/common/net_helper"
	"world/common/pbBase/Response"
	"world/common/pbCross"
	"world/common/pbGame"
	"world/common/pbLogin"
	"world/common/pb_helper"
	"world/common/router"
	"world/game/gameBase/gameStruct"
	ut "world/utils"
	"world/utils/array"

	"github.com/bamzi/jobrunner"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	cmap "github.com/orcaman/concurrent-map/v2"
	ants "github.com/panjf2000/ants/v2"
	"github.com/samber/lo"
	"github.com/sasha-s/go-deadlock"
	"google.golang.org/protobuf/reflect/protoreflect"
)

var mapManagerLock deadlock.Once
var mapManager *MapManager

// Map 地图
func Map() *MapManager {
	mapManagerLock.Do(func() {
		mapManager = &MapManager{}
		mapManager.dataMap = cmap.NewWithCustomShardingFunction[int, cmap.ConcurrentMap[int, *mapUnit]](func(key int) uint32 { return uint32(key) % 100 })
		var err error
		mapManager.pool, err = ants.NewPool(256, ants.WithPreAlloc(true))
		if err != nil {
			panic(err)
		}
		runtime.SetFinalizer(mapManager.pool, func(p *ants.Pool) { p.Release() })
		mapManager.loadAllMap()

		if env.IsDebug() {
			jobrunner.Schedule("@every 30s", mapManager)
		}
	})

	return mapManager
}

type mapUnit struct {
	GameId int // 游戏id
	Leader int // 队长id
}

type MapManager struct {
	app     module.RPCModule
	pool    *ants.Pool
	dataMap cmap.ConcurrentMap[int, cmap.ConcurrentMap[int, *mapUnit]] // 外层key是地图id，内层key是玩家gameId
}

func (m *MapManager) SetApp(app module.RPCModule) { m.app = app }

func (m *MapManager) Run() {
	log.Debug("---------开始遍历地图数据---------")
	m.dataMap.IterCb(func(key int, v cmap.ConcurrentMap[int, *mapUnit]) {
		curMapTeamData, exists := Team().dataMap.Get(key)
		if v.Count() <= 0 {
			if exists && curMapTeamData.Count() > 0 {
				log.Debug("❌ 地图ID:%d,Team数据存在,但是Map中玩家数据不存在", key)
				curMapTeamData.IterCb(func(key int, v *teamData) {
					log.Debug("❌ 队长:%d,队员:%v", v.Leader.Leader, lo.Map(v.Members, func(item *mapUnit, index int) int { return item.GameId }))
				})
			}
			return
		}
		log.Debug("地图ID:%d", key)
		log.Debug("在线人数:%d", v.Count())
		log.Debug("队伍数量:%d", curMapTeamData.Count())
		curMapTeamData.IterCb(func(key int, v *teamData) {
			log.Debug("队长:%d,队员:%v", v.Leader.Leader, lo.Map(v.Members, func(item *mapUnit, index int) int { return item.GameId }))
		})
	})
	log.Debug("---------结束遍历地图数据---------")
}

// loadAllMap
/*
 * @description 加载所有地图
 */
func (m *MapManager) loadAllMap() {
	if !m.dataMap.IsEmpty() {
		log.Error("MapManager - loadMap 已经加载过地图数据了")
		return
	}
	configs := cfg.ContainerMap.GetData()
	for _, config := range configs {
		m.dataMap.Set(config.Id, cmap.NewWithCustomShardingFunction[int, *mapUnit](func(key int) uint32 { return uint32(key) % 100 }))
		log.Debug("loadMap,id:%d,name:%s", config.Id, config.Name)
		Team().initMapTeam(config.Id)
	}
}

func (m *MapManager) submitPoolTask(fn func()) error {
	if fn == nil {
		return errors.New("fn is nil")
	}
	return m.pool.Submit(fn)
}

// GetMapNpcAry
/*
 * @description 获取地图存在的npc 后续扩展npc限时出现，等操作
 * @param mapId 地图id
 * @return []int
 */
func (m *MapManager) GetMapNpcAry(mapId int) []int {
	ary := make([]int, 0)
	mapBean, _ := cfg.ContainerMap.GetBeanByUnique(mapId)
	if mapBean != nil {
		ary = lo.Keys(mapBean.NpcMap)
	}
	return ary
}

// 玩家是否在地图中
func (m *MapManager) IsInMap(mapId int, gameId int) bool {
	return m.GetUnit(mapId, gameId) != nil
}

// 获取单位
func (m *MapManager) GetUnit(mapId int, gameId int) *mapUnit {
	mapData, exists := m.dataMap.Get(mapId)
	if !exists {
		return nil
	}
	unit, _ := mapData.Get(gameId)
	return unit
}

func (m *MapManager) GetUnitByPlayer(plr *gameStruct.Player) *mapUnit {
	return m.GetUnit(plr.GetMapId(), plr.GetGameId())
}

// EnterMap
/*
 * @description 进入地图
 * @param plr
 * @param toMapId 目标地图id
 * @param toMapX
 * @param tpMapY
 */
func (m *MapManager) EnterMap(plr *gameStruct.Player, toMapId, toMapX, toMapY int) {
	unit := m.GetUnitByPlayer(plr)
	if unit != nil && unit.Leader != 0 {
		if !Team().IsLeader(plr) {
			Sender().SendTo(plr, router.S2CErrorMessage, &pbLogin.S2C_ErrorMessage{Code: Response.E6011}, false)
			return
		}
		m.TeamEnterMap(plr, toMapId, toMapX, toMapY)
		return
	}

	log.Debug("[%d]进入地图: [%d], 原地图: [%d]", plr.GetGameId(), toMapId, plr.GetMapId())
	fromMapId := plr.GetMapId()
	crossSimplePlayer := plr.ToCrossSimplePb(m.app)

	// 广播所有节点
	replay := net_helper.CallGameSync(m.app, router.S2RNotifyPlayerEnterMapMessage, &pbCross.S2R_NotifyPlayerEnterMapMessage{
		FromMapId: int32(fromMapId),
		ToMapId:   int32(toMapId),
		ToMapX:    int32(toMapX),
		ToMapY:    int32(toMapY),
		Plr:       []*pbBase.CrossSimplePlayer{crossSimplePlayer},
	})

	units := make([]*pbBase.CrossSimplePlayer, 0)
	for _, bytes := range replay {
		if bytes == nil {
			continue
		}
		msg := &pbCross.R2S_ReplayPlayerEnterMapMessage{}
		err := pb_helper.ProtoUnMarshal(bytes, msg)
		if err != nil {
			log.Error("EnterMap 响应解析消息失败:%v", err)
			continue
		}
		units = append(units, msg.Plr...)
	}
	// 通知客户端地图数据
	// npc基础显隐数据必须由服务器来控制
	Sender().SendTo(plr, router.S2CMapDataMessage, &pbGame.S2C_MapDataMessage{
		MapId:   int32(toMapId),
		Pos:     &pbBase.Point{X: int32(toMapX), Y: int32(toMapY)},
		NpcList: ut.ToInt32(m.GetMapNpcAry(toMapId)),
		PlrList: units,
	}, false)
}

// OnSyncPlayerEnterMap
/*
 * @description 同步玩家进入地图数据
 * @param fromMapId 原地图
 * @param toMapId 目标地图
 * @param toMapX
 * @param toMapY
 * @param plr
 */
func (m *MapManager) OnSyncPlayerEnterMap(fromMapId, toMapId, toMapX, toMapY int, plrAry []*pbBase.CrossSimplePlayer, leaderId int) ([]*pbBase.CrossSimplePlayer, []*pbBase.CrossSimplePlayer) {
	log.Debug("数据同步进入地图: [%d], 原地图: [%d]", toMapId, fromMapId)

	isSingle := leaderId == 0
	// 场景玩家数据
	scenePlr := make([]*pbBase.CrossSimplePlayer, 0)
	// 场景队伍数据（自己的队伍）
	sceneMember := make([]*pbBase.CrossSimplePlayer, 0)

	ignoreEnter := make([]int, 0)
	ignoreLeave := make([]int, 0)
	leaveIds := make([]int32, 0)
	for _, v := range plrAry {
		sourceId := int(v.GameId)
		ignoreEnter = append(ignoreEnter, sourceId)
		var unit *mapUnit
		// 离开原地图
		if fromMapId != toMapId {
			mapData, _ := m.dataMap.Get(fromMapId)
			unit, _ = mapData.Get(sourceId)
			if unit != nil {
				mapData.Remove(sourceId)
				leaveIds = append(leaveIds, int32(sourceId))
				ignoreLeave = append(ignoreLeave, sourceId)
			}
		}
		if unit == nil {
			unit = &mapUnit{GameId: sourceId}
		}
		// 进入目标地图
		mapData, _ := m.dataMap.Get(toMapId)
		mapData.Set(sourceId, unit)
		// 如果当前节点有玩家数据，更新玩家地图数据
		player, _ := Player().TryGetPlayerByGameId(sourceId)
		if player != nil {
			// 这里不用锁，上层已经锁住了
			player.SetMapId(toMapId).SetX(toMapX).SetY(toMapY)
		}
		// 这里要手动更新一下位置，不然不对
		v.X = int32(toMapX)
		v.Y = int32(toMapY)
	}

	if leaderId != 0 {
		// 这个锁待测试
		lpc := ut.LPLock(fmt.Sprintf("%d", leaderId))
		defer ut.LPUnlock(lpc)
		data, _ := Team().GetTeam(fromMapId, leaderId)
		if data != nil {
			Team().DelTeam(fromMapId, leaderId)
			Team().AddTeam(toMapId, data)
		}
	}

	mapData, _ := m.dataMap.Get(toMapId)
	mapData.IterCb(func(key int, v *mapUnit) {
		if v == nil {
			return
		}
		plr, exists := Player().TryGetPlayerByGameId(v.GameId)
		if !exists {
			return
		}
		if isSingle {
			if v.GameId == int(plrAry[0].GameId) {
				return
			}
		}
		if lo.ContainsBy(plrAry, func(item *pbBase.CrossSimplePlayer) bool { return item.GameId == int32(v.GameId) }) {
			sceneMember = append(sceneMember, plr.ToCrossSimplePb(m.app))
			return
		}
		scenePlr = append(scenePlr, plr.ToCrossSimplePb(m.app))
	})

	if len(leaveIds) > 0 {
		m.mapNotifyAll(fromMapId, router.S2CLeaveMapMessage, &pbGame.S2C_LeaveMapMessage{Id: leaveIds}, ignoreLeave...)
	}
	if len(plrAry) > 0 {
		m.mapNotifyAll(toMapId, router.S2CEnterMapMessage, &pbGame.S2C_EnterMapMessage{Plr: plrAry}, ignoreEnter...)
	}
	return scenePlr, sceneMember
}

// 离开当前地图
func (m *MapManager) LeaveMap(plr *gameStruct.Player) {
	mapId := plr.GetMapId()
	mapData, _ := m.dataMap.Get(mapId)
	_, exists := mapData.Get(plr.GetGameId())
	if !exists {
		log.Error("LeaveMap 玩家不在地图中,地图id:%d,玩家id:%d", mapId, plr.GetGameId())
		return
	}
	net_helper.CallGameSync(m.app, router.S2RNotifyPlayerLeaveMapMessage, &pbCross.S2R_NotifyPlayerLeaveMapMessage{
		MapId: int32(mapId),
		Id:    int32(plr.GetGameId()),
	})
}

func (m *MapManager) OnSyncPlayerLeaveMap(msg *pbCross.S2R_NotifyPlayerLeaveMapMessage) {
	mapId := int(msg.MapId)
	plrId := int(msg.Id)
	mapData, _ := m.dataMap.Get(mapId)
	_, exists := mapData.Get(plrId)
	if !exists {
		log.Error("OnSyncPlayerLeaveMap 玩家不在地图中,地图id:%d,玩家id:%d", mapId, plrId)
		return
	}
	mapData.Remove(plrId)
	m.mapNotifyAll(mapId, router.S2CLeaveMapMessage, &pbGame.S2C_LeaveMapMessage{Id: []int32{msg.Id}}, plrId)
}

// 玩家移动
func (m *MapManager) PlayerMove(plr *gameStruct.Player, x, y int) {
	if plr.GetX() == x && plr.GetY() == y {
		return
	}
	plr.SetX(x).SetY(y)
	net_helper.CallGameSync(m.app, router.S2RNotifyPlayerMoveMessage, &pbCross.S2R_NotifyPlayerMoveMessage{
		MapId: int32(plr.GetMapId()),
		Id:    int32(plr.GetGameId()),
		X:     int32(x),
		Y:     int32(y),
		Mode:  int32(plr.GetMode()),
	})
}
func (m *MapManager) OnSyncPlayerMove(msg *pbCross.S2R_NotifyPlayerMoveMessage) {
	m.mapNotifyAll(int(msg.MapId), router.S2COtherMoveMessage, &pbGame.S2C_OtherMoveMessage{Id: msg.Id, X: msg.X, Y: msg.Y, Mode: msg.Mode}, int(msg.Id))
}

// mapNotifyAll 通知指定地图中的所有单位
//
// Parameters:
//   - mapId int 地图id
//   - route string 路由
//   - msg protoreflect.ProtoMessage 消息数据
//   - syncSend bool 是否同步发送
//   - excludeGameId ...int 排除的玩家
func (m *MapManager) mapNotifyAll(mapId int, route string, msg protoreflect.ProtoMessage, excludeGameId ...int) {
	log.Debug("地图通知, 地图id: %d, 路由: %s, 消息: %v, 排除玩家: %v", mapId, route, msg, excludeGameId)
	if route == "" || msg == nil {
		log.Error("mapNotifyAll route or msg is nil [%s]", route)
		return
	}

	mapData, exists := m.dataMap.Get(mapId)
	if !exists {
		log.Error("mapNotifyAll mapData not exists [%s]", route)
		return
	}
	// 先收集所有需要通知的玩家
	var units []*mapUnit
	mapData.IterCb(func(key int, v *mapUnit) {
		if v != nil && !lo.Contains(excludeGameId, v.GameId) {
			units = append(units, v)
		}
	})
	var wg sync.WaitGroup
	for _, v := range units {
		if v == nil || lo.Contains(excludeGameId, v.GameId) {
			continue
		}
		wg.Add(1)
		// 注意闭包问题  =,= ...
		id := v.GameId
		err := m.submitPoolTask(func() {
			defer wg.Done()
			plr, exists := Player().TryGetPlayerByGameId(id)
			if !exists {
				return
			}
			Sender().SendTo(plr, route, msg, false)
		})
		if err != nil {
			wg.Done()
			log.Error("mapNotifyAll 提交任务到协程池失败:%v", err)
		}
	}
	wg.Wait()
}

func (m *MapManager) TeamEnterMap(plr *gameStruct.Player, toMapId, toMapX, toMapY int) {
	unit := m.GetUnitByPlayer(plr)
	teamData, exists := Team().GetTeam(plr.GetMapId(), unit.GameId)

	if !exists {
		log.Error("队伍跳图，但是队伍不存在？？")
		return
	}
	// 队长广播
	replay := net_helper.CallGameSync(m.app, router.S2RBroadcastTeamEnterMapMessage, &pbCross.S2R_BroadcastTeamEnterMapMessage{
		FromMapId: int32(plr.GetMapId()),
		ToMapId:   int32(toMapId),
		ToMapX:    int32(toMapX),
		ToMapY:    int32(toMapY),
		Leader:    int32(teamData.Leader.GameId),
		Member:    lo.Map(teamData.Members, func(item *mapUnit, index int) int32 { return int32(item.GameId) }),
	})
	scenePlr := make([]*pbBase.CrossSimplePlayer, 0)
	sceneMember := make([]*pbBase.CrossSimplePlayer, 0)
	for _, bytes := range replay {
		if bytes == nil {
			continue
		}
		msg := &pbCross.R2S_ReplayPlayerEnterMapMessage{}
		err := pb_helper.ProtoUnMarshal(bytes, msg)
		if err != nil {
			log.Error("ProcessTeamEnterMap 响应解析消息失败:%v", err)
			continue
		}
		scenePlr = append(scenePlr, msg.Plr...)
		sceneMember = append(sceneMember, msg.Members...)
	}

	// 便于后面过滤，不发自己的数据
	tmp := append(scenePlr, sceneMember...)

	// 通知所有队员
	for _, v := range teamData.Members {
		tmp := array.SliceCopy(scenePlr)
		for _, m := range sceneMember {
			if int(m.GameId) == v.GameId {
				continue
			}
			tmp = append(tmp, m)
		}
		Sender().ForwardMsgByGameId(v.GameId, router.S2CMapDataMessage, &pbGame.S2C_MapDataMessage{
			MapId:   int32(toMapId),
			Pos:     &pbBase.Point{X: int32(toMapX), Y: int32(toMapY)},
			NpcList: ut.ToInt32(m.GetMapNpcAry(toMapId)),
			PlrList: lo.Filter(tmp, func(item *pbBase.CrossSimplePlayer, index int) bool { return int(item.GameId) != v.GameId }),
		})
	}
	// 通知队长
	Sender().ForwardMsgByGameId(unit.GameId, router.S2CMapDataMessage, &pbGame.S2C_MapDataMessage{
		MapId:   int32(toMapId),
		Pos:     &pbBase.Point{X: int32(toMapX), Y: int32(toMapY)},
		NpcList: ut.ToInt32(m.GetMapNpcAry(toMapId)),
		PlrList: lo.Filter(tmp, func(item *pbBase.CrossSimplePlayer, index int) bool { return int(item.GameId) != unit.GameId }),
	})
	// 通知地图，有队伍进入地图
	m.mapNotifyAll(toMapId, router.S2CBroadcastEnterMapTeamMessage, &pbGame.S2C_BroadcastEnterMapTeamMessage{
		Leader:  int32(teamData.Leader.GameId),
		Members: lo.Map(teamData.Members, func(item *mapUnit, index int) int32 { return int32(item.GameId) }),
	})
}

func (m *MapManager) ProcessTeamEnterMap(fromMapId, toMapId, toMapX, toMapY int, leaderId int, memberIds []int) ([]*pbBase.CrossSimplePlayer, []*pbBase.CrossSimplePlayer) {
	cps := make([]*pbBase.CrossSimplePlayer, 0)
	leader, _ := Player().TryGetPlayerByGameId(leaderId)
	if leader != nil {
		cps = append(cps, leader.ToCrossSimplePb(m.app))
	}

	for _, memberId := range memberIds {
		member, _ := Player().TryGetPlayerByGameId(memberId)
		if member == nil {
			continue
		}
		// 在这里锁队员
		lock := Player().LockByUid(member.GetId())
		defer ut.Unlock(lock)
		cps = append(cps, member.ToCrossSimplePb(m.app))
	}

	scenePlr := make([]*pbBase.CrossSimplePlayer, 0)
	sceneMember := make([]*pbBase.CrossSimplePlayer, 0)
	if len(cps) > 0 {
		replay := net_helper.CallGameSync(m.app, router.S2RNotifyPlayerEnterMapMessage, &pbCross.S2R_NotifyPlayerEnterMapMessage{
			FromMapId: int32(fromMapId),
			ToMapId:   int32(toMapId),
			ToMapX:    int32(toMapX),
			ToMapY:    int32(toMapY),
			Plr:       cps,
			LeaderId:  int32(leaderId),
		})

		for _, bytes := range replay {
			if bytes == nil {
				continue
			}
			msg := &pbCross.R2S_ReplayPlayerEnterMapMessage{}
			err := pb_helper.ProtoUnMarshal(bytes, msg)
			if err != nil {
				log.Error("ProcessTeamEnterMap 响应解析消息失败:%v", err)
				continue
			}
			scenePlr = append(scenePlr, msg.Plr...)
			sceneMember = append(sceneMember, msg.Members...)
		}
	}
	return scenePlr, sceneMember
}
