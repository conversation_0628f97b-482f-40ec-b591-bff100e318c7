package gameMgr

import (
	"world/base/cfg"
	"world/common/pbBase/Response"
	"world/game/gameBase/event"
	"world/game/gameBase/gameStruct"

	"github.com/sasha-s/go-deadlock"
)

var battleManagerLock deadlock.Once
var battleManager *BattleManager

// Battle 战斗管理
func Battle() *BattleManager {
	battleManagerLock.Do(func() {
		battleManager = &BattleManager{}
		event.Require(battleManager)
	})
	return battleManager
}

type BattleManager struct {
}

func (b *BattleManager) OnClientBattlePassTest(plr *gameStruct.Player, battleGroupId int) Response.Code {
	bean, _ := cfg.ContainerGroup.GetBeanByUnique(battleGroupId)
	if bean == nil {
		return Response.ErrBattleIdNotExist
	}

	event.Dispatch(event.EvtAfterBattle, plr, battleGroupId)

	return Response.NoError
}

// AfterBattle 战斗结束后的收尾处理
//
// Parameters:
//   - plr *gameStruct.Player
//   - battleGroupId int
func (b *BattleManager) AfterBattle(plr *gameStruct.Player, battleGroupId int) {

}
