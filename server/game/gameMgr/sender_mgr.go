package gameMgr

import (
	"errors"
	"world/common/net_helper"
	"world/common/pbBase/Response"
	"world/common/pbCross"
	"world/common/pbLogin"
	"world/common/pb_helper"
	"world/common/router"
	"world/game/gameBase/gameStruct"
	ut "world/utils"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	"github.com/sasha-s/go-deadlock"
	"google.golang.org/protobuf/reflect/protoreflect"
)

var senderManagerLock deadlock.Once
var senderManager *SenderManager

// Sender 消息发送
func Sender() *SenderManager {
	senderManagerLock.Do(func() {
		senderManager = &SenderManager{}
	})
	return senderManager
}

type SenderManager struct {
	app module.RPCModule
}

func (s *SenderManager) SetApp(app module.RPCModule) { s.app = app }

func (s *SenderManager) SendWithBytes(session gate.Session, topic string, msg []byte, sync bool) bool {
	if session == nil {
		return false
	}
	fun := session.SendNR
	if sync {
		fun = session.Send
	}
	err := fun(topic, msg)
	if err == "" {
		return true
	}
	log.Error("发送给客户端消息时出错:%s", err)
	return false
}

// Send 发送消息给客户端
func (s *SenderManager) Send(session gate.Session, topic string, msg protoreflect.ProtoMessage, sync bool) bool {
	return s.SendWithBytes(session, topic, pb_helper.ProtoMarshalForce(msg), sync)
}

// SendErr 发送错误消息
func (s *SenderManager) SendErr(session gate.Session, code Response.Code) bool {
	msg := &pbLogin.S2C_ErrorMessage{
		Code: code,
	}
	return s.Send(session, router.S2CErrorMessage, msg, true)
}

// SendTo
/*
 * @description 发送消息给玩家
 * @param player
 * @param topic
 * @param msg
 * @param sync 是否同步发送
 */
func (s *SenderManager) SendTo(player *gameStruct.Player, topic string, msg protoreflect.ProtoMessage, sync bool) bool {
	if player == nil {
		log.Error("发送消息给玩家时出错:player为空.")
		return false
	}
	log.Debug("发送给玩家[%d]的消息:[%s]", player.GetGameId, topic)
	return s.Send(player.Session, topic, msg, sync)
}

// 替代invoke
func (s *SenderManager) SendMsgToNode(nodeId string, route string, msg protoreflect.ProtoMessage) (bytes []byte, err error) {
	return net_helper.Invoke(s.app, nodeId, route, msg)
}

func (s *SenderManager) AsyncSendMsgToNode(nodeId string, route string, msg protoreflect.ProtoMessage) error {
	return net_helper.AsyncInvoke(s.app, nodeId, route, msg)
}

// SendMsgToNodeByPlayerId 通过玩家id 发送消息到玩家所在节点 id和gameId必须传入一个 从redis获取最新节点
//
// Parameters:
//   - id string 玩家id
//   - gameId int 玩家gameId
//   - route string
//   - msg protoreflect.ProtoMessage
//   - retry int 节点找不到时的重找次数 如果为了数据安全同时防止长时间hold 建议1-3次或者0次
//
// Returns:
//   - bytes []byte
//   - err error
func (s *SenderManager) SendMsgToNodeByPlayerId(id string, gameId int, route string, msg protoreflect.ProtoMessage, retry int) (bytes []byte, err error) {
	retry = ut.Max(retry, 0)
	err = errors.New(net_helper.NOT_FOUND)
	for i := 0; i <= retry; i++ {
		nodeId := ""
		if id != "" {
			nodeId = Player().GetPlayerNodeId(id)
		}
		if nodeId == "" && gameId > 0 {
			nodeId = Player().GetPlayerNodeIdByGameId(gameId)
		}
		if nodeId == "" {
			continue
		}
		bytes, err := s.SendMsgToNode(nodeId, route, msg)
		if err == nil {
			return bytes, nil
		}
		if net_helper.IsNodeNotFoundError(err) {
			continue
		}
		log.Error("SendMsgToNodeByPlayerId [%s]-[%d]发送消息失败:%v", route, retry, err)
	}
	return nil, err
}

// 异步不关心结果 通过玩家id 发送消息到玩家所在节点 id和gameId必须传入一个 从redis获取最新节点
func (s *SenderManager) AsyncSendMsgToNodeByPlayerId(id string, gameId int, route string, msg protoreflect.ProtoMessage, retry int) (err error) {
	retry = ut.Max(retry, 0)
	err = errors.New(net_helper.NOT_FOUND)
	for i := 0; i <= retry; i++ {
		nodeId := ""
		if id != "" {
			nodeId = Player().GetPlayerNodeId(id)
		}
		if nodeId == "" && gameId > 0 {
			nodeId = Player().GetPlayerNodeIdByGameId(gameId)
		}
		if nodeId == "" {
			continue
		}
		err := s.AsyncSendMsgToNode(nodeId, route, msg)
		if err == nil {
			return nil
		}
		if net_helper.IsNodeNotFoundError(err) {
			continue
		}
		log.Error("SendMsgToNodeByPlayerId [%s]-[%d]发送消息失败:%v", route, retry, err)
	}
	return err
}

// ForwardMsgWithBytes 将消息转发到玩家所在节点，然后通过玩家的session发送给客户端
//
// Parameters:
//   - id string
//   - gameId int
//   - route string
//   - msg []byte
//   - retry int 节点找不到时的重找次数
//
// Returns:
//   - bytes []byte
//   - err error
func (s *SenderManager) ForwardMsgWithBytes(id string, gameId int, route string, msg []byte, retry int) (bytes []byte, err error) {
	return s.SendMsgToNodeByPlayerId(id, gameId, router.S2RForwardMessage, &pbCross.S2R_ForwardMessage{
		Router: route,
		Msg:    msg,
		Id:     id,
		GameId: int32(gameId),
	}, retry)
}

func (s *SenderManager) ForwardMsg(id string, gameId int, route string, msg protoreflect.ProtoMessage, retry int) (bytes []byte, err error) {
	return s.ForwardMsgWithBytes(id, gameId, route, pb_helper.ProtoMarshalForce(msg), retry)
}

// 通过玩家id  转发一个消息给该玩家 节点找不到时不重试 如需重试请使用ForwardMsg
func (s *SenderManager) ForwardMsgById(id string, router string, msg protoreflect.ProtoMessage) (bytes []byte, err error) {
	return s.ForwardMsg(id, 0, router, msg, 0)
}

// 通过玩家gameId  转发一个消息给该玩家 节点找不到时不重试 如需重试请使用ForwardMsg
func (s *SenderManager) ForwardMsgByGameId(id int, router string, msg protoreflect.ProtoMessage) (bytes []byte, err error) {
	return s.ForwardMsg("", id, router, msg, 0)
}
