package game

import (
	"world/base/enum/NodeState"
	"world/base/enum/key"
	"world/base/env"
	"world/game/gameMgr"
	"world/net"

	mqrpc "github.com/huyangv/vmqant/rpc"

	"github.com/huyangv/vmqant/conf"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	basemodule "github.com/huyangv/vmqant/module/base"
	"github.com/huyangv/vmqant/server"
	"github.com/spf13/cast"
)

var Module = func() module.Module {
	return new(Game)
}

type Game struct {
	basemodule.BaseModule
	middleware *net.Middleware
}

func (this *Game) GetType() string {
	return "game"
}

func (this *Game) Version() string {
	return "1.0.0" //可以在监控时了解代码版本
}

func (this *Game) GetMinClientVersion() string {
	return "1.0.0"
}

// OnAppConfigurationLoaded 当应用配置加载完成时调用
func (this *Game) OnAppConfigurationLoaded(app module.App) {
	this.BaseModule.OnAppConfigurationLoaded(app)
}

func (this *Game) OnInit(app module.App, settings *conf.ModuleSettings) {
	this.BaseModule.OnInit(this, app, settings, func(op *server.Options) {
		op.Metadata = map[string]string{
			key.NodeSid:          cast.ToString(env.GetSid()),
			key.MinClientVersion: this.GetMinClientVersion(),
		}
	})

	gameMgr.Gm()
	gameMgr.Player()
	gameMgr.Sender().SetApp(this)
	gameMgr.Map().SetApp(this)
	gameMgr.Team().SetApp(this)
	gameMgr.Task().SetApp(this)
	this.middleware = net.Create(this)
	InitRpc(this)
	this.InitHttpRpc()
}

func (this *Game) Run(closeSig chan bool) {
	<-closeSig
	log.Info("%v模块已停止 正在保存信息...", this.GetType())
	// 关闭区服
	// 保存玩家数据数据
	this.Offline(false)
}

func (this *Game) OnDestroy() {
	this.BaseModule.OnDestroy()
}

func (this *Game) InvokeSelf(route string, args ...any) (result interface{}, err string) {
	return this.Invoke(this.GetServerID(), route, args...)
}

func (this *Game) GetModuleServer() server.Server {
	return this.GetServer()
}

func (this *Game) GetRpcServer() mqrpc.RPCServer {
	return this.GetServer().GetRpcServer()
}

// 节点下线, 分为两种：1.单纯保存,清理玩家数据，玩家会在别的节点重新拉取数据 2.保存玩家数据，然后踢下线
func (this *Game) Offline(kick bool) {
	this.setMeta(key.NodeState, NodeState.ReadyOffline)
	if kick {
		gameMgr.Player().OfflineAll()
	} else {
		gameMgr.Player().ClearAll()
	}
	this.setMeta(key.NodeState, NodeState.Offline)
}

func (this *Game) setMeta(key string, value string) {
	this.GetServer().Options().Metadata["state"] = NodeState.Offline
	this.GetServer().ServiceRegister()
}
