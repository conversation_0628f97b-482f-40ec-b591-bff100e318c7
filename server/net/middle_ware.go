package net

import (
	"reflect"
	"strings"
	"world/common/ecode"
	"world/game/gameMgr"
	ut "world/utils"

	"github.com/huyangv/vmqant/module"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"github.com/sasha-s/go-deadlock"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
)

func checkPlayerArg(str string) bool {
	return str == "*structs.Player" || str == "*gameStruct.Player"
}

type HandlerFun struct {
	in []reflect.Type // 方法参数类型列表
	ft reflect.Type   // 保留
	fv reflect.Value  // 方法
}

func Create(mod ServerModule) *Middleware {
	return &Middleware{
		mod:   mod,
		route: make(map[string]*HandlerFun),
	}
}

type Middleware struct {
	route map[string]*HandlerFun
	mod   ServerModule
	lock  deadlock.RWMutex
}

// Deprecated: 弃用
func (this *Middleware) Register(id string, f interface{}) *Middleware {
	this.lock.Lock()
	defer this.lock.Unlock()
	hf := &HandlerFun{
		in: make([]reflect.Type, 0),
		ft: reflect.ValueOf(f).Type(),
		fv: reflect.ValueOf(f),
	}
	for i := 0; i < hf.ft.NumIn(); i++ {
		rv := hf.ft.In(i)
		hf.in = append(hf.in, rv)
	}
	// 注册
	this.route[id] = hf
	return this
}

// Deprecated: 弃用
func (this *Middleware) Off(id string) {
	this.lock.Lock()
	defer this.lock.Unlock()
	delete(this.route, id)
}

func (this *Middleware) Wrap(msgId string, f func(mod module.RPCModule) any, opts ...WrapOptionString) (string, any) {
	logPrint := true //默认打印日志
	setLock := true  //如果函数的第一个参为structs.Player，那么默认要操作角色数据，这里默认加锁
	if opts != nil {
		opt := opts[0]
		logPrint = ut.If(opt.Contains("log=false"), false, logPrint)
		setLock = ut.If(opt.Contains("lock=false"), false, setLock)
	}
	// 执行注册方法
	realFunc := f(this.mod.(module.RPCModule))
	// 创建反射对象
	funcValue := reflect.ValueOf(realFunc)
	funcType := reflect.ValueOf(realFunc).Type()
	argTypes := make([]reflect.Type, 0)
	for i := 0; i < funcType.NumIn(); i++ {
		rv := funcType.In(i)
		argTypes = append(argTypes, rv)
	}
	// 用来保证只进行一次检查
	skipCheckPlayer := false
	argsHasPlayer := false

	logic := func(args ...any) (res any, errStr string) {
		uid := ""
		traceId := ""

		transArg := func(typ reflect.Type, value any) reflect.Value {
			switch ts := typ.String(); ts {
			case "*structs.Player":
				fallthrough
			case "*gameStruct.Player":
				session := value.(gate.Session)
				uid := session.GetUserID()
				plr, _ := gameMgr.Player().TryGetPlayerByUid(uid)
				if plr == nil {
					return reflect.ValueOf(nil)
				}
				return reflect.ValueOf(plr)
			case "module.RPCModule":
				return reflect.ValueOf(this.mod)
			}
			return reflect.ValueOf(value)
		}

		// 对于接收参数列表，如果包含*structs.Player类型就要判断一下
		if !skipCheckPlayer {
			_, exists := lo.Find(argTypes, func(r reflect.Type) bool {
				return checkPlayerArg(r.String())
			})
			argsHasPlayer = exists
			skipCheckPlayer = true
		}
		if len(args) > 0 && argsHasPlayer {
			arg, e := lo.Find(args, func(arg any) bool {
				_, ok := arg.(gate.Session)
				return ok
			})
			if e {
				session, _ := arg.(gate.Session)
				uid = session.GetUserID()
				traceId = session.TraceID()
				if checkPlayerArg(argTypes[0].String()) {
					if setLock {
						lock := gameMgr.Player().LockByUid(uid)
						defer ut.Unlock(lock)
					}
					plr, _ := gameMgr.Player().TryGetPlayerByUid(uid)
					if plr == nil || !plr.IsValid() {
						log.Error("PLAYER_NOT_FOUND [%s] [%s] %v", traceId, uid, plr == nil)
						return nil, ecode.PLAYER_NOT_FOUND.String() //抛回上层，重新找节点
					}
				}
			}
		}

		if logPrint {
			log.Info("I - %s [%s] [%s] %+v", msgId, traceId, uid, args)
		}

		in := make([]reflect.Value, 0)
		// 组合参数
		for i, t := range argTypes {
			_arg := transArg(t, args[i])
			in = append(in, _arg)
		}

		replay := funcValue.Call(in)

		// rpc 均返回一个正常值和一个字符串错误
		if len(replay) == 2 {
			if logPrint {
				log.Info("O - %s [%s] [%s] %+v", msgId, traceId, uid, replay[0].Interface())
			}
			return replay[0].Interface(), cast.ToString(replay[1].Interface())
		}
		_recover := replay[0]
		if _recover.Interface() != nil {
			j, ok := _recover.Interface().(proto.Message)
			if ok {
				if logPrint {
					log.Info("O - %s [%s] [%s] %+v", msgId, traceId, uid, j)
				}
				msgBytes, err := proto.Marshal(j)
				if err != nil {
					errStr = err.Error()
				}
				return msgBytes, errStr
			}
		}
		return
	}

	// 注册
	this.mod.GetModuleServer().RegisterGO(msgId, logic)
	// 修改
	rpcServer := this.mod.GetRpcServer()
	functionInfo := rpcServer.GetFunction(msgId)
	// 方法
	functionInfo.FuncType = funcType
	// 形参
	functionInfo.InType = argTypes
	return msgId, logic
}

// WrapOptionString 包装option字符串
type WrapOptionString string

func (ws WrapOptionString) String() string {
	return string(ws)
}

func (ws WrapOptionString) Contains(str string) bool {
	split := strings.Split(ws.String(), "|")
	_, _, exists := lo.FindIndexOf(split, func(sps string) bool { return str == sps })
	return exists
}
