// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v6.30.1
// source: pbBase/Job/Job.proto

package Job

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// 职业类型
type Type int32

const (
	ZeroNone Type = 0  //未知
	Ranger   Type = 1  //侠客
	XiuZhen  Type = 2  //修真
	Warrior  Type = 3  //战士
	Wizard   Type = 4  //法师
	New      Type = 5  //贤者
	Backup2  Type = 6  //武圣
	PetWil   Type = 7  //睿智
	PetStr   Type = 8  //勇猛
	PetAgi   Type = 9  //迅捷
	Backup3  Type = 10 //枪王
	ChuiShi  Type = 11 //锤师
	LanWu    Type = 12 //岚舞
)

// Enum value maps for Type.
var (
	Type_name = map[int32]string{
		0:  "ZeroNone",
		1:  "Ranger",
		2:  "XiuZhen",
		3:  "Warrior",
		4:  "Wizard",
		5:  "New",
		6:  "Backup2",
		7:  "PetWil",
		8:  "PetStr",
		9:  "PetAgi",
		10: "Backup3",
		11: "ChuiShi",
		12: "LanWu",
	}
	Type_value = map[string]int32{
		"ZeroNone": 0,
		"Ranger":   1,
		"XiuZhen":  2,
		"Warrior":  3,
		"Wizard":   4,
		"New":      5,
		"Backup2":  6,
		"PetWil":   7,
		"PetStr":   8,
		"PetAgi":   9,
		"Backup3":  10,
		"ChuiShi":  11,
		"LanWu":    12,
	}
)

func (x Type) Enum() *Type {
	p := new(Type)
	*p = x
	return p
}

func (x Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Type) Descriptor() protoreflect.EnumDescriptor {
	return file_pbBase_Job_Job_proto_enumTypes[0].Descriptor()
}

func (Type) Type() protoreflect.EnumType {
	return &file_pbBase_Job_Job_proto_enumTypes[0]
}

func (x Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Type.Descriptor instead.
func (Type) EnumDescriptor() ([]byte, []int) {
	return file_pbBase_Job_Job_proto_rawDescGZIP(), []int{0}
}

var File_pbBase_Job_Job_proto protoreflect.FileDescriptor

var file_pbBase_Job_Job_proto_rawDesc = []byte{
	0x0a, 0x14, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x4a, 0x6f, 0x62, 0x2f, 0x4a, 0x6f, 0x62,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4a, 0x6f,
	0x62, 0x2a, 0xa5, 0x01, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x5a, 0x65,
	0x72, 0x6f, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x58, 0x69, 0x75, 0x5a, 0x68, 0x65, 0x6e, 0x10,
	0x02, 0x12, 0x0b, 0x0a, 0x07, 0x57, 0x61, 0x72, 0x72, 0x69, 0x6f, 0x72, 0x10, 0x03, 0x12, 0x0a,
	0x0a, 0x06, 0x57, 0x69, 0x7a, 0x61, 0x72, 0x64, 0x10, 0x04, 0x12, 0x07, 0x0a, 0x03, 0x4e, 0x65,
	0x77, 0x10, 0x05, 0x12, 0x0b, 0x0a, 0x07, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x32, 0x10, 0x06,
	0x12, 0x0a, 0x0a, 0x06, 0x50, 0x65, 0x74, 0x57, 0x69, 0x6c, 0x10, 0x07, 0x12, 0x0a, 0x0a, 0x06,
	0x50, 0x65, 0x74, 0x53, 0x74, 0x72, 0x10, 0x08, 0x12, 0x0a, 0x0a, 0x06, 0x50, 0x65, 0x74, 0x41,
	0x67, 0x69, 0x10, 0x09, 0x12, 0x0b, 0x0a, 0x07, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x33, 0x10,
	0x0a, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x68, 0x75, 0x69, 0x53, 0x68, 0x69, 0x10, 0x0b, 0x12, 0x09,
	0x0a, 0x05, 0x4c, 0x61, 0x6e, 0x57, 0x75, 0x10, 0x0c, 0x42, 0x1d, 0x5a, 0x1b, 0x77, 0x6f, 0x72,
	0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65,
	0x2f, 0x4a, 0x6f, 0x62, 0x3b, 0x4a, 0x6f, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbBase_Job_Job_proto_rawDescOnce sync.Once
	file_pbBase_Job_Job_proto_rawDescData = file_pbBase_Job_Job_proto_rawDesc
)

func file_pbBase_Job_Job_proto_rawDescGZIP() []byte {
	file_pbBase_Job_Job_proto_rawDescOnce.Do(func() {
		file_pbBase_Job_Job_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbBase_Job_Job_proto_rawDescData)
	})
	return file_pbBase_Job_Job_proto_rawDescData
}

var file_pbBase_Job_Job_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pbBase_Job_Job_proto_goTypes = []interface{}{
	(Type)(0), // 0: proto.Job.Type
}
var file_pbBase_Job_Job_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbBase_Job_Job_proto_init() }
func file_pbBase_Job_Job_proto_init() {
	if File_pbBase_Job_Job_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbBase_Job_Job_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbBase_Job_Job_proto_goTypes,
		DependencyIndexes: file_pbBase_Job_Job_proto_depIdxs,
		EnumInfos:         file_pbBase_Job_Job_proto_enumTypes,
	}.Build()
	File_pbBase_Job_Job_proto = out.File
	file_pbBase_Job_Job_proto_rawDesc = nil
	file_pbBase_Job_Job_proto_goTypes = nil
	file_pbBase_Job_Job_proto_depIdxs = nil
}
