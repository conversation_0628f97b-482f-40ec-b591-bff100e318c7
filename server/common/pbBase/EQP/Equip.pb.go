// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v6.30.1
// source: pbBase/EQP/Equip.proto

package EQP

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// 物品get时的装备操作类型定义
type Type int32

const (
	ZERO        Type = 0  //自动添加
	DEF_STR     Type = 1  //劈砍防御
	DEF_AGI     Type = 2  //穿刺防御
	DEF_MAGIC   Type = 3  //魔法防御
	HIT_RATE    Type = 4  //物理命中
	ATK_MIN     Type = 5  //武伤小
	ATK_MAX     Type = 6  //武伤大
	HIT_TIME    Type = 7  //攻击次数
	ATK_TYPE    Type = 8  //攻击类型
	ITEM_TYPE   Type = 9  //物品类型
	ITEM_SET_ID Type = 10 //物品套装id
)

// Enum value maps for Type.
var (
	Type_name = map[int32]string{
		0:  "ZERO",
		1:  "DEF_STR",
		2:  "DEF_AGI",
		3:  "DEF_MAGIC",
		4:  "HIT_RATE",
		5:  "ATK_MIN",
		6:  "ATK_MAX",
		7:  "HIT_TIME",
		8:  "ATK_TYPE",
		9:  "ITEM_TYPE",
		10: "ITEM_SET_ID",
	}
	Type_value = map[string]int32{
		"ZERO":        0,
		"DEF_STR":     1,
		"DEF_AGI":     2,
		"DEF_MAGIC":   3,
		"HIT_RATE":    4,
		"ATK_MIN":     5,
		"ATK_MAX":     6,
		"HIT_TIME":    7,
		"ATK_TYPE":    8,
		"ITEM_TYPE":   9,
		"ITEM_SET_ID": 10,
	}
)

func (x Type) Enum() *Type {
	p := new(Type)
	*p = x
	return p
}

func (x Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Type) Descriptor() protoreflect.EnumDescriptor {
	return file_pbBase_EQP_Equip_proto_enumTypes[0].Descriptor()
}

func (Type) Type() protoreflect.EnumType {
	return &file_pbBase_EQP_Equip_proto_enumTypes[0]
}

func (x Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Type.Descriptor instead.
func (Type) EnumDescriptor() ([]byte, []int) {
	return file_pbBase_EQP_Equip_proto_rawDescGZIP(), []int{0}
}

var File_pbBase_EQP_Equip_proto protoreflect.FileDescriptor

var file_pbBase_EQP_Equip_proto_rawDesc = []byte{
	0x0a, 0x16, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x45, 0x51, 0x50, 0x2f, 0x45, 0x71, 0x75,
	0x69, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x45, 0x51, 0x50, 0x2a, 0x9d, 0x01, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04,
	0x5a, 0x45, 0x52, 0x4f, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x46, 0x5f, 0x53, 0x54,
	0x52, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x46, 0x5f, 0x41, 0x47, 0x49, 0x10, 0x02,
	0x12, 0x0d, 0x0a, 0x09, 0x44, 0x45, 0x46, 0x5f, 0x4d, 0x41, 0x47, 0x49, 0x43, 0x10, 0x03, 0x12,
	0x0c, 0x0a, 0x08, 0x48, 0x49, 0x54, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x10, 0x04, 0x12, 0x0b, 0x0a,
	0x07, 0x41, 0x54, 0x4b, 0x5f, 0x4d, 0x49, 0x4e, 0x10, 0x05, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x54,
	0x4b, 0x5f, 0x4d, 0x41, 0x58, 0x10, 0x06, 0x12, 0x0c, 0x0a, 0x08, 0x48, 0x49, 0x54, 0x5f, 0x54,
	0x49, 0x4d, 0x45, 0x10, 0x07, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x54, 0x4b, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x10, 0x08, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x10, 0x09, 0x12, 0x0f, 0x0a, 0x0b, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x49,
	0x44, 0x10, 0x0a, 0x42, 0x1d, 0x5a, 0x1b, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x45, 0x51, 0x50, 0x3b, 0x45,
	0x51, 0x50, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbBase_EQP_Equip_proto_rawDescOnce sync.Once
	file_pbBase_EQP_Equip_proto_rawDescData = file_pbBase_EQP_Equip_proto_rawDesc
)

func file_pbBase_EQP_Equip_proto_rawDescGZIP() []byte {
	file_pbBase_EQP_Equip_proto_rawDescOnce.Do(func() {
		file_pbBase_EQP_Equip_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbBase_EQP_Equip_proto_rawDescData)
	})
	return file_pbBase_EQP_Equip_proto_rawDescData
}

var file_pbBase_EQP_Equip_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pbBase_EQP_Equip_proto_goTypes = []interface{}{
	(Type)(0), // 0: proto.EQP.Type
}
var file_pbBase_EQP_Equip_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbBase_EQP_Equip_proto_init() }
func file_pbBase_EQP_Equip_proto_init() {
	if File_pbBase_EQP_Equip_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbBase_EQP_Equip_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbBase_EQP_Equip_proto_goTypes,
		DependencyIndexes: file_pbBase_EQP_Equip_proto_depIdxs,
		EnumInfos:         file_pbBase_EQP_Equip_proto_enumTypes,
	}.Build()
	File_pbBase_EQP_Equip_proto = out.File
	file_pbBase_EQP_Equip_proto_rawDesc = nil
	file_pbBase_EQP_Equip_proto_goTypes = nil
	file_pbBase_EQP_Equip_proto_depIdxs = nil
}
