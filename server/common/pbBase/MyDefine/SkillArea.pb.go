// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v6.30.1
// source: pbBase/MyDefine/SkillArea.proto

package MyDefine

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// 技能范围定义
type SKILL_AREA int32

const (
	SINGLE              SKILL_AREA = 0  //单体
	FRONT_BACK_TWO      SKILL_AREA = 1  //前后两人
	UP_DOWN_TWO         SKILL_AREA = 2  //上下两人
	UP_DOWN_THREE       SKILL_AREA = 3  //上下三人
	UP_DOWN_FOUR        SKILL_AREA = 4  //上下四人
	UP_DOWN_FIVE        SKILL_AREA = 5  //上下五人
	TEN                 SKILL_AREA = 6  //十字
	SQUARE              SKILL_AREA = 7  //正方
	AROUND_SIX          SKILL_AREA = 8  //六人长方
	ALL                 SKILL_AREA = 9  //全体
	ENEMY_SINGLE        SKILL_AREA = 10 //敌单体
	ENEMY_FONT_BACK_TWO SKILL_AREA = 11 //敌前后两人
	ENEMY_UP_DOWN_TWO   SKILL_AREA = 12 //敌上下两人
	ENEMY_UP_DOWN_THREE SKILL_AREA = 13 //敌上下三人
	ENEMY_UP_DOWN_FOUR  SKILL_AREA = 14 //敌上下四人
	ENEMY_UP_DOWN_FIVE  SKILL_AREA = 15 //敌上下五人
	ENEMY_TEN           SKILL_AREA = 16 //敌十字
	ENEMY_SQUARE        SKILL_AREA = 17 //敌正方
	ENEMY_AROUND_SIX    SKILL_AREA = 18 //敌六人长方
	ENEMY_ALL           SKILL_AREA = 19 //敌全体
	ME_SINGLE           SKILL_AREA = 20 //我单体
	ME_FONT_BACK_TWO    SKILL_AREA = 21 //我前后两人
	ME_UP_DOWN_TWO      SKILL_AREA = 22 //我上下两人
	ME_UP_DOWN_THREE    SKILL_AREA = 23 //我上下三人
	ME_UP_DOWN_FOUR     SKILL_AREA = 24 //我上下四人
	ME_UP_DOWN_FIVE     SKILL_AREA = 25 //我上下五人
	ME_TEN              SKILL_AREA = 26 //我十字
	ME_SQUARE           SKILL_AREA = 27 //我正方
	ME_AROUND_SIX       SKILL_AREA = 28 //我六人长方
	ME_ALL              SKILL_AREA = 29 //我全体
	MY_SELF             SKILL_AREA = 30 //自身
	ENEMY_HP_LEAST      SKILL_AREA = 31 //敌当前生命最少者
	ENEMY_HP_MOST       SKILL_AREA = 32 //敌当前生命最大者
	ME_HP_LEAST         SKILL_AREA = 33 //我当前生命最少者
	ME_HP_MOST          SKILL_AREA = 34 //我当前生命最大者
	MY_OWNER            SKILL_AREA = 35 //自身主人
	ME_ALL_NO_SELF      SKILL_AREA = 36 //我全体(不包括自身)
	ALL_NO_SELF         SKILL_AREA = 37 //全体(不包括自身)
	PLAYER_AND_PET      SKILL_AREA = 38 //宠物和主人
)

// Enum value maps for SKILL_AREA.
var (
	SKILL_AREA_name = map[int32]string{
		0:  "SINGLE",
		1:  "FRONT_BACK_TWO",
		2:  "UP_DOWN_TWO",
		3:  "UP_DOWN_THREE",
		4:  "UP_DOWN_FOUR",
		5:  "UP_DOWN_FIVE",
		6:  "TEN",
		7:  "SQUARE",
		8:  "AROUND_SIX",
		9:  "ALL",
		10: "ENEMY_SINGLE",
		11: "ENEMY_FONT_BACK_TWO",
		12: "ENEMY_UP_DOWN_TWO",
		13: "ENEMY_UP_DOWN_THREE",
		14: "ENEMY_UP_DOWN_FOUR",
		15: "ENEMY_UP_DOWN_FIVE",
		16: "ENEMY_TEN",
		17: "ENEMY_SQUARE",
		18: "ENEMY_AROUND_SIX",
		19: "ENEMY_ALL",
		20: "ME_SINGLE",
		21: "ME_FONT_BACK_TWO",
		22: "ME_UP_DOWN_TWO",
		23: "ME_UP_DOWN_THREE",
		24: "ME_UP_DOWN_FOUR",
		25: "ME_UP_DOWN_FIVE",
		26: "ME_TEN",
		27: "ME_SQUARE",
		28: "ME_AROUND_SIX",
		29: "ME_ALL",
		30: "MY_SELF",
		31: "ENEMY_HP_LEAST",
		32: "ENEMY_HP_MOST",
		33: "ME_HP_LEAST",
		34: "ME_HP_MOST",
		35: "MY_OWNER",
		36: "ME_ALL_NO_SELF",
		37: "ALL_NO_SELF",
		38: "PLAYER_AND_PET",
	}
	SKILL_AREA_value = map[string]int32{
		"SINGLE":              0,
		"FRONT_BACK_TWO":      1,
		"UP_DOWN_TWO":         2,
		"UP_DOWN_THREE":       3,
		"UP_DOWN_FOUR":        4,
		"UP_DOWN_FIVE":        5,
		"TEN":                 6,
		"SQUARE":              7,
		"AROUND_SIX":          8,
		"ALL":                 9,
		"ENEMY_SINGLE":        10,
		"ENEMY_FONT_BACK_TWO": 11,
		"ENEMY_UP_DOWN_TWO":   12,
		"ENEMY_UP_DOWN_THREE": 13,
		"ENEMY_UP_DOWN_FOUR":  14,
		"ENEMY_UP_DOWN_FIVE":  15,
		"ENEMY_TEN":           16,
		"ENEMY_SQUARE":        17,
		"ENEMY_AROUND_SIX":    18,
		"ENEMY_ALL":           19,
		"ME_SINGLE":           20,
		"ME_FONT_BACK_TWO":    21,
		"ME_UP_DOWN_TWO":      22,
		"ME_UP_DOWN_THREE":    23,
		"ME_UP_DOWN_FOUR":     24,
		"ME_UP_DOWN_FIVE":     25,
		"ME_TEN":              26,
		"ME_SQUARE":           27,
		"ME_AROUND_SIX":       28,
		"ME_ALL":              29,
		"MY_SELF":             30,
		"ENEMY_HP_LEAST":      31,
		"ENEMY_HP_MOST":       32,
		"ME_HP_LEAST":         33,
		"ME_HP_MOST":          34,
		"MY_OWNER":            35,
		"ME_ALL_NO_SELF":      36,
		"ALL_NO_SELF":         37,
		"PLAYER_AND_PET":      38,
	}
)

func (x SKILL_AREA) Enum() *SKILL_AREA {
	p := new(SKILL_AREA)
	*p = x
	return p
}

func (x SKILL_AREA) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SKILL_AREA) Descriptor() protoreflect.EnumDescriptor {
	return file_pbBase_MyDefine_SkillArea_proto_enumTypes[0].Descriptor()
}

func (SKILL_AREA) Type() protoreflect.EnumType {
	return &file_pbBase_MyDefine_SkillArea_proto_enumTypes[0]
}

func (x SKILL_AREA) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SKILL_AREA.Descriptor instead.
func (SKILL_AREA) EnumDescriptor() ([]byte, []int) {
	return file_pbBase_MyDefine_SkillArea_proto_rawDescGZIP(), []int{0}
}

// 技能范围定义

type SKILL_AREA_SEARCH int32

const (
	SEARCH_ALL      SKILL_AREA_SEARCH = 0 //搜索全体目标
	SEARCH_ENEMY    SKILL_AREA_SEARCH = 1 //搜索敌方
	SEARCH_FRIEND   SKILL_AREA_SEARCH = 2 //搜索友方
	SEARCH_MY_SELF  SKILL_AREA_SEARCH = 3 //搜索自身
	SEARCH_MY_OWNER SKILL_AREA_SEARCH = 4 //搜索自身主人
)

// Enum value maps for SKILL_AREA_SEARCH.
var (
	SKILL_AREA_SEARCH_name = map[int32]string{
		0: "SEARCH_ALL",
		1: "SEARCH_ENEMY",
		2: "SEARCH_FRIEND",
		3: "SEARCH_MY_SELF",
		4: "SEARCH_MY_OWNER",
	}
	SKILL_AREA_SEARCH_value = map[string]int32{
		"SEARCH_ALL":      0,
		"SEARCH_ENEMY":    1,
		"SEARCH_FRIEND":   2,
		"SEARCH_MY_SELF":  3,
		"SEARCH_MY_OWNER": 4,
	}
)

func (x SKILL_AREA_SEARCH) Enum() *SKILL_AREA_SEARCH {
	p := new(SKILL_AREA_SEARCH)
	*p = x
	return p
}

func (x SKILL_AREA_SEARCH) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SKILL_AREA_SEARCH) Descriptor() protoreflect.EnumDescriptor {
	return file_pbBase_MyDefine_SkillArea_proto_enumTypes[1].Descriptor()
}

func (SKILL_AREA_SEARCH) Type() protoreflect.EnumType {
	return &file_pbBase_MyDefine_SkillArea_proto_enumTypes[1]
}

func (x SKILL_AREA_SEARCH) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SKILL_AREA_SEARCH.Descriptor instead.
func (SKILL_AREA_SEARCH) EnumDescriptor() ([]byte, []int) {
	return file_pbBase_MyDefine_SkillArea_proto_rawDescGZIP(), []int{1}
}

// 技能范围定义

type SKILL_AREA_CURSOR int32

const (
	CURSOR_NONE SKILL_AREA_CURSOR = 0  //
	CURSOR_ALL  SKILL_AREA_CURSOR = -1 //
	CURSOR_1    SKILL_AREA_CURSOR = 1  //
	CURSOR_2    SKILL_AREA_CURSOR = 2  //
	CURSOR_3    SKILL_AREA_CURSOR = 3  //
	CURSOR_4    SKILL_AREA_CURSOR = 4  //
	CURSOR_5    SKILL_AREA_CURSOR = 5  //
	CURSOR_6    SKILL_AREA_CURSOR = 6  //
)

// Enum value maps for SKILL_AREA_CURSOR.
var (
	SKILL_AREA_CURSOR_name = map[int32]string{
		0:  "CURSOR_NONE",
		-1: "CURSOR_ALL",
		1:  "CURSOR_1",
		2:  "CURSOR_2",
		3:  "CURSOR_3",
		4:  "CURSOR_4",
		5:  "CURSOR_5",
		6:  "CURSOR_6",
	}
	SKILL_AREA_CURSOR_value = map[string]int32{
		"CURSOR_NONE": 0,
		"CURSOR_ALL":  -1,
		"CURSOR_1":    1,
		"CURSOR_2":    2,
		"CURSOR_3":    3,
		"CURSOR_4":    4,
		"CURSOR_5":    5,
		"CURSOR_6":    6,
	}
)

func (x SKILL_AREA_CURSOR) Enum() *SKILL_AREA_CURSOR {
	p := new(SKILL_AREA_CURSOR)
	*p = x
	return p
}

func (x SKILL_AREA_CURSOR) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SKILL_AREA_CURSOR) Descriptor() protoreflect.EnumDescriptor {
	return file_pbBase_MyDefine_SkillArea_proto_enumTypes[2].Descriptor()
}

func (SKILL_AREA_CURSOR) Type() protoreflect.EnumType {
	return &file_pbBase_MyDefine_SkillArea_proto_enumTypes[2]
}

func (x SKILL_AREA_CURSOR) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SKILL_AREA_CURSOR.Descriptor instead.
func (SKILL_AREA_CURSOR) EnumDescriptor() ([]byte, []int) {
	return file_pbBase_MyDefine_SkillArea_proto_rawDescGZIP(), []int{2}
}

var File_pbBase_MyDefine_SkillArea_proto protoreflect.FileDescriptor

var file_pbBase_MyDefine_SkillArea_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x4d, 0x79, 0x44, 0x65, 0x66, 0x69, 0x6e,
	0x65, 0x2f, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x41, 0x72, 0x65, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x79, 0x44, 0x65, 0x66, 0x69, 0x6e,
	0x65, 0x2a, 0xc2, 0x05, 0x0a, 0x0a, 0x53, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x41, 0x52, 0x45, 0x41,
	0x12, 0x0a, 0x0a, 0x06, 0x53, 0x49, 0x4e, 0x47, 0x4c, 0x45, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e,
	0x46, 0x52, 0x4f, 0x4e, 0x54, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x54, 0x57, 0x4f, 0x10, 0x01,
	0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x50, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x5f, 0x54, 0x57, 0x4f, 0x10,
	0x02, 0x12, 0x11, 0x0a, 0x0d, 0x55, 0x50, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x5f, 0x54, 0x48, 0x52,
	0x45, 0x45, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x55, 0x50, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x5f,
	0x46, 0x4f, 0x55, 0x52, 0x10, 0x04, 0x12, 0x10, 0x0a, 0x0c, 0x55, 0x50, 0x5f, 0x44, 0x4f, 0x57,
	0x4e, 0x5f, 0x46, 0x49, 0x56, 0x45, 0x10, 0x05, 0x12, 0x07, 0x0a, 0x03, 0x54, 0x45, 0x4e, 0x10,
	0x06, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x51, 0x55, 0x41, 0x52, 0x45, 0x10, 0x07, 0x12, 0x0e, 0x0a,
	0x0a, 0x41, 0x52, 0x4f, 0x55, 0x4e, 0x44, 0x5f, 0x53, 0x49, 0x58, 0x10, 0x08, 0x12, 0x07, 0x0a,
	0x03, 0x41, 0x4c, 0x4c, 0x10, 0x09, 0x12, 0x10, 0x0a, 0x0c, 0x45, 0x4e, 0x45, 0x4d, 0x59, 0x5f,
	0x53, 0x49, 0x4e, 0x47, 0x4c, 0x45, 0x10, 0x0a, 0x12, 0x17, 0x0a, 0x13, 0x45, 0x4e, 0x45, 0x4d,
	0x59, 0x5f, 0x46, 0x4f, 0x4e, 0x54, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x54, 0x57, 0x4f, 0x10,
	0x0b, 0x12, 0x15, 0x0a, 0x11, 0x45, 0x4e, 0x45, 0x4d, 0x59, 0x5f, 0x55, 0x50, 0x5f, 0x44, 0x4f,
	0x57, 0x4e, 0x5f, 0x54, 0x57, 0x4f, 0x10, 0x0c, 0x12, 0x17, 0x0a, 0x13, 0x45, 0x4e, 0x45, 0x4d,
	0x59, 0x5f, 0x55, 0x50, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x5f, 0x54, 0x48, 0x52, 0x45, 0x45, 0x10,
	0x0d, 0x12, 0x16, 0x0a, 0x12, 0x45, 0x4e, 0x45, 0x4d, 0x59, 0x5f, 0x55, 0x50, 0x5f, 0x44, 0x4f,
	0x57, 0x4e, 0x5f, 0x46, 0x4f, 0x55, 0x52, 0x10, 0x0e, 0x12, 0x16, 0x0a, 0x12, 0x45, 0x4e, 0x45,
	0x4d, 0x59, 0x5f, 0x55, 0x50, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x5f, 0x46, 0x49, 0x56, 0x45, 0x10,
	0x0f, 0x12, 0x0d, 0x0a, 0x09, 0x45, 0x4e, 0x45, 0x4d, 0x59, 0x5f, 0x54, 0x45, 0x4e, 0x10, 0x10,
	0x12, 0x10, 0x0a, 0x0c, 0x45, 0x4e, 0x45, 0x4d, 0x59, 0x5f, 0x53, 0x51, 0x55, 0x41, 0x52, 0x45,
	0x10, 0x11, 0x12, 0x14, 0x0a, 0x10, 0x45, 0x4e, 0x45, 0x4d, 0x59, 0x5f, 0x41, 0x52, 0x4f, 0x55,
	0x4e, 0x44, 0x5f, 0x53, 0x49, 0x58, 0x10, 0x12, 0x12, 0x0d, 0x0a, 0x09, 0x45, 0x4e, 0x45, 0x4d,
	0x59, 0x5f, 0x41, 0x4c, 0x4c, 0x10, 0x13, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x45, 0x5f, 0x53, 0x49,
	0x4e, 0x47, 0x4c, 0x45, 0x10, 0x14, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x45, 0x5f, 0x46, 0x4f, 0x4e,
	0x54, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x54, 0x57, 0x4f, 0x10, 0x15, 0x12, 0x12, 0x0a, 0x0e,
	0x4d, 0x45, 0x5f, 0x55, 0x50, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x5f, 0x54, 0x57, 0x4f, 0x10, 0x16,
	0x12, 0x14, 0x0a, 0x10, 0x4d, 0x45, 0x5f, 0x55, 0x50, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x5f, 0x54,
	0x48, 0x52, 0x45, 0x45, 0x10, 0x17, 0x12, 0x13, 0x0a, 0x0f, 0x4d, 0x45, 0x5f, 0x55, 0x50, 0x5f,
	0x44, 0x4f, 0x57, 0x4e, 0x5f, 0x46, 0x4f, 0x55, 0x52, 0x10, 0x18, 0x12, 0x13, 0x0a, 0x0f, 0x4d,
	0x45, 0x5f, 0x55, 0x50, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x5f, 0x46, 0x49, 0x56, 0x45, 0x10, 0x19,
	0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x45, 0x5f, 0x54, 0x45, 0x4e, 0x10, 0x1a, 0x12, 0x0d, 0x0a, 0x09,
	0x4d, 0x45, 0x5f, 0x53, 0x51, 0x55, 0x41, 0x52, 0x45, 0x10, 0x1b, 0x12, 0x11, 0x0a, 0x0d, 0x4d,
	0x45, 0x5f, 0x41, 0x52, 0x4f, 0x55, 0x4e, 0x44, 0x5f, 0x53, 0x49, 0x58, 0x10, 0x1c, 0x12, 0x0a,
	0x0a, 0x06, 0x4d, 0x45, 0x5f, 0x41, 0x4c, 0x4c, 0x10, 0x1d, 0x12, 0x0b, 0x0a, 0x07, 0x4d, 0x59,
	0x5f, 0x53, 0x45, 0x4c, 0x46, 0x10, 0x1e, 0x12, 0x12, 0x0a, 0x0e, 0x45, 0x4e, 0x45, 0x4d, 0x59,
	0x5f, 0x48, 0x50, 0x5f, 0x4c, 0x45, 0x41, 0x53, 0x54, 0x10, 0x1f, 0x12, 0x11, 0x0a, 0x0d, 0x45,
	0x4e, 0x45, 0x4d, 0x59, 0x5f, 0x48, 0x50, 0x5f, 0x4d, 0x4f, 0x53, 0x54, 0x10, 0x20, 0x12, 0x0f,
	0x0a, 0x0b, 0x4d, 0x45, 0x5f, 0x48, 0x50, 0x5f, 0x4c, 0x45, 0x41, 0x53, 0x54, 0x10, 0x21, 0x12,
	0x0e, 0x0a, 0x0a, 0x4d, 0x45, 0x5f, 0x48, 0x50, 0x5f, 0x4d, 0x4f, 0x53, 0x54, 0x10, 0x22, 0x12,
	0x0c, 0x0a, 0x08, 0x4d, 0x59, 0x5f, 0x4f, 0x57, 0x4e, 0x45, 0x52, 0x10, 0x23, 0x12, 0x12, 0x0a,
	0x0e, 0x4d, 0x45, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x4e, 0x4f, 0x5f, 0x53, 0x45, 0x4c, 0x46, 0x10,
	0x24, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x4c, 0x4c, 0x5f, 0x4e, 0x4f, 0x5f, 0x53, 0x45, 0x4c, 0x46,
	0x10, 0x25, 0x12, 0x12, 0x0a, 0x0e, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x41, 0x4e, 0x44,
	0x5f, 0x50, 0x45, 0x54, 0x10, 0x26, 0x2a, 0x71, 0x0a, 0x11, 0x53, 0x4b, 0x49, 0x4c, 0x4c, 0x5f,
	0x41, 0x52, 0x45, 0x41, 0x5f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x12, 0x0e, 0x0a, 0x0a, 0x53,
	0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x41, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x53,
	0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x45, 0x4e, 0x45, 0x4d, 0x59, 0x10, 0x01, 0x12, 0x11, 0x0a,
	0x0d, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x46, 0x52, 0x49, 0x45, 0x4e, 0x44, 0x10, 0x02,
	0x12, 0x12, 0x0a, 0x0e, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4d, 0x59, 0x5f, 0x53, 0x45,
	0x4c, 0x46, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x4d,
	0x59, 0x5f, 0x4f, 0x57, 0x4e, 0x45, 0x52, 0x10, 0x04, 0x2a, 0x91, 0x01, 0x0a, 0x11, 0x53, 0x4b,
	0x49, 0x4c, 0x4c, 0x5f, 0x41, 0x52, 0x45, 0x41, 0x5f, 0x43, 0x55, 0x52, 0x53, 0x4f, 0x52, 0x12,
	0x0f, 0x0a, 0x0b, 0x43, 0x55, 0x52, 0x53, 0x4f, 0x52, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00,
	0x12, 0x17, 0x0a, 0x0a, 0x43, 0x55, 0x52, 0x53, 0x4f, 0x52, 0x5f, 0x41, 0x4c, 0x4c, 0x10, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x55, 0x52,
	0x53, 0x4f, 0x52, 0x5f, 0x31, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x55, 0x52, 0x53, 0x4f,
	0x52, 0x5f, 0x32, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x55, 0x52, 0x53, 0x4f, 0x52, 0x5f,
	0x33, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x55, 0x52, 0x53, 0x4f, 0x52, 0x5f, 0x34, 0x10,
	0x04, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x55, 0x52, 0x53, 0x4f, 0x52, 0x5f, 0x35, 0x10, 0x05, 0x12,
	0x0c, 0x0a, 0x08, 0x43, 0x55, 0x52, 0x53, 0x4f, 0x52, 0x5f, 0x36, 0x10, 0x06, 0x42, 0x27, 0x5a,
	0x25, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62,
	0x42, 0x61, 0x73, 0x65, 0x2f, 0x4d, 0x79, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x3b, 0x4d, 0x79,
	0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbBase_MyDefine_SkillArea_proto_rawDescOnce sync.Once
	file_pbBase_MyDefine_SkillArea_proto_rawDescData = file_pbBase_MyDefine_SkillArea_proto_rawDesc
)

func file_pbBase_MyDefine_SkillArea_proto_rawDescGZIP() []byte {
	file_pbBase_MyDefine_SkillArea_proto_rawDescOnce.Do(func() {
		file_pbBase_MyDefine_SkillArea_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbBase_MyDefine_SkillArea_proto_rawDescData)
	})
	return file_pbBase_MyDefine_SkillArea_proto_rawDescData
}

var file_pbBase_MyDefine_SkillArea_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_pbBase_MyDefine_SkillArea_proto_goTypes = []interface{}{
	(SKILL_AREA)(0),        // 0: proto.MyDefine.SKILL_AREA
	(SKILL_AREA_SEARCH)(0), // 1: proto.MyDefine.SKILL_AREA_SEARCH
	(SKILL_AREA_CURSOR)(0), // 2: proto.MyDefine.SKILL_AREA_CURSOR
}
var file_pbBase_MyDefine_SkillArea_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbBase_MyDefine_SkillArea_proto_init() }
func file_pbBase_MyDefine_SkillArea_proto_init() {
	if File_pbBase_MyDefine_SkillArea_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbBase_MyDefine_SkillArea_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbBase_MyDefine_SkillArea_proto_goTypes,
		DependencyIndexes: file_pbBase_MyDefine_SkillArea_proto_depIdxs,
		EnumInfos:         file_pbBase_MyDefine_SkillArea_proto_enumTypes,
	}.Build()
	File_pbBase_MyDefine_SkillArea_proto = out.File
	file_pbBase_MyDefine_SkillArea_proto_rawDesc = nil
	file_pbBase_MyDefine_SkillArea_proto_goTypes = nil
	file_pbBase_MyDefine_SkillArea_proto_depIdxs = nil
}
