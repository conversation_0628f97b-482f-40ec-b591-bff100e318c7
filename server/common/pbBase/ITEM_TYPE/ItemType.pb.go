// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v6.30.1
// source: pbBase/ITEM_TYPE/ItemType.proto

package ITEM_TYPE

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// 物品type定义 类型
type Type int32

const (
	ARMOR_HEAD              Type = 0  //防具-头盔
	ARMOR_CLOTHES           Type = 1  //防具-衣服
	ARMOR_TROUSERS          Type = 2  //防具-裤子
	ARMOR_SHOULDER          Type = 3  //防具-肩部
	ARMOR_WAIST             Type = 4  //防具-腰部
	ARMOR_BACK              Type = 5  //防具-背部
	ARMOR_SHOES             Type = 6  //防具-鞋子
	ARMOR_HAND              Type = 7  //防具-手套
	ARMOR_NECKLACE          Type = 8  //防具-项链
	ARMOR_RING              Type = 9  //防具-戒指
	ARMOR_AMULET            Type = 10 //防具-护符
	ARMOR_TRANSPORT         Type = 11 //防具-坐骑
	ARMOR_FASHION           Type = 12 //防具-时装
	WEAPON_ONEHAND_SWORD    Type = 13 //武器-单手剑
	WEAPON_TWOHAND_SWORD    Type = 14 //武器-重剑
	WEAPON_ONEHAND_BLADE    Type = 15 //武器-单手刀
	WEAPON_TWOHAND_BLADE    Type = 16 //武器-重刀
	WEAPON_ONEHAND_HEAVY    Type = 17 //武器-单手重型
	WEAPON_TWOHAND_HEAVY    Type = 18 //武器-双持重型
	WEAPON_TWOHAND_STAFF    Type = 19 //武器-法杖
	WEAPON_TWOHAND_LANCE    Type = 20 //武器-长柄
	WEAPON_ONEHAND_CROSSBOW Type = 21 //武器-单手弩
	WEAPON_TWOHAND_CROSSBOW Type = 22 //武器-重弩
	WEAPON_TWOHAND_BOW      Type = 23 //武器-弓箭
	WEAPON_ONEHAND_HAND     Type = 24 //武器-副手
	TASK                    Type = 25 //任务物品
	BATTLE_USE              Type = 26 //战斗中使用药品
	ANYTIME_USE             Type = 27 //任何时间使用药品
	NOT_BATTLE_USE          Type = 28 //战斗中不可使用药品
	BUILD_MATERIAL          Type = 29 //建筑材料
	GEM                     Type = 30 //宝石
	SKILL_BOOK              Type = 31 //技能书
	PET                     Type = 32 //宠物
	SPECIAL                 Type = 33 //特殊物品
	WEAPON_BALL             Type = 34 //法器
	WEAPON_ONEHAND_GUN      Type = 35 //武器-轻枪
	WEAPON_TWOHAND_GUN      Type = 36 //武器-重枪
	WEAPON_ONEHAND_HAMMER   Type = 37 //武器-轻锤
	WEAPON_TWOHAND_HAMMER   Type = 38 //武器-重锤
	WEAPON_TWOHAND_FAN      Type = 39 //武器-扇
	BLOOD_BOTTLE            Type = 40 //能量精华
	PET_EQUIP               Type = 41 //宠物装备
	PET_EQUIP_EXP_BOOK      Type = 42 //宠物装备经验
	SEAL                    Type = 43 //赋灵符
	ENERGY_ESSENCE          Type = 44 //赋灵符
	BOX_CHOOSE_ONE          Type = 45 //自选盒子
	ADD_PET_LIFE            Type = 46 //增加宠物寿命
	LN_STONE                Type = 47 //
)

// Enum value maps for Type.
var (
	Type_name = map[int32]string{
		0:  "ARMOR_HEAD",
		1:  "ARMOR_CLOTHES",
		2:  "ARMOR_TROUSERS",
		3:  "ARMOR_SHOULDER",
		4:  "ARMOR_WAIST",
		5:  "ARMOR_BACK",
		6:  "ARMOR_SHOES",
		7:  "ARMOR_HAND",
		8:  "ARMOR_NECKLACE",
		9:  "ARMOR_RING",
		10: "ARMOR_AMULET",
		11: "ARMOR_TRANSPORT",
		12: "ARMOR_FASHION",
		13: "WEAPON_ONEHAND_SWORD",
		14: "WEAPON_TWOHAND_SWORD",
		15: "WEAPON_ONEHAND_BLADE",
		16: "WEAPON_TWOHAND_BLADE",
		17: "WEAPON_ONEHAND_HEAVY",
		18: "WEAPON_TWOHAND_HEAVY",
		19: "WEAPON_TWOHAND_STAFF",
		20: "WEAPON_TWOHAND_LANCE",
		21: "WEAPON_ONEHAND_CROSSBOW",
		22: "WEAPON_TWOHAND_CROSSBOW",
		23: "WEAPON_TWOHAND_BOW",
		24: "WEAPON_ONEHAND_HAND",
		25: "TASK",
		26: "BATTLE_USE",
		27: "ANYTIME_USE",
		28: "NOT_BATTLE_USE",
		29: "BUILD_MATERIAL",
		30: "GEM",
		31: "SKILL_BOOK",
		32: "PET",
		33: "SPECIAL",
		34: "WEAPON_BALL",
		35: "WEAPON_ONEHAND_GUN",
		36: "WEAPON_TWOHAND_GUN",
		37: "WEAPON_ONEHAND_HAMMER",
		38: "WEAPON_TWOHAND_HAMMER",
		39: "WEAPON_TWOHAND_FAN",
		40: "BLOOD_BOTTLE",
		41: "PET_EQUIP",
		42: "PET_EQUIP_EXP_BOOK",
		43: "SEAL",
		44: "ENERGY_ESSENCE",
		45: "BOX_CHOOSE_ONE",
		46: "ADD_PET_LIFE",
		47: "LN_STONE",
	}
	Type_value = map[string]int32{
		"ARMOR_HEAD":              0,
		"ARMOR_CLOTHES":           1,
		"ARMOR_TROUSERS":          2,
		"ARMOR_SHOULDER":          3,
		"ARMOR_WAIST":             4,
		"ARMOR_BACK":              5,
		"ARMOR_SHOES":             6,
		"ARMOR_HAND":              7,
		"ARMOR_NECKLACE":          8,
		"ARMOR_RING":              9,
		"ARMOR_AMULET":            10,
		"ARMOR_TRANSPORT":         11,
		"ARMOR_FASHION":           12,
		"WEAPON_ONEHAND_SWORD":    13,
		"WEAPON_TWOHAND_SWORD":    14,
		"WEAPON_ONEHAND_BLADE":    15,
		"WEAPON_TWOHAND_BLADE":    16,
		"WEAPON_ONEHAND_HEAVY":    17,
		"WEAPON_TWOHAND_HEAVY":    18,
		"WEAPON_TWOHAND_STAFF":    19,
		"WEAPON_TWOHAND_LANCE":    20,
		"WEAPON_ONEHAND_CROSSBOW": 21,
		"WEAPON_TWOHAND_CROSSBOW": 22,
		"WEAPON_TWOHAND_BOW":      23,
		"WEAPON_ONEHAND_HAND":     24,
		"TASK":                    25,
		"BATTLE_USE":              26,
		"ANYTIME_USE":             27,
		"NOT_BATTLE_USE":          28,
		"BUILD_MATERIAL":          29,
		"GEM":                     30,
		"SKILL_BOOK":              31,
		"PET":                     32,
		"SPECIAL":                 33,
		"WEAPON_BALL":             34,
		"WEAPON_ONEHAND_GUN":      35,
		"WEAPON_TWOHAND_GUN":      36,
		"WEAPON_ONEHAND_HAMMER":   37,
		"WEAPON_TWOHAND_HAMMER":   38,
		"WEAPON_TWOHAND_FAN":      39,
		"BLOOD_BOTTLE":            40,
		"PET_EQUIP":               41,
		"PET_EQUIP_EXP_BOOK":      42,
		"SEAL":                    43,
		"ENERGY_ESSENCE":          44,
		"BOX_CHOOSE_ONE":          45,
		"ADD_PET_LIFE":            46,
		"LN_STONE":                47,
	}
)

func (x Type) Enum() *Type {
	p := new(Type)
	*p = x
	return p
}

func (x Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Type) Descriptor() protoreflect.EnumDescriptor {
	return file_pbBase_ITEM_TYPE_ItemType_proto_enumTypes[0].Descriptor()
}

func (Type) Type() protoreflect.EnumType {
	return &file_pbBase_ITEM_TYPE_ItemType_proto_enumTypes[0]
}

func (x Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Type.Descriptor instead.
func (Type) EnumDescriptor() ([]byte, []int) {
	return file_pbBase_ITEM_TYPE_ItemType_proto_rawDescGZIP(), []int{0}
}

var File_pbBase_ITEM_TYPE_ItemType_proto protoreflect.FileDescriptor

var file_pbBase_ITEM_TYPE_ItemType_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x2f, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x2a, 0xc8, 0x07, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x41,
	0x52, 0x4d, 0x4f, 0x52, 0x5f, 0x48, 0x45, 0x41, 0x44, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x41,
	0x52, 0x4d, 0x4f, 0x52, 0x5f, 0x43, 0x4c, 0x4f, 0x54, 0x48, 0x45, 0x53, 0x10, 0x01, 0x12, 0x12,
	0x0a, 0x0e, 0x41, 0x52, 0x4d, 0x4f, 0x52, 0x5f, 0x54, 0x52, 0x4f, 0x55, 0x53, 0x45, 0x52, 0x53,
	0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x52, 0x4d, 0x4f, 0x52, 0x5f, 0x53, 0x48, 0x4f, 0x55,
	0x4c, 0x44, 0x45, 0x52, 0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x52, 0x4d, 0x4f, 0x52, 0x5f,
	0x57, 0x41, 0x49, 0x53, 0x54, 0x10, 0x04, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x52, 0x4d, 0x4f, 0x52,
	0x5f, 0x42, 0x41, 0x43, 0x4b, 0x10, 0x05, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x52, 0x4d, 0x4f, 0x52,
	0x5f, 0x53, 0x48, 0x4f, 0x45, 0x53, 0x10, 0x06, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x52, 0x4d, 0x4f,
	0x52, 0x5f, 0x48, 0x41, 0x4e, 0x44, 0x10, 0x07, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x52, 0x4d, 0x4f,
	0x52, 0x5f, 0x4e, 0x45, 0x43, 0x4b, 0x4c, 0x41, 0x43, 0x45, 0x10, 0x08, 0x12, 0x0e, 0x0a, 0x0a,
	0x41, 0x52, 0x4d, 0x4f, 0x52, 0x5f, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x09, 0x12, 0x10, 0x0a, 0x0c,
	0x41, 0x52, 0x4d, 0x4f, 0x52, 0x5f, 0x41, 0x4d, 0x55, 0x4c, 0x45, 0x54, 0x10, 0x0a, 0x12, 0x13,
	0x0a, 0x0f, 0x41, 0x52, 0x4d, 0x4f, 0x52, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x50, 0x4f, 0x52,
	0x54, 0x10, 0x0b, 0x12, 0x11, 0x0a, 0x0d, 0x41, 0x52, 0x4d, 0x4f, 0x52, 0x5f, 0x46, 0x41, 0x53,
	0x48, 0x49, 0x4f, 0x4e, 0x10, 0x0c, 0x12, 0x18, 0x0a, 0x14, 0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e,
	0x5f, 0x4f, 0x4e, 0x45, 0x48, 0x41, 0x4e, 0x44, 0x5f, 0x53, 0x57, 0x4f, 0x52, 0x44, 0x10, 0x0d,
	0x12, 0x18, 0x0a, 0x14, 0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x54, 0x57, 0x4f, 0x48, 0x41,
	0x4e, 0x44, 0x5f, 0x53, 0x57, 0x4f, 0x52, 0x44, 0x10, 0x0e, 0x12, 0x18, 0x0a, 0x14, 0x57, 0x45,
	0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x4f, 0x4e, 0x45, 0x48, 0x41, 0x4e, 0x44, 0x5f, 0x42, 0x4c, 0x41,
	0x44, 0x45, 0x10, 0x0f, 0x12, 0x18, 0x0a, 0x14, 0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x54,
	0x57, 0x4f, 0x48, 0x41, 0x4e, 0x44, 0x5f, 0x42, 0x4c, 0x41, 0x44, 0x45, 0x10, 0x10, 0x12, 0x18,
	0x0a, 0x14, 0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x4f, 0x4e, 0x45, 0x48, 0x41, 0x4e, 0x44,
	0x5f, 0x48, 0x45, 0x41, 0x56, 0x59, 0x10, 0x11, 0x12, 0x18, 0x0a, 0x14, 0x57, 0x45, 0x41, 0x50,
	0x4f, 0x4e, 0x5f, 0x54, 0x57, 0x4f, 0x48, 0x41, 0x4e, 0x44, 0x5f, 0x48, 0x45, 0x41, 0x56, 0x59,
	0x10, 0x12, 0x12, 0x18, 0x0a, 0x14, 0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x54, 0x57, 0x4f,
	0x48, 0x41, 0x4e, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x46, 0x46, 0x10, 0x13, 0x12, 0x18, 0x0a, 0x14,
	0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x54, 0x57, 0x4f, 0x48, 0x41, 0x4e, 0x44, 0x5f, 0x4c,
	0x41, 0x4e, 0x43, 0x45, 0x10, 0x14, 0x12, 0x1b, 0x0a, 0x17, 0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e,
	0x5f, 0x4f, 0x4e, 0x45, 0x48, 0x41, 0x4e, 0x44, 0x5f, 0x43, 0x52, 0x4f, 0x53, 0x53, 0x42, 0x4f,
	0x57, 0x10, 0x15, 0x12, 0x1b, 0x0a, 0x17, 0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x54, 0x57,
	0x4f, 0x48, 0x41, 0x4e, 0x44, 0x5f, 0x43, 0x52, 0x4f, 0x53, 0x53, 0x42, 0x4f, 0x57, 0x10, 0x16,
	0x12, 0x16, 0x0a, 0x12, 0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x54, 0x57, 0x4f, 0x48, 0x41,
	0x4e, 0x44, 0x5f, 0x42, 0x4f, 0x57, 0x10, 0x17, 0x12, 0x17, 0x0a, 0x13, 0x57, 0x45, 0x41, 0x50,
	0x4f, 0x4e, 0x5f, 0x4f, 0x4e, 0x45, 0x48, 0x41, 0x4e, 0x44, 0x5f, 0x48, 0x41, 0x4e, 0x44, 0x10,
	0x18, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x41, 0x53, 0x4b, 0x10, 0x19, 0x12, 0x0e, 0x0a, 0x0a, 0x42,
	0x41, 0x54, 0x54, 0x4c, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x10, 0x1a, 0x12, 0x0f, 0x0a, 0x0b, 0x41,
	0x4e, 0x59, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x10, 0x1b, 0x12, 0x12, 0x0a, 0x0e,
	0x4e, 0x4f, 0x54, 0x5f, 0x42, 0x41, 0x54, 0x54, 0x4c, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x10, 0x1c,
	0x12, 0x12, 0x0a, 0x0e, 0x42, 0x55, 0x49, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x54, 0x45, 0x52, 0x49,
	0x41, 0x4c, 0x10, 0x1d, 0x12, 0x07, 0x0a, 0x03, 0x47, 0x45, 0x4d, 0x10, 0x1e, 0x12, 0x0e, 0x0a,
	0x0a, 0x53, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x10, 0x1f, 0x12, 0x07, 0x0a,
	0x03, 0x50, 0x45, 0x54, 0x10, 0x20, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41,
	0x4c, 0x10, 0x21, 0x12, 0x0f, 0x0a, 0x0b, 0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x42, 0x41,
	0x4c, 0x4c, 0x10, 0x22, 0x12, 0x16, 0x0a, 0x12, 0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x4f,
	0x4e, 0x45, 0x48, 0x41, 0x4e, 0x44, 0x5f, 0x47, 0x55, 0x4e, 0x10, 0x23, 0x12, 0x16, 0x0a, 0x12,
	0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x54, 0x57, 0x4f, 0x48, 0x41, 0x4e, 0x44, 0x5f, 0x47,
	0x55, 0x4e, 0x10, 0x24, 0x12, 0x19, 0x0a, 0x15, 0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x4f,
	0x4e, 0x45, 0x48, 0x41, 0x4e, 0x44, 0x5f, 0x48, 0x41, 0x4d, 0x4d, 0x45, 0x52, 0x10, 0x25, 0x12,
	0x19, 0x0a, 0x15, 0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x54, 0x57, 0x4f, 0x48, 0x41, 0x4e,
	0x44, 0x5f, 0x48, 0x41, 0x4d, 0x4d, 0x45, 0x52, 0x10, 0x26, 0x12, 0x16, 0x0a, 0x12, 0x57, 0x45,
	0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x54, 0x57, 0x4f, 0x48, 0x41, 0x4e, 0x44, 0x5f, 0x46, 0x41, 0x4e,
	0x10, 0x27, 0x12, 0x10, 0x0a, 0x0c, 0x42, 0x4c, 0x4f, 0x4f, 0x44, 0x5f, 0x42, 0x4f, 0x54, 0x54,
	0x4c, 0x45, 0x10, 0x28, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x45, 0x54, 0x5f, 0x45, 0x51, 0x55, 0x49,
	0x50, 0x10, 0x29, 0x12, 0x16, 0x0a, 0x12, 0x50, 0x45, 0x54, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x50,
	0x5f, 0x45, 0x58, 0x50, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x10, 0x2a, 0x12, 0x08, 0x0a, 0x04, 0x53,
	0x45, 0x41, 0x4c, 0x10, 0x2b, 0x12, 0x12, 0x0a, 0x0e, 0x45, 0x4e, 0x45, 0x52, 0x47, 0x59, 0x5f,
	0x45, 0x53, 0x53, 0x45, 0x4e, 0x43, 0x45, 0x10, 0x2c, 0x12, 0x12, 0x0a, 0x0e, 0x42, 0x4f, 0x58,
	0x5f, 0x43, 0x48, 0x4f, 0x4f, 0x53, 0x45, 0x5f, 0x4f, 0x4e, 0x45, 0x10, 0x2d, 0x12, 0x10, 0x0a,
	0x0c, 0x41, 0x44, 0x44, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x4c, 0x49, 0x46, 0x45, 0x10, 0x2e, 0x12,
	0x0c, 0x0a, 0x08, 0x4c, 0x4e, 0x5f, 0x53, 0x54, 0x4f, 0x4e, 0x45, 0x10, 0x2f, 0x42, 0x29, 0x5a,
	0x27, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62,
	0x42, 0x61, 0x73, 0x65, 0x2f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x3b, 0x49,
	0x54, 0x45, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbBase_ITEM_TYPE_ItemType_proto_rawDescOnce sync.Once
	file_pbBase_ITEM_TYPE_ItemType_proto_rawDescData = file_pbBase_ITEM_TYPE_ItemType_proto_rawDesc
)

func file_pbBase_ITEM_TYPE_ItemType_proto_rawDescGZIP() []byte {
	file_pbBase_ITEM_TYPE_ItemType_proto_rawDescOnce.Do(func() {
		file_pbBase_ITEM_TYPE_ItemType_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbBase_ITEM_TYPE_ItemType_proto_rawDescData)
	})
	return file_pbBase_ITEM_TYPE_ItemType_proto_rawDescData
}

var file_pbBase_ITEM_TYPE_ItemType_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pbBase_ITEM_TYPE_ItemType_proto_goTypes = []interface{}{
	(Type)(0), // 0: proto.ITEM_TYPE.Type
}
var file_pbBase_ITEM_TYPE_ItemType_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbBase_ITEM_TYPE_ItemType_proto_init() }
func file_pbBase_ITEM_TYPE_ItemType_proto_init() {
	if File_pbBase_ITEM_TYPE_ItemType_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbBase_ITEM_TYPE_ItemType_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbBase_ITEM_TYPE_ItemType_proto_goTypes,
		DependencyIndexes: file_pbBase_ITEM_TYPE_ItemType_proto_depIdxs,
		EnumInfos:         file_pbBase_ITEM_TYPE_ItemType_proto_enumTypes,
	}.Build()
	File_pbBase_ITEM_TYPE_ItemType_proto = out.File
	file_pbBase_ITEM_TYPE_ItemType_proto_rawDesc = nil
	file_pbBase_ITEM_TYPE_ItemType_proto_goTypes = nil
	file_pbBase_ITEM_TYPE_ItemType_proto_depIdxs = nil
}
