// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v6.30.1
// source: pbBase/event.proto

package pbBase

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// 主要事件,从10000开始
type MainEvent int32

const (
	MainEventNone            MainEvent = 0     //自动添加
	ON_UPDATE_PLAYER_INFO_UI MainEvent = 10000 //玩家头像ui更新
	ON_PLAYER_ATTR_UPDATE    MainEvent = 10001 //玩家属性更新
)

// Enum value maps for MainEvent.
var (
	MainEvent_name = map[int32]string{
		0:     "MainEventNone",
		10000: "ON_UPDATE_PLAYER_INFO_UI",
		10001: "ON_PLAYER_ATTR_UPDATE",
	}
	MainEvent_value = map[string]int32{
		"MainEventNone":            0,
		"ON_UPDATE_PLAYER_INFO_UI": 10000,
		"ON_PLAYER_ATTR_UPDATE":    10001,
	}
)

func (x MainEvent) Enum() *MainEvent {
	p := new(MainEvent)
	*p = x
	return p
}

func (x MainEvent) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MainEvent) Descriptor() protoreflect.EnumDescriptor {
	return file_pbBase_event_proto_enumTypes[0].Descriptor()
}

func (MainEvent) Type() protoreflect.EnumType {
	return &file_pbBase_event_proto_enumTypes[0]
}

func (x MainEvent) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MainEvent.Descriptor instead.
func (MainEvent) EnumDescriptor() ([]byte, []int) {
	return file_pbBase_event_proto_rawDescGZIP(), []int{0}
}

var File_pbBase_event_proto protoreflect.FileDescriptor

var file_pbBase_event_proto_rawDesc = []byte{
	0x0a, 0x12, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2a, 0x59, 0x0a, 0x09, 0x4d,
	0x61, 0x69, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x11, 0x0a, 0x0d, 0x4d, 0x61, 0x69, 0x6e,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x18, 0x4f,
	0x4e, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f,
	0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x55, 0x49, 0x10, 0x90, 0x4e, 0x12, 0x1a, 0x0a, 0x15, 0x4f, 0x4e,
	0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x41, 0x54, 0x54, 0x52, 0x5f, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x10, 0x91, 0x4e, 0x42, 0x1c, 0x5a, 0x1a, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x3b, 0x70, 0x62,
	0x42, 0x61, 0x73, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbBase_event_proto_rawDescOnce sync.Once
	file_pbBase_event_proto_rawDescData = file_pbBase_event_proto_rawDesc
)

func file_pbBase_event_proto_rawDescGZIP() []byte {
	file_pbBase_event_proto_rawDescOnce.Do(func() {
		file_pbBase_event_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbBase_event_proto_rawDescData)
	})
	return file_pbBase_event_proto_rawDescData
}

var file_pbBase_event_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pbBase_event_proto_goTypes = []interface{}{
	(MainEvent)(0), // 0: proto.MainEvent
}
var file_pbBase_event_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbBase_event_proto_init() }
func file_pbBase_event_proto_init() {
	if File_pbBase_event_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbBase_event_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbBase_event_proto_goTypes,
		DependencyIndexes: file_pbBase_event_proto_depIdxs,
		EnumInfos:         file_pbBase_event_proto_enumTypes,
	}.Build()
	File_pbBase_event_proto = out.File
	file_pbBase_event_proto_rawDesc = nil
	file_pbBase_event_proto_goTypes = nil
	file_pbBase_event_proto_depIdxs = nil
}
