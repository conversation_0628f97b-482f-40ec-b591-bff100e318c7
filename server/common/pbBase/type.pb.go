// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v6.30.1
// source: pbBase/type.proto

package pbBase

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// 玩家模型的状态
type PlayerModeType int32

const (
	ModeNormal       PlayerModeType = 0  //正常状态
	ModeShop         PlayerModeType = 1  //摆摊
	ModeBattleLocal  PlayerModeType = 50 //本地战斗
	ModeBattleRemote PlayerModeType = 51 //联网战斗
	ModeBattlePk     PlayerModeType = 52 //pk战斗
	ModeBattleOb     PlayerModeType = 53 //
	ModeBattlePetPk  PlayerModeType = 54 //宠物对战
)

// Enum value maps for PlayerModeType.
var (
	PlayerModeType_name = map[int32]string{
		0:  "ModeNormal",
		1:  "ModeShop",
		50: "ModeBattleLocal",
		51: "ModeBattleRemote",
		52: "ModeBattlePk",
		53: "ModeBattleOb",
		54: "ModeBattlePetPk",
	}
	PlayerModeType_value = map[string]int32{
		"ModeNormal":       0,
		"ModeShop":         1,
		"ModeBattleLocal":  50,
		"ModeBattleRemote": 51,
		"ModeBattlePk":     52,
		"ModeBattleOb":     53,
		"ModeBattlePetPk":  54,
	}
)

func (x PlayerModeType) Enum() *PlayerModeType {
	p := new(PlayerModeType)
	*p = x
	return p
}

func (x PlayerModeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PlayerModeType) Descriptor() protoreflect.EnumDescriptor {
	return file_pbBase_type_proto_enumTypes[0].Descriptor()
}

func (PlayerModeType) Type() protoreflect.EnumType {
	return &file_pbBase_type_proto_enumTypes[0]
}

func (x PlayerModeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PlayerModeType.Descriptor instead.
func (PlayerModeType) EnumDescriptor() ([]byte, []int) {
	return file_pbBase_type_proto_rawDescGZIP(), []int{0}
}

var File_pbBase_type_proto protoreflect.FileDescriptor

var file_pbBase_type_proto_rawDesc = []byte{
	0x0a, 0x11, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2a, 0x92, 0x01, 0x0a, 0x0e, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a,
	0x0a, 0x4d, 0x6f, 0x64, 0x65, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x10, 0x00, 0x12, 0x0c, 0x0a,
	0x08, 0x4d, 0x6f, 0x64, 0x65, 0x53, 0x68, 0x6f, 0x70, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x4d,
	0x6f, 0x64, 0x65, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x10, 0x32,
	0x12, 0x14, 0x0a, 0x10, 0x4d, 0x6f, 0x64, 0x65, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65,
	0x6d, 0x6f, 0x74, 0x65, 0x10, 0x33, 0x12, 0x10, 0x0a, 0x0c, 0x4d, 0x6f, 0x64, 0x65, 0x42, 0x61,
	0x74, 0x74, 0x6c, 0x65, 0x50, 0x6b, 0x10, 0x34, 0x12, 0x10, 0x0a, 0x0c, 0x4d, 0x6f, 0x64, 0x65,
	0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x4f, 0x62, 0x10, 0x35, 0x12, 0x13, 0x0a, 0x0f, 0x4d, 0x6f,
	0x64, 0x65, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x50, 0x6b, 0x10, 0x36, 0x42,
	0x1c, 0x5a, 0x1a, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x3b, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbBase_type_proto_rawDescOnce sync.Once
	file_pbBase_type_proto_rawDescData = file_pbBase_type_proto_rawDesc
)

func file_pbBase_type_proto_rawDescGZIP() []byte {
	file_pbBase_type_proto_rawDescOnce.Do(func() {
		file_pbBase_type_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbBase_type_proto_rawDescData)
	})
	return file_pbBase_type_proto_rawDescData
}

var file_pbBase_type_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pbBase_type_proto_goTypes = []interface{}{
	(PlayerModeType)(0), // 0: proto.PlayerModeType
}
var file_pbBase_type_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbBase_type_proto_init() }
func file_pbBase_type_proto_init() {
	if File_pbBase_type_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbBase_type_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbBase_type_proto_goTypes,
		DependencyIndexes: file_pbBase_type_proto_depIdxs,
		EnumInfos:         file_pbBase_type_proto_enumTypes,
	}.Build()
	File_pbBase_type_proto = out.File
	file_pbBase_type_proto_rawDesc = nil
	file_pbBase_type_proto_goTypes = nil
	file_pbBase_type_proto_depIdxs = nil
}
