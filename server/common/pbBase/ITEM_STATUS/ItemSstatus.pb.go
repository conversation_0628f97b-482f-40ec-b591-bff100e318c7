// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v6.30.1
// source: pbBase/ITEM_STATUS/ItemSstatus.proto

package ITEM_STATUS

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// 物品状态定义
type Type int32

const (
	None          Type = 0   //自动添加
	TIME_ITEM     Type = 1   //限时物品
	BIND          Type = 2   //绑定
	SHOP_LOCKED   Type = 4   //商店锁定
	TIME_OUT      Type = 8   //过期
	CAN_IDENTIFY  Type = 16  //可鉴定
	ATTACK_BROKEN Type = 32  //镶嵌破损
	VIP_TIME_OUT  Type = 64  //vip过期
	SELLING       Type = 256 //摆摊出售中
)

// Enum value maps for Type.
var (
	Type_name = map[int32]string{
		0:   "None",
		1:   "TIME_ITEM",
		2:   "BIND",
		4:   "SHOP_LOCKED",
		8:   "TIME_OUT",
		16:  "CAN_IDENTIFY",
		32:  "ATTACK_BROKEN",
		64:  "VIP_TIME_OUT",
		256: "SELLING",
	}
	Type_value = map[string]int32{
		"None":          0,
		"TIME_ITEM":     1,
		"BIND":          2,
		"SHOP_LOCKED":   4,
		"TIME_OUT":      8,
		"CAN_IDENTIFY":  16,
		"ATTACK_BROKEN": 32,
		"VIP_TIME_OUT":  64,
		"SELLING":       256,
	}
)

func (x Type) Enum() *Type {
	p := new(Type)
	*p = x
	return p
}

func (x Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Type) Descriptor() protoreflect.EnumDescriptor {
	return file_pbBase_ITEM_STATUS_ItemSstatus_proto_enumTypes[0].Descriptor()
}

func (Type) Type() protoreflect.EnumType {
	return &file_pbBase_ITEM_STATUS_ItemSstatus_proto_enumTypes[0]
}

func (x Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Type.Descriptor instead.
func (Type) EnumDescriptor() ([]byte, []int) {
	return file_pbBase_ITEM_STATUS_ItemSstatus_proto_rawDescGZIP(), []int{0}
}

var File_pbBase_ITEM_STATUS_ItemSstatus_proto protoreflect.FileDescriptor

var file_pbBase_ITEM_STATUS_ItemSstatus_proto_rawDesc = []byte{
	0x0a, 0x24, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x2f, 0x49, 0x74, 0x65, 0x6d, 0x53, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x54,
	0x45, 0x4d, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x2a, 0x8d, 0x01, 0x0a, 0x04, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09,
	0x54, 0x49, 0x4d, 0x45, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x42,
	0x49, 0x4e, 0x44, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x48, 0x4f, 0x50, 0x5f, 0x4c, 0x4f,
	0x43, 0x4b, 0x45, 0x44, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x4f,
	0x55, 0x54, 0x10, 0x08, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x41, 0x4e, 0x5f, 0x49, 0x44, 0x45, 0x4e,
	0x54, 0x49, 0x46, 0x59, 0x10, 0x10, 0x12, 0x11, 0x0a, 0x0d, 0x41, 0x54, 0x54, 0x41, 0x43, 0x4b,
	0x5f, 0x42, 0x52, 0x4f, 0x4b, 0x45, 0x4e, 0x10, 0x20, 0x12, 0x10, 0x0a, 0x0c, 0x56, 0x49, 0x50,
	0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x4f, 0x55, 0x54, 0x10, 0x40, 0x12, 0x0c, 0x0a, 0x07, 0x53,
	0x45, 0x4c, 0x4c, 0x49, 0x4e, 0x47, 0x10, 0x80, 0x02, 0x42, 0x2d, 0x5a, 0x2b, 0x77, 0x6f, 0x72,
	0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65,
	0x2f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x3b, 0x49, 0x54, 0x45,
	0x4d, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbBase_ITEM_STATUS_ItemSstatus_proto_rawDescOnce sync.Once
	file_pbBase_ITEM_STATUS_ItemSstatus_proto_rawDescData = file_pbBase_ITEM_STATUS_ItemSstatus_proto_rawDesc
)

func file_pbBase_ITEM_STATUS_ItemSstatus_proto_rawDescGZIP() []byte {
	file_pbBase_ITEM_STATUS_ItemSstatus_proto_rawDescOnce.Do(func() {
		file_pbBase_ITEM_STATUS_ItemSstatus_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbBase_ITEM_STATUS_ItemSstatus_proto_rawDescData)
	})
	return file_pbBase_ITEM_STATUS_ItemSstatus_proto_rawDescData
}

var file_pbBase_ITEM_STATUS_ItemSstatus_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pbBase_ITEM_STATUS_ItemSstatus_proto_goTypes = []interface{}{
	(Type)(0), // 0: proto.ITEM_STATUS.Type
}
var file_pbBase_ITEM_STATUS_ItemSstatus_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbBase_ITEM_STATUS_ItemSstatus_proto_init() }
func file_pbBase_ITEM_STATUS_ItemSstatus_proto_init() {
	if File_pbBase_ITEM_STATUS_ItemSstatus_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbBase_ITEM_STATUS_ItemSstatus_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbBase_ITEM_STATUS_ItemSstatus_proto_goTypes,
		DependencyIndexes: file_pbBase_ITEM_STATUS_ItemSstatus_proto_depIdxs,
		EnumInfos:         file_pbBase_ITEM_STATUS_ItemSstatus_proto_enumTypes,
	}.Build()
	File_pbBase_ITEM_STATUS_ItemSstatus_proto = out.File
	file_pbBase_ITEM_STATUS_ItemSstatus_proto_rawDesc = nil
	file_pbBase_ITEM_STATUS_ItemSstatus_proto_goTypes = nil
	file_pbBase_ITEM_STATUS_ItemSstatus_proto_depIdxs = nil
}
