// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v6.30.1
// source: pbBase/SKILL_TYPE/SkillType.proto

package SKILL_TYPE

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// 技能类型
type SKILL_TYPE int32

const (
	ZERO      SKILL_TYPE = 0 //自动添加
	PASSIVE   SKILL_TYPE = 1 //被动
	ROUND     SKILL_TYPE = 2 //自动
	ACTIVE    SKILL_TYPE = 3 //主动
	FORMATION SKILL_TYPE = 4 //阵型
)

// Enum value maps for SKILL_TYPE.
var (
	SKILL_TYPE_name = map[int32]string{
		0: "ZERO",
		1: "PASSIVE",
		2: "ROUND",
		3: "ACTIVE",
		4: "FORMATION",
	}
	SKILL_TYPE_value = map[string]int32{
		"ZERO":      0,
		"PASSIVE":   1,
		"ROUND":     2,
		"ACTIVE":    3,
		"FORMATION": 4,
	}
)

func (x SKILL_TYPE) Enum() *SKILL_TYPE {
	p := new(SKILL_TYPE)
	*p = x
	return p
}

func (x SKILL_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SKILL_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_pbBase_SKILL_TYPE_SkillType_proto_enumTypes[0].Descriptor()
}

func (SKILL_TYPE) Type() protoreflect.EnumType {
	return &file_pbBase_SKILL_TYPE_SkillType_proto_enumTypes[0]
}

func (x SKILL_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SKILL_TYPE.Descriptor instead.
func (SKILL_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_pbBase_SKILL_TYPE_SkillType_proto_rawDescGZIP(), []int{0}
}

var File_pbBase_SKILL_TYPE_SkillType_proto protoreflect.FileDescriptor

var file_pbBase_SKILL_TYPE_SkillType_proto_rawDesc = []byte{
	0x0a, 0x21, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x53, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x2f, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x10, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x4b, 0x49, 0x4c, 0x4c,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x2a, 0x49, 0x0a, 0x0a, 0x53, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x12, 0x08, 0x0a, 0x04, 0x5a, 0x45, 0x52, 0x4f, 0x10, 0x00, 0x12, 0x0b, 0x0a,
	0x07, 0x50, 0x41, 0x53, 0x53, 0x49, 0x56, 0x45, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x52, 0x4f,
	0x55, 0x4e, 0x44, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10,
	0x03, 0x12, 0x0d, 0x0a, 0x09, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x04,
	0x42, 0x2b, 0x5a, 0x29, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x53, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x3b, 0x53, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbBase_SKILL_TYPE_SkillType_proto_rawDescOnce sync.Once
	file_pbBase_SKILL_TYPE_SkillType_proto_rawDescData = file_pbBase_SKILL_TYPE_SkillType_proto_rawDesc
)

func file_pbBase_SKILL_TYPE_SkillType_proto_rawDescGZIP() []byte {
	file_pbBase_SKILL_TYPE_SkillType_proto_rawDescOnce.Do(func() {
		file_pbBase_SKILL_TYPE_SkillType_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbBase_SKILL_TYPE_SkillType_proto_rawDescData)
	})
	return file_pbBase_SKILL_TYPE_SkillType_proto_rawDescData
}

var file_pbBase_SKILL_TYPE_SkillType_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pbBase_SKILL_TYPE_SkillType_proto_goTypes = []interface{}{
	(SKILL_TYPE)(0), // 0: proto.SKILL_TYPE.SKILL_TYPE
}
var file_pbBase_SKILL_TYPE_SkillType_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbBase_SKILL_TYPE_SkillType_proto_init() }
func file_pbBase_SKILL_TYPE_SkillType_proto_init() {
	if File_pbBase_SKILL_TYPE_SkillType_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbBase_SKILL_TYPE_SkillType_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbBase_SKILL_TYPE_SkillType_proto_goTypes,
		DependencyIndexes: file_pbBase_SKILL_TYPE_SkillType_proto_depIdxs,
		EnumInfos:         file_pbBase_SKILL_TYPE_SkillType_proto_enumTypes,
	}.Build()
	File_pbBase_SKILL_TYPE_SkillType_proto = out.File
	file_pbBase_SKILL_TYPE_SkillType_proto_rawDesc = nil
	file_pbBase_SKILL_TYPE_SkillType_proto_goTypes = nil
	file_pbBase_SKILL_TYPE_SkillType_proto_depIdxs = nil
}
