// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v6.30.1
// source: pbBase/struct.proto

package pbBase

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	AreaLineState "world/common/pbBase/AreaLineState"
	BattleDefine "world/common/pbBase/BattleDefine"
	ConditionType "world/common/pbBase/ConditionType"
	MailDefine "world/common/pbBase/MailDefine"
	MyDefine "world/common/pbBase/MyDefine"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are structs.
// 登录结果信息
type LoginResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`       //用户id
	Token string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"` //会话凭证
}

func (x *LoginResult) Reset() {
	*x = LoginResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbBase_struct_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginResult) ProtoMessage() {}

func (x *LoginResult) ProtoReflect() protoreflect.Message {
	mi := &file_pbBase_struct_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginResult.ProtoReflect.Descriptor instead.
func (*LoginResult) Descriptor() ([]byte, []int) {
	return file_pbBase_struct_proto_rawDescGZIP(), []int{0}
}

func (x *LoginResult) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *LoginResult) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// 区服信息
type AreaLine struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int32              `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                     //区服id
	Name       string             `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                  //区服名称
	OpenTime   int32              `protobuf:"varint,3,opt,name=openTime,proto3" json:"openTime,omitempty"`                         //区服名称
	State      AreaLineState.Type `protobuf:"varint,4,opt,name=state,proto3,enum=proto.AreaLineState.Type" json:"state,omitempty"` //区服状态
	ActorCount int32              `protobuf:"varint,5,opt,name=actorCount,proto3" json:"actorCount,omitempty"`                     //拥有角色数量
}

func (x *AreaLine) Reset() {
	*x = AreaLine{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbBase_struct_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AreaLine) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AreaLine) ProtoMessage() {}

func (x *AreaLine) ProtoReflect() protoreflect.Message {
	mi := &file_pbBase_struct_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AreaLine.ProtoReflect.Descriptor instead.
func (*AreaLine) Descriptor() ([]byte, []int) {
	return file_pbBase_struct_proto_rawDescGZIP(), []int{1}
}

func (x *AreaLine) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AreaLine) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AreaLine) GetOpenTime() int32 {
	if x != nil {
		return x.OpenTime
	}
	return 0
}

func (x *AreaLine) GetState() AreaLineState.Type {
	if x != nil {
		return x.State
	}
	return AreaLineState.Type(0)
}

func (x *AreaLine) GetActorCount() int32 {
	if x != nil {
		return x.ActorCount
	}
	return 0
}

// 跨节点简要传递玩家数据
type CrossSimplePlayer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	From        string          `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"`                //发送方，即玩家当前所在节点
	Id          string          `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`                    //玩家用户id
	GameId      int32           `protobuf:"varint,3,opt,name=gameId,proto3" json:"gameId,omitempty"`           //玩家游戏id
	Name        string          `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                //玩家名称
	MapId       int32           `protobuf:"varint,5,opt,name=mapId,proto3" json:"mapId,omitempty"`             //所处地图id
	X           int32           `protobuf:"varint,6,opt,name=x,proto3" json:"x,omitempty"`                     //所处地图x
	Y           int32           `protobuf:"varint,7,opt,name=y,proto3" json:"y,omitempty"`                     //所处地图y
	Icon1       int64           `protobuf:"varint,8,opt,name=icon1,proto3" json:"icon1,omitempty"`             //icon1
	Icon2       int64           `protobuf:"varint,9,opt,name=icon2,proto3" json:"icon2,omitempty"`             //icon2
	Icon3       int64           `protobuf:"varint,10,opt,name=icon3,proto3" json:"icon3,omitempty"`            //icon3
	Level       int32           `protobuf:"varint,11,opt,name=level,proto3" json:"level,omitempty"`            //等级
	Level2      int32           `protobuf:"varint,12,opt,name=level2,proto3" json:"level2,omitempty"`          //传奇等级
	Title       string          `protobuf:"bytes,13,opt,name=title,proto3" json:"title,omitempty"`             //称号
	Setting     int64           `protobuf:"varint,14,opt,name=setting,proto3" json:"setting,omitempty"`        //设置
	Status      int64           `protobuf:"varint,15,opt,name=status,proto3" json:"status,omitempty"`          //状态
	Mode        int32           `protobuf:"varint,16,opt,name=mode,proto3" json:"mode,omitempty"`              //设置
	ShopName    string          `protobuf:"bytes,17,opt,name=shopName,proto3" json:"shopName,omitempty"`       //摆摊名称
	CountryName string          `protobuf:"bytes,18,opt,name=countryName,proto3" json:"countryName,omitempty"` //国家名称
	VipLv       int32           `protobuf:"varint,19,opt,name=vipLv,proto3" json:"vipLv,omitempty"`            //vip等级
	VipLvMax    int32           `protobuf:"varint,20,opt,name=vipLvMax,proto3" json:"vipLvMax,omitempty"`      //历史最高vip等级
	Pet         *CrossSimplePet `protobuf:"bytes,21,opt,name=pet,proto3" json:"pet,omitempty"`                 //宠物数据
}

func (x *CrossSimplePlayer) Reset() {
	*x = CrossSimplePlayer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbBase_struct_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CrossSimplePlayer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CrossSimplePlayer) ProtoMessage() {}

func (x *CrossSimplePlayer) ProtoReflect() protoreflect.Message {
	mi := &file_pbBase_struct_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CrossSimplePlayer.ProtoReflect.Descriptor instead.
func (*CrossSimplePlayer) Descriptor() ([]byte, []int) {
	return file_pbBase_struct_proto_rawDescGZIP(), []int{2}
}

func (x *CrossSimplePlayer) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *CrossSimplePlayer) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CrossSimplePlayer) GetGameId() int32 {
	if x != nil {
		return x.GameId
	}
	return 0
}

func (x *CrossSimplePlayer) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CrossSimplePlayer) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *CrossSimplePlayer) GetX() int32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *CrossSimplePlayer) GetY() int32 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *CrossSimplePlayer) GetIcon1() int64 {
	if x != nil {
		return x.Icon1
	}
	return 0
}

func (x *CrossSimplePlayer) GetIcon2() int64 {
	if x != nil {
		return x.Icon2
	}
	return 0
}

func (x *CrossSimplePlayer) GetIcon3() int64 {
	if x != nil {
		return x.Icon3
	}
	return 0
}

func (x *CrossSimplePlayer) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *CrossSimplePlayer) GetLevel2() int32 {
	if x != nil {
		return x.Level2
	}
	return 0
}

func (x *CrossSimplePlayer) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CrossSimplePlayer) GetSetting() int64 {
	if x != nil {
		return x.Setting
	}
	return 0
}

func (x *CrossSimplePlayer) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *CrossSimplePlayer) GetMode() int32 {
	if x != nil {
		return x.Mode
	}
	return 0
}

func (x *CrossSimplePlayer) GetShopName() string {
	if x != nil {
		return x.ShopName
	}
	return ""
}

func (x *CrossSimplePlayer) GetCountryName() string {
	if x != nil {
		return x.CountryName
	}
	return ""
}

func (x *CrossSimplePlayer) GetVipLv() int32 {
	if x != nil {
		return x.VipLv
	}
	return 0
}

func (x *CrossSimplePlayer) GetVipLvMax() int32 {
	if x != nil {
		return x.VipLvMax
	}
	return 0
}

func (x *CrossSimplePlayer) GetPet() *CrossSimplePet {
	if x != nil {
		return x.Pet
	}
	return nil
}

// 跨节点简要传递宠物数据
type CrossSimplePet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CfgId int32  `protobuf:"varint,1,opt,name=cfgId,proto3" json:"cfgId,omitempty"` //宠物配置id
	Id    int64  `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`       //宠物id
	Name  string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`    //自定义名字，可能存在
	Age   int64  `protobuf:"varint,4,opt,name=age,proto3" json:"age,omitempty"`     //寿命
}

func (x *CrossSimplePet) Reset() {
	*x = CrossSimplePet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbBase_struct_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CrossSimplePet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CrossSimplePet) ProtoMessage() {}

func (x *CrossSimplePet) ProtoReflect() protoreflect.Message {
	mi := &file_pbBase_struct_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CrossSimplePet.ProtoReflect.Descriptor instead.
func (*CrossSimplePet) Descriptor() ([]byte, []int) {
	return file_pbBase_struct_proto_rawDescGZIP(), []int{3}
}

func (x *CrossSimplePet) GetCfgId() int32 {
	if x != nil {
		return x.CfgId
	}
	return 0
}

func (x *CrossSimplePet) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CrossSimplePet) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CrossSimplePet) GetAge() int64 {
	if x != nil {
		return x.Age
	}
	return 0
}

// 玩家事件数据
type PlayerEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EventId    int32              `protobuf:"varint,1,opt,name=eventId,proto3" json:"eventId,omitempty"`       //事件id
	EventType  int32              `protobuf:"varint,2,opt,name=eventType,proto3" json:"eventType,omitempty"`   //事件类型
	Message    string             `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`        //事件消息
	ExtraInfo  string             `protobuf:"bytes,4,opt,name=extraInfo,proto3" json:"extraInfo,omitempty"`    //额外信息
	ExpireTime int64              `protobuf:"varint,5,opt,name=expireTime,proto3" json:"expireTime,omitempty"` //过期时间
	Player     *CrossSimplePlayer `protobuf:"bytes,6,opt,name=player,proto3" json:"player,omitempty"`          //发起者
}

func (x *PlayerEvent) Reset() {
	*x = PlayerEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbBase_struct_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerEvent) ProtoMessage() {}

func (x *PlayerEvent) ProtoReflect() protoreflect.Message {
	mi := &file_pbBase_struct_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerEvent.ProtoReflect.Descriptor instead.
func (*PlayerEvent) Descriptor() ([]byte, []int) {
	return file_pbBase_struct_proto_rawDescGZIP(), []int{4}
}

func (x *PlayerEvent) GetEventId() int32 {
	if x != nil {
		return x.EventId
	}
	return 0
}

func (x *PlayerEvent) GetEventType() int32 {
	if x != nil {
		return x.EventType
	}
	return 0
}

func (x *PlayerEvent) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PlayerEvent) GetExtraInfo() string {
	if x != nil {
		return x.ExtraInfo
	}
	return ""
}

func (x *PlayerEvent) GetExpireTime() int64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

func (x *PlayerEvent) GetPlayer() *CrossSimplePlayer {
	if x != nil {
		return x.Player
	}
	return nil
}

// 简要玩家数据,用于拉取角色列表时使用
type SimplePlayerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            int32            `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                       //玩家id
	MapId         int32            `protobuf:"varint,2,opt,name=mapId,proto3" json:"mapId,omitempty"`                 //当前所处地图
	X             int32            `protobuf:"varint,3,opt,name=x,proto3" json:"x,omitempty"`                         //所处地图x
	Y             int32            `protobuf:"varint,4,opt,name=y,proto3" json:"y,omitempty"`                         //所处地图y
	Name          string           `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`                    //玩家名称
	Attr          *AttrData        `protobuf:"bytes,6,opt,name=attr,proto3" json:"attr,omitempty"`                    //属性
	DeleteEndTime int64            `protobuf:"varint,7,opt,name=deleteEndTime,proto3" json:"deleteEndTime,omitempty"` //删除计时
	OfflineTask   *OfflineTaskData `protobuf:"bytes,8,opt,name=offlineTask,proto3" json:"offlineTask,omitempty"`      //离线任务数据
}

func (x *SimplePlayerInfo) Reset() {
	*x = SimplePlayerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbBase_struct_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SimplePlayerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimplePlayerInfo) ProtoMessage() {}

func (x *SimplePlayerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pbBase_struct_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimplePlayerInfo.ProtoReflect.Descriptor instead.
func (*SimplePlayerInfo) Descriptor() ([]byte, []int) {
	return file_pbBase_struct_proto_rawDescGZIP(), []int{5}
}

func (x *SimplePlayerInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SimplePlayerInfo) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *SimplePlayerInfo) GetX() int32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *SimplePlayerInfo) GetY() int32 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *SimplePlayerInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SimplePlayerInfo) GetAttr() *AttrData {
	if x != nil {
		return x.Attr
	}
	return nil
}

func (x *SimplePlayerInfo) GetDeleteEndTime() int64 {
	if x != nil {
		return x.DeleteEndTime
	}
	return 0
}

func (x *SimplePlayerInfo) GetOfflineTask() *OfflineTaskData {
	if x != nil {
		return x.OfflineTask
	}
	return nil
}

// 玩家数据,玩家进入游戏时推送
type PlayerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int32      `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                           //玩家id
	MapId       int32      `protobuf:"varint,2,opt,name=mapId,proto3" json:"mapId,omitempty"`                     //当前所处地图
	X           int32      `protobuf:"varint,3,opt,name=x,proto3" json:"x,omitempty"`                             //所处地图x
	Y           int32      `protobuf:"varint,4,opt,name=y,proto3" json:"y,omitempty"`                             //所处地图y
	Name        string     `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`                        //玩家名称
	Setting     int64      `protobuf:"varint,6,opt,name=setting,proto3" json:"setting,omitempty"`                 //设置
	Mode        int32      `protobuf:"varint,7,opt,name=mode,proto3" json:"mode,omitempty"`                       //设置
	Attr        *AttrData  `protobuf:"bytes,8,opt,name=attr,proto3" json:"attr,omitempty"`                        //属性
	Bag         *BagData   `protobuf:"bytes,9,opt,name=bag,proto3" json:"bag,omitempty"`                          //背包
	Task        *TaskData  `protobuf:"bytes,10,opt,name=task,proto3" json:"task,omitempty"`                       //任务
	ItemSetData []int32    `protobuf:"varint,11,rep,packed,name=itemSetData,proto3" json:"itemSetData,omitempty"` //玩家套装数据
	Skill       *SkillData `protobuf:"bytes,12,opt,name=skill,proto3" json:"skill,omitempty"`                     //技能
	PetId       int64      `protobuf:"varint,13,opt,name=petId,proto3" json:"petId,omitempty"`                    //上阵的宠物
	UnreadMail  bool       `protobuf:"varint,14,opt,name=unreadMail,proto3" json:"unreadMail,omitempty"`          //是否有未读邮件
}

func (x *PlayerInfo) Reset() {
	*x = PlayerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbBase_struct_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerInfo) ProtoMessage() {}

func (x *PlayerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pbBase_struct_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerInfo.ProtoReflect.Descriptor instead.
func (*PlayerInfo) Descriptor() ([]byte, []int) {
	return file_pbBase_struct_proto_rawDescGZIP(), []int{6}
}

func (x *PlayerInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PlayerInfo) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *PlayerInfo) GetX() int32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *PlayerInfo) GetY() int32 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *PlayerInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PlayerInfo) GetSetting() int64 {
	if x != nil {
		return x.Setting
	}
	return 0
}

func (x *PlayerInfo) GetMode() int32 {
	if x != nil {
		return x.Mode
	}
	return 0
}

func (x *PlayerInfo) GetAttr() *AttrData {
	if x != nil {
		return x.Attr
	}
	return nil
}

func (x *PlayerInfo) GetBag() *BagData {
	if x != nil {
		return x.Bag
	}
	return nil
}

func (x *PlayerInfo) GetTask() *TaskData {
	if x != nil {
		return x.Task
	}
	return nil
}

func (x *PlayerInfo) GetItemSetData() []int32 {
	if x != nil {
		return x.ItemSetData
	}
	return nil
}

func (x *PlayerInfo) GetSkill() *SkillData {
	if x != nil {
		return x.Skill
	}
	return nil
}

func (x *PlayerInfo) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PlayerInfo) GetUnreadMail() bool {
	if x != nil {
		return x.UnreadMail
	}
	return false
}

// 属性模块
type AttrData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Icon1    int64 `protobuf:"varint,1,opt,name=icon1,proto3" json:"icon1,omitempty"`        //icon1
	Icon2    int64 `protobuf:"varint,2,opt,name=icon2,proto3" json:"icon2,omitempty"`        //icon2
	Icon3    int64 `protobuf:"varint,3,opt,name=icon3,proto3" json:"icon3,omitempty"`        //icon3
	Status   int64 `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`      //状态
	Level    int32 `protobuf:"varint,5,opt,name=level,proto3" json:"level,omitempty"`        //等级
	Level2   int32 `protobuf:"varint,6,opt,name=level2,proto3" json:"level2,omitempty"`      //传奇等级
	Exp      int32 `protobuf:"varint,7,opt,name=exp,proto3" json:"exp,omitempty"`            //普通经验
	Exp2     int32 `protobuf:"varint,8,opt,name=exp2,proto3" json:"exp2,omitempty"`          //传奇经验
	VipLv    int32 `protobuf:"varint,9,opt,name=vipLv,proto3" json:"vipLv,omitempty"`        //vip等级
	VipLvMax int32 `protobuf:"varint,10,opt,name=vipLvMax,proto3" json:"vipLvMax,omitempty"` //历史最高vip等级
	Cp       int32 `protobuf:"varint,11,opt,name=cp,proto3" json:"cp,omitempty"`             //未使用的技能点
	Str      int32 `protobuf:"varint,12,opt,name=str,proto3" json:"str,omitempty"`           //力量
	Agi      int32 `protobuf:"varint,13,opt,name=agi,proto3" json:"agi,omitempty"`           //敏捷
	Con      int32 `protobuf:"varint,14,opt,name=con,proto3" json:"con,omitempty"`           //体质
	Ilt      int32 `protobuf:"varint,15,opt,name=ilt,proto3" json:"ilt,omitempty"`           //智力
	Wis      int32 `protobuf:"varint,16,opt,name=wis,proto3" json:"wis,omitempty"`           //感知
	Hp       int32 `protobuf:"varint,17,opt,name=hp,proto3" json:"hp,omitempty"`             //血
	Mp       int32 `protobuf:"varint,18,opt,name=mp,proto3" json:"mp,omitempty"`             //蓝
}

func (x *AttrData) Reset() {
	*x = AttrData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbBase_struct_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AttrData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttrData) ProtoMessage() {}

func (x *AttrData) ProtoReflect() protoreflect.Message {
	mi := &file_pbBase_struct_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttrData.ProtoReflect.Descriptor instead.
func (*AttrData) Descriptor() ([]byte, []int) {
	return file_pbBase_struct_proto_rawDescGZIP(), []int{7}
}

func (x *AttrData) GetIcon1() int64 {
	if x != nil {
		return x.Icon1
	}
	return 0
}

func (x *AttrData) GetIcon2() int64 {
	if x != nil {
		return x.Icon2
	}
	return 0
}

func (x *AttrData) GetIcon3() int64 {
	if x != nil {
		return x.Icon3
	}
	return 0
}

func (x *AttrData) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *AttrData) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *AttrData) GetLevel2() int32 {
	if x != nil {
		return x.Level2
	}
	return 0
}

func (x *AttrData) GetExp() int32 {
	if x != nil {
		return x.Exp
	}
	return 0
}

func (x *AttrData) GetExp2() int32 {
	if x != nil {
		return x.Exp2
	}
	return 0
}

func (x *AttrData) GetVipLv() int32 {
	if x != nil {
		return x.VipLv
	}
	return 0
}

func (x *AttrData) GetVipLvMax() int32 {
	if x != nil {
		return x.VipLvMax
	}
	return 0
}

func (x *AttrData) GetCp() int32 {
	if x != nil {
		return x.Cp
	}
	return 0
}

func (x *AttrData) GetStr() int32 {
	if x != nil {
		return x.Str
	}
	return 0
}

func (x *AttrData) GetAgi() int32 {
	if x != nil {
		return x.Agi
	}
	return 0
}

func (x *AttrData) GetCon() int32 {
	if x != nil {
		return x.Con
	}
	return 0
}

func (x *AttrData) GetIlt() int32 {
	if x != nil {
		return x.Ilt
	}
	return 0
}

func (x *AttrData) GetWis() int32 {
	if x != nil {
		return x.Wis
	}
	return 0
}

func (x *AttrData) GetHp() int32 {
	if x != nil {
		return x.Hp
	}
	return 0
}

func (x *AttrData) GetMp() int32 {
	if x != nil {
		return x.Mp
	}
	return 0
}

// 背包数据
type BagData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Money1        int32               `protobuf:"varint,1,opt,name=money1,proto3" json:"money1,omitempty"`                                                                                       //黄金
	Money2        int32               `protobuf:"varint,2,opt,name=money2,proto3" json:"money2,omitempty"`                                                                                       //金叶
	Money3        int32               `protobuf:"varint,3,opt,name=money3,proto3" json:"money3,omitempty"`                                                                                       //铜币
	BagSize       int32               `protobuf:"varint,4,opt,name=bagSize,proto3" json:"bagSize,omitempty"`                                                                                     //背包格子数量
	SelfStoreSize int32               `protobuf:"varint,5,opt,name=selfStoreSize,proto3" json:"selfStoreSize,omitempty"`                                                                         //个人仓库购买数量
	Store         map[int32]*ItemData `protobuf:"bytes,6,rep,name=store,proto3" json:"store,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //物品数据
}

func (x *BagData) Reset() {
	*x = BagData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbBase_struct_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BagData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BagData) ProtoMessage() {}

func (x *BagData) ProtoReflect() protoreflect.Message {
	mi := &file_pbBase_struct_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BagData.ProtoReflect.Descriptor instead.
func (*BagData) Descriptor() ([]byte, []int) {
	return file_pbBase_struct_proto_rawDescGZIP(), []int{8}
}

func (x *BagData) GetMoney1() int32 {
	if x != nil {
		return x.Money1
	}
	return 0
}

func (x *BagData) GetMoney2() int32 {
	if x != nil {
		return x.Money2
	}
	return 0
}

func (x *BagData) GetMoney3() int32 {
	if x != nil {
		return x.Money3
	}
	return 0
}

func (x *BagData) GetBagSize() int32 {
	if x != nil {
		return x.BagSize
	}
	return 0
}

func (x *BagData) GetSelfStoreSize() int32 {
	if x != nil {
		return x.SelfStoreSize
	}
	return 0
}

func (x *BagData) GetStore() map[int32]*ItemData {
	if x != nil {
		return x.Store
	}
	return nil
}

// 物品数据
type PetData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CfgId     int32      `protobuf:"varint,1,opt,name=cfgId,proto3" json:"cfgId,omitempty"`         //宠物配置id
	Name      string     `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`            //自定义名字，可能存在
	Grow      int32      `protobuf:"varint,3,opt,name=grow,proto3" json:"grow,omitempty"`           //成长值
	Learn     int32      `protobuf:"varint,4,opt,name=learn,proto3" json:"learn,omitempty"`         //领悟值
	GrowLevel int32      `protobuf:"varint,5,opt,name=growLevel,proto3" json:"growLevel,omitempty"` //成长等级
	Attr      *AttrData  `protobuf:"bytes,6,opt,name=attr,proto3" json:"attr,omitempty"`            //属性
	Skill     *SkillData `protobuf:"bytes,7,opt,name=skill,proto3" json:"skill,omitempty"`          //技能
	Age       int64      `protobuf:"varint,8,opt,name=age,proto3" json:"age,omitempty"`             //寿命
	GrowExp   int32      `protobuf:"varint,9,opt,name=growExp,proto3" json:"growExp,omitempty"`     //成长经验
	Id        int64      `protobuf:"varint,10,opt,name=id,proto3" json:"id,omitempty"`              //宠物id
}

func (x *PetData) Reset() {
	*x = PetData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbBase_struct_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetData) ProtoMessage() {}

func (x *PetData) ProtoReflect() protoreflect.Message {
	mi := &file_pbBase_struct_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetData.ProtoReflect.Descriptor instead.
func (*PetData) Descriptor() ([]byte, []int) {
	return file_pbBase_struct_proto_rawDescGZIP(), []int{9}
}

func (x *PetData) GetCfgId() int32 {
	if x != nil {
		return x.CfgId
	}
	return 0
}

func (x *PetData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PetData) GetGrow() int32 {
	if x != nil {
		return x.Grow
	}
	return 0
}

func (x *PetData) GetLearn() int32 {
	if x != nil {
		return x.Learn
	}
	return 0
}

func (x *PetData) GetGrowLevel() int32 {
	if x != nil {
		return x.GrowLevel
	}
	return 0
}

func (x *PetData) GetAttr() *AttrData {
	if x != nil {
		return x.Attr
	}
	return nil
}

func (x *PetData) GetSkill() *SkillData {
	if x != nil {
		return x.Skill
	}
	return nil
}

func (x *PetData) GetAge() int64 {
	if x != nil {
		return x.Age
	}
	return 0
}

func (x *PetData) GetGrowExp() int32 {
	if x != nil {
		return x.GrowExp
	}
	return 0
}

func (x *PetData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 物品数据
type ItemData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            int32             `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                                                               //id
	SlotPos       int32             `protobuf:"varint,2,opt,name=slotPos,proto3" json:"slotPos,omitempty"`                                                                                     //位置
	Quantity      int32             `protobuf:"varint,3,opt,name=quantity,proto3" json:"quantity,omitempty"`                                                                                   //数量
	Status        int32             `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`                                                                                       //状态
	Power1        *PowerData        `protobuf:"bytes,5,opt,name=power1,proto3" json:"power1,omitempty"`                                                                                        //基础属性1
	Power2        *PowerData        `protobuf:"bytes,6,opt,name=power2,proto3" json:"power2,omitempty"`                                                                                        //基础属性2
	Power3        *PowerData        `protobuf:"bytes,7,opt,name=power3,proto3" json:"power3,omitempty"`                                                                                        //基础属性3
	BindPower1    *PowerData        `protobuf:"bytes,8,opt,name=bindPower1,proto3" json:"bindPower1,omitempty"`                                                                                //绑定属性1
	BindPower2    *PowerData        `protobuf:"bytes,9,opt,name=bindPower2,proto3" json:"bindPower2,omitempty"`                                                                                //绑定属性2
	Power4        *PowerData        `protobuf:"bytes,10,opt,name=power4,proto3" json:"power4,omitempty"`                                                                                       //进阶属性1
	Power5        *PowerData        `protobuf:"bytes,11,opt,name=power5,proto3" json:"power5,omitempty"`                                                                                       //进阶属性2
	Power6        *PowerData        `protobuf:"bytes,12,opt,name=power6,proto3" json:"power6,omitempty"`                                                                                       //进阶属性3
	Power7        *PowerData        `protobuf:"bytes,13,opt,name=power7,proto3" json:"power7,omitempty"`                                                                                       //进阶属性4
	EnchantPower1 *PowerData        `protobuf:"bytes,14,opt,name=enchantPower1,proto3" json:"enchantPower1,omitempty"`                                                                         //附魔1
	EnchantPower2 *PowerData        `protobuf:"bytes,15,opt,name=enchantPower2,proto3" json:"enchantPower2,omitempty"`                                                                         //附魔2
	Durability    int32             `protobuf:"varint,16,opt,name=durability,proto3" json:"durability,omitempty"`                                                                              //耐久
	AttachDone    int32             `protobuf:"varint,17,opt,name=attachDone,proto3" json:"attachDone,omitempty"`                                                                              //宝石镶嵌数量
	AttachPower   *PowerData        `protobuf:"bytes,18,opt,name=attachPower,proto3" json:"attachPower,omitempty"`                                                                             //宝石属性
	ExpireTime    int64             `protobuf:"varint,19,opt,name=expireTime,proto3" json:"expireTime,omitempty"`                                                                              //过期时间
	Star          int32             `protobuf:"varint,20,opt,name=star,proto3" json:"star,omitempty"`                                                                                          //普通升星数量
	UpgradeStar   int32             `protobuf:"varint,21,opt,name=upgradeStar,proto3" json:"upgradeStar,omitempty"`                                                                            //进阶升星数量
	PetId         int64             `protobuf:"varint,22,opt,name=petId,proto3" json:"petId,omitempty"`                                                                                        //宠物唯一id
	PetItem       *PetData          `protobuf:"bytes,23,opt,name=petItem,proto3" json:"petItem,omitempty"`                                                                                     //宠物数据
	Extra         map[string]string `protobuf:"bytes,24,rep,name=extra,proto3" json:"extra,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //扩展信息
}

func (x *ItemData) Reset() {
	*x = ItemData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbBase_struct_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ItemData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItemData) ProtoMessage() {}

func (x *ItemData) ProtoReflect() protoreflect.Message {
	mi := &file_pbBase_struct_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItemData.ProtoReflect.Descriptor instead.
func (*ItemData) Descriptor() ([]byte, []int) {
	return file_pbBase_struct_proto_rawDescGZIP(), []int{10}
}

func (x *ItemData) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ItemData) GetSlotPos() int32 {
	if x != nil {
		return x.SlotPos
	}
	return 0
}

func (x *ItemData) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *ItemData) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ItemData) GetPower1() *PowerData {
	if x != nil {
		return x.Power1
	}
	return nil
}

func (x *ItemData) GetPower2() *PowerData {
	if x != nil {
		return x.Power2
	}
	return nil
}

func (x *ItemData) GetPower3() *PowerData {
	if x != nil {
		return x.Power3
	}
	return nil
}

func (x *ItemData) GetBindPower1() *PowerData {
	if x != nil {
		return x.BindPower1
	}
	return nil
}

func (x *ItemData) GetBindPower2() *PowerData {
	if x != nil {
		return x.BindPower2
	}
	return nil
}

func (x *ItemData) GetPower4() *PowerData {
	if x != nil {
		return x.Power4
	}
	return nil
}

func (x *ItemData) GetPower5() *PowerData {
	if x != nil {
		return x.Power5
	}
	return nil
}

func (x *ItemData) GetPower6() *PowerData {
	if x != nil {
		return x.Power6
	}
	return nil
}

func (x *ItemData) GetPower7() *PowerData {
	if x != nil {
		return x.Power7
	}
	return nil
}

func (x *ItemData) GetEnchantPower1() *PowerData {
	if x != nil {
		return x.EnchantPower1
	}
	return nil
}

func (x *ItemData) GetEnchantPower2() *PowerData {
	if x != nil {
		return x.EnchantPower2
	}
	return nil
}

func (x *ItemData) GetDurability() int32 {
	if x != nil {
		return x.Durability
	}
	return 0
}

func (x *ItemData) GetAttachDone() int32 {
	if x != nil {
		return x.AttachDone
	}
	return 0
}

func (x *ItemData) GetAttachPower() *PowerData {
	if x != nil {
		return x.AttachPower
	}
	return nil
}

func (x *ItemData) GetExpireTime() int64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

func (x *ItemData) GetStar() int32 {
	if x != nil {
		return x.Star
	}
	return 0
}

func (x *ItemData) GetUpgradeStar() int32 {
	if x != nil {
		return x.UpgradeStar
	}
	return 0
}

func (x *ItemData) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ItemData) GetPetItem() *PetData {
	if x != nil {
		return x.PetItem
	}
	return nil
}

func (x *ItemData) GetExtra() map[string]string {
	if x != nil {
		return x.Extra
	}
	return nil
}

// 条件数据
type Condition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type  ConditionType.Type `protobuf:"varint,1,opt,name=type,proto3,enum=proto.ConditionType.Type" json:"type,omitempty"` //类型
	Id    int32              `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`                                   //id
	Num   int32              `protobuf:"varint,3,opt,name=num,proto3" json:"num,omitempty"`                                 //需求数量
	Extra string             `protobuf:"bytes,4,opt,name=extra,proto3" json:"extra,omitempty"`                              //扩展数据
}

func (x *Condition) Reset() {
	*x = Condition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbBase_struct_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Condition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Condition) ProtoMessage() {}

func (x *Condition) ProtoReflect() protoreflect.Message {
	mi := &file_pbBase_struct_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Condition.ProtoReflect.Descriptor instead.
func (*Condition) Descriptor() ([]byte, []int) {
	return file_pbBase_struct_proto_rawDescGZIP(), []int{11}
}

func (x *Condition) GetType() ConditionType.Type {
	if x != nil {
		return x.Type
	}
	return ConditionType.Type(0)
}

func (x *Condition) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Condition) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *Condition) GetExtra() string {
	if x != nil {
		return x.Extra
	}
	return ""
}

// 任务模块数据
type TaskData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskStatus []byte      `protobuf:"bytes,1,opt,name=taskStatus,proto3" json:"taskStatus,omitempty"` //物品数据
	Tasks      []*TaskInfo `protobuf:"bytes,2,rep,name=tasks,proto3" json:"tasks,omitempty"`           //物品数据
}

func (x *TaskData) Reset() {
	*x = TaskData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbBase_struct_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskData) ProtoMessage() {}

func (x *TaskData) ProtoReflect() protoreflect.Message {
	mi := &file_pbBase_struct_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskData.ProtoReflect.Descriptor instead.
func (*TaskData) Descriptor() ([]byte, []int) {
	return file_pbBase_struct_proto_rawDescGZIP(), []int{12}
}

func (x *TaskData) GetTaskStatus() []byte {
	if x != nil {
		return x.TaskStatus
	}
	return nil
}

func (x *TaskData) GetTasks() []*TaskInfo {
	if x != nil {
		return x.Tasks
	}
	return nil
}

// 任务数据
type TaskInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int32        `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`    //物品数据
	Cond []*Condition `protobuf:"bytes,2,rep,name=cond,proto3" json:"cond,omitempty"` //条件详情
}

func (x *TaskInfo) Reset() {
	*x = TaskInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbBase_struct_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskInfo) ProtoMessage() {}

func (x *TaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pbBase_struct_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskInfo.ProtoReflect.Descriptor instead.
func (*TaskInfo) Descriptor() ([]byte, []int) {
	return file_pbBase_struct_proto_rawDescGZIP(), []int{13}
}

func (x *TaskInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TaskInfo) GetCond() []*Condition {
	if x != nil {
		return x.Cond
	}
	return nil
}

// 属性数据
type PowerData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type  MyDefine.POWER `protobuf:"varint,1,opt,name=type,proto3,enum=proto.MyDefine.POWER" json:"type,omitempty"` //属性类型
	Value int32          `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`                         //属性值
}

func (x *PowerData) Reset() {
	*x = PowerData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbBase_struct_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PowerData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PowerData) ProtoMessage() {}

func (x *PowerData) ProtoReflect() protoreflect.Message {
	mi := &file_pbBase_struct_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PowerData.ProtoReflect.Descriptor instead.
func (*PowerData) Descriptor() ([]byte, []int) {
	return file_pbBase_struct_proto_rawDescGZIP(), []int{14}
}

func (x *PowerData) GetType() MyDefine.POWER {
	if x != nil {
		return x.Type
	}
	return MyDefine.POWER(0)
}

func (x *PowerData) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

// 离线任务数据
type OfflineTaskData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *OfflineTaskData) Reset() {
	*x = OfflineTaskData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbBase_struct_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OfflineTaskData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfflineTaskData) ProtoMessage() {}

func (x *OfflineTaskData) ProtoReflect() protoreflect.Message {
	mi := &file_pbBase_struct_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfflineTaskData.ProtoReflect.Descriptor instead.
func (*OfflineTaskData) Descriptor() ([]byte, []int) {
	return file_pbBase_struct_proto_rawDescGZIP(), []int{15}
}

// 点位数据
type Point struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X int32 `protobuf:"varint,1,opt,name=x,proto3" json:"x,omitempty"` //x
	Y int32 `protobuf:"varint,2,opt,name=y,proto3" json:"y,omitempty"` //y
}

func (x *Point) Reset() {
	*x = Point{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbBase_struct_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Point) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Point) ProtoMessage() {}

func (x *Point) ProtoReflect() protoreflect.Message {
	mi := &file_pbBase_struct_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Point.ProtoReflect.Descriptor instead.
func (*Point) Descriptor() ([]byte, []int) {
	return file_pbBase_struct_proto_rawDescGZIP(), []int{16}
}

func (x *Point) GetX() int32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *Point) GetY() int32 {
	if x != nil {
		return x.Y
	}
	return 0
}

// 技能模块数据
type SkillData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sp                int32                `protobuf:"varint,1,opt,name=sp,proto3" json:"sp,omitempty"`                                                                                             //技能点
	List              map[int32]*SkillInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //技能列表数据
	Cnt               int32                `protobuf:"varint,3,opt,name=cnt,proto3" json:"cnt,omitempty"`                                                                                           //技能槽数量
	ActiveAutoSkillId int32                `protobuf:"varint,4,opt,name=activeAutoSkillId,proto3" json:"activeAutoSkillId,omitempty"`                                                               //自动释放的主动技能id
	AutoSkillId       []int32              `protobuf:"varint,5,rep,packed,name=autoSkillId,proto3" json:"autoSkillId,omitempty"`                                                                    //自动释放的自动技能id
}

func (x *SkillData) Reset() {
	*x = SkillData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbBase_struct_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SkillData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SkillData) ProtoMessage() {}

func (x *SkillData) ProtoReflect() protoreflect.Message {
	mi := &file_pbBase_struct_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SkillData.ProtoReflect.Descriptor instead.
func (*SkillData) Descriptor() ([]byte, []int) {
	return file_pbBase_struct_proto_rawDescGZIP(), []int{17}
}

func (x *SkillData) GetSp() int32 {
	if x != nil {
		return x.Sp
	}
	return 0
}

func (x *SkillData) GetList() map[int32]*SkillInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *SkillData) GetCnt() int32 {
	if x != nil {
		return x.Cnt
	}
	return 0
}

func (x *SkillData) GetActiveAutoSkillId() int32 {
	if x != nil {
		return x.ActiveAutoSkillId
	}
	return 0
}

func (x *SkillData) GetAutoSkillId() []int32 {
	if x != nil {
		return x.AutoSkillId
	}
	return nil
}

// 技能信息
type SkillInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`               //技能id
	BaseLevel int32 `protobuf:"varint,2,opt,name=baseLevel,proto3" json:"baseLevel,omitempty"` //技能基础等级
	AddLevel  int32 `protobuf:"varint,3,opt,name=addLevel,proto3" json:"addLevel,omitempty"`   //技能增加等级
	IsLearn   bool  `protobuf:"varint,4,opt,name=isLearn,proto3" json:"isLearn,omitempty"`     //是不是学习而来
}

func (x *SkillInfo) Reset() {
	*x = SkillInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbBase_struct_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SkillInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SkillInfo) ProtoMessage() {}

func (x *SkillInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pbBase_struct_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SkillInfo.ProtoReflect.Descriptor instead.
func (*SkillInfo) Descriptor() ([]byte, []int) {
	return file_pbBase_struct_proto_rawDescGZIP(), []int{18}
}

func (x *SkillInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SkillInfo) GetBaseLevel() int32 {
	if x != nil {
		return x.BaseLevel
	}
	return 0
}

func (x *SkillInfo) GetAddLevel() int32 {
	if x != nil {
		return x.AddLevel
	}
	return 0
}

func (x *SkillInfo) GetIsLearn() bool {
	if x != nil {
		return x.IsLearn
	}
	return false
}

// 使用物品响应-宠物潜能石
type ItemUseResultPetAddSkillItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Old []int32      `protobuf:"varint,1,rep,packed,name=old,proto3" json:"old,omitempty"` //原本拥有的潜能技能id
	New []*SkillInfo `protobuf:"bytes,2,rep,name=new,proto3" json:"new,omitempty"`         //新的潜能技能
}

func (x *ItemUseResultPetAddSkillItem) Reset() {
	*x = ItemUseResultPetAddSkillItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbBase_struct_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ItemUseResultPetAddSkillItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItemUseResultPetAddSkillItem) ProtoMessage() {}

func (x *ItemUseResultPetAddSkillItem) ProtoReflect() protoreflect.Message {
	mi := &file_pbBase_struct_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItemUseResultPetAddSkillItem.ProtoReflect.Descriptor instead.
func (*ItemUseResultPetAddSkillItem) Descriptor() ([]byte, []int) {
	return file_pbBase_struct_proto_rawDescGZIP(), []int{19}
}

func (x *ItemUseResultPetAddSkillItem) GetOld() []int32 {
	if x != nil {
		return x.Old
	}
	return nil
}

func (x *ItemUseResultPetAddSkillItem) GetNew() []*SkillInfo {
	if x != nil {
		return x.New
	}
	return nil
}

// 战斗出招数据结构体
type BattlePlanObj struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type     BattleDefine.Plan `protobuf:"varint,1,opt,name=type,proto3,enum=proto.BattleDefine.Plan" json:"type,omitempty"` //出招类型
	Position int32             `protobuf:"varint,2,opt,name=position,proto3" json:"position,omitempty"`                      //目标位置
	Extra    int32             `protobuf:"varint,3,opt,name=extra,proto3" json:"extra,omitempty"`                            //如果是使用技能,技能id;如果是使用物品,物品id
}

func (x *BattlePlanObj) Reset() {
	*x = BattlePlanObj{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbBase_struct_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BattlePlanObj) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattlePlanObj) ProtoMessage() {}

func (x *BattlePlanObj) ProtoReflect() protoreflect.Message {
	mi := &file_pbBase_struct_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattlePlanObj.ProtoReflect.Descriptor instead.
func (*BattlePlanObj) Descriptor() ([]byte, []int) {
	return file_pbBase_struct_proto_rawDescGZIP(), []int{20}
}

func (x *BattlePlanObj) GetType() BattleDefine.Plan {
	if x != nil {
		return x.Type
	}
	return BattleDefine.Plan(0)
}

func (x *BattlePlanObj) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *BattlePlanObj) GetExtra() int32 {
	if x != nil {
		return x.Extra
	}
	return 0
}

// 邮件简单信息
type MailSimpleNumInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type   MailDefine.MAIL_TYPE `protobuf:"varint,1,opt,name=type,proto3,enum=proto.MailDefine.MAIL_TYPE" json:"type,omitempty"` //类型
	Unread int32                `protobuf:"varint,2,opt,name=unread,proto3" json:"unread,omitempty"`                             //未读
	Read   int32                `protobuf:"varint,3,opt,name=read,proto3" json:"read,omitempty"`                                 //已读
}

func (x *MailSimpleNumInfo) Reset() {
	*x = MailSimpleNumInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbBase_struct_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MailSimpleNumInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MailSimpleNumInfo) ProtoMessage() {}

func (x *MailSimpleNumInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pbBase_struct_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MailSimpleNumInfo.ProtoReflect.Descriptor instead.
func (*MailSimpleNumInfo) Descriptor() ([]byte, []int) {
	return file_pbBase_struct_proto_rawDescGZIP(), []int{21}
}

func (x *MailSimpleNumInfo) GetType() MailDefine.MAIL_TYPE {
	if x != nil {
		return x.Type
	}
	return MailDefine.MAIL_TYPE(0)
}

func (x *MailSimpleNumInfo) GetUnread() int32 {
	if x != nil {
		return x.Unread
	}
	return 0
}

func (x *MailSimpleNumInfo) GetRead() int32 {
	if x != nil {
		return x.Read
	}
	return 0
}

// 邮件数据
type Mail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                           //邮件id
	Receiver     int32                  `protobuf:"varint,2,opt,name=receiver,proto3" json:"receiver,omitempty"`                               //接收者id
	Title        string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`                                      //标题
	Content      string                 `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`                                  //内容
	ReceiverName string                 `protobuf:"bytes,5,opt,name=receiverName,proto3" json:"receiverName,omitempty"`                        //接收者名称
	SenderName   string                 `protobuf:"bytes,6,opt,name=senderName,proto3" json:"senderName,omitempty"`                            //发送者名称
	Sender       int32                  `protobuf:"varint,7,opt,name=sender,proto3" json:"sender,omitempty"`                                   //发送者id
	Type         MailDefine.MAIL_TYPE   `protobuf:"varint,8,opt,name=type,proto3,enum=proto.MailDefine.MAIL_TYPE" json:"type,omitempty"`       //类型
	Status       MailDefine.MAIL_STATUS `protobuf:"varint,9,opt,name=status,proto3,enum=proto.MailDefine.MAIL_STATUS" json:"status,omitempty"` //状态
	Appendix     []*ItemData            `protobuf:"bytes,10,rep,name=appendix,proto3" json:"appendix,omitempty"`                               //附件
	SelectItem   []*ItemData            `protobuf:"bytes,11,rep,name=selectItem,proto3" json:"selectItem,omitempty"`                           //可选列表
	ExpireTime   int64                  `protobuf:"varint,12,opt,name=expireTime,proto3" json:"expireTime,omitempty"`                          //过期时间
	ReqMoney1    int32                  `protobuf:"varint,13,opt,name=reqMoney1,proto3" json:"reqMoney1,omitempty"`                            //索要黄金
	ReqMoney3    int32                  `protobuf:"varint,14,opt,name=reqMoney3,proto3" json:"reqMoney3,omitempty"`                            //索要铜币
	Money1       int32                  `protobuf:"varint,15,opt,name=money1,proto3" json:"money1,omitempty"`                                  //赠送黄金
	Money3       int32                  `protobuf:"varint,16,opt,name=money3,proto3" json:"money3,omitempty"`                                  //赠送铜币
}

func (x *Mail) Reset() {
	*x = Mail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbBase_struct_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Mail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Mail) ProtoMessage() {}

func (x *Mail) ProtoReflect() protoreflect.Message {
	mi := &file_pbBase_struct_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Mail.ProtoReflect.Descriptor instead.
func (*Mail) Descriptor() ([]byte, []int) {
	return file_pbBase_struct_proto_rawDescGZIP(), []int{22}
}

func (x *Mail) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Mail) GetReceiver() int32 {
	if x != nil {
		return x.Receiver
	}
	return 0
}

func (x *Mail) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Mail) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *Mail) GetReceiverName() string {
	if x != nil {
		return x.ReceiverName
	}
	return ""
}

func (x *Mail) GetSenderName() string {
	if x != nil {
		return x.SenderName
	}
	return ""
}

func (x *Mail) GetSender() int32 {
	if x != nil {
		return x.Sender
	}
	return 0
}

func (x *Mail) GetType() MailDefine.MAIL_TYPE {
	if x != nil {
		return x.Type
	}
	return MailDefine.MAIL_TYPE(0)
}

func (x *Mail) GetStatus() MailDefine.MAIL_STATUS {
	if x != nil {
		return x.Status
	}
	return MailDefine.MAIL_STATUS(0)
}

func (x *Mail) GetAppendix() []*ItemData {
	if x != nil {
		return x.Appendix
	}
	return nil
}

func (x *Mail) GetSelectItem() []*ItemData {
	if x != nil {
		return x.SelectItem
	}
	return nil
}

func (x *Mail) GetExpireTime() int64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

func (x *Mail) GetReqMoney1() int32 {
	if x != nil {
		return x.ReqMoney1
	}
	return 0
}

func (x *Mail) GetReqMoney3() int32 {
	if x != nil {
		return x.ReqMoney3
	}
	return 0
}

func (x *Mail) GetMoney1() int32 {
	if x != nil {
		return x.Money1
	}
	return 0
}

func (x *Mail) GetMoney3() int32 {
	if x != nil {
		return x.Money3
	}
	return 0
}

var File_pbBase_struct_proto protoreflect.FileDescriptor

var file_pbBase_struct_proto_rawDesc = []byte{
	0x0a, 0x13, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x70, 0x62,
	0x42, 0x61, 0x73, 0x65, 0x2f, 0x41, 0x72, 0x65, 0x61, 0x4c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x2f, 0x41, 0x72, 0x65, 0x61, 0x4c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x43,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x2f, 0x43, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1e, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x4d, 0x79, 0x44, 0x65, 0x66, 0x69, 0x6e,
	0x65, 0x2f, 0x4d, 0x79, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x26, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x44,
	0x65, 0x66, 0x69, 0x6e, 0x65, 0x2f, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x69,
	0x6e, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65,
	0x2f, 0x4d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x2f, 0x4d, 0x61, 0x69, 0x6c,
	0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x33, 0x0a, 0x0b,
	0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x22, 0x9b, 0x01, 0x0a, 0x08, 0x41, 0x72, 0x65, 0x61, 0x4c, 0x69, 0x6e, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2f,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x72, 0x65, 0x61, 0x4c, 0x69, 0x6e, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22,
	0xfa, 0x03, 0x0a, 0x11, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x61, 0x6d,
	0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x12, 0x0c, 0x0a, 0x01, 0x78,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x63, 0x6f, 0x6e, 0x31,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x69, 0x63, 0x6f, 0x6e, 0x31, 0x12, 0x14, 0x0a,
	0x05, 0x69, 0x63, 0x6f, 0x6e, 0x32, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x69, 0x63,
	0x6f, 0x6e, 0x32, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x63, 0x6f, 0x6e, 0x33, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x69, 0x63, 0x6f, 0x6e, 0x33, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12,
	0x16, 0x0a, 0x06, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x32, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x32, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x6d,
	0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x68, 0x6f, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x68, 0x6f, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x69, 0x70, 0x4c, 0x76, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x76, 0x69, 0x70, 0x4c, 0x76, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x69, 0x70, 0x4c, 0x76,
	0x4d, 0x61, 0x78, 0x18, 0x14, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x76, 0x69, 0x70, 0x4c, 0x76,
	0x4d, 0x61, 0x78, 0x12, 0x27, 0x0a, 0x03, 0x70, 0x65, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x53, 0x69,
	0x6d, 0x70, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x52, 0x03, 0x70, 0x65, 0x74, 0x22, 0x5c, 0x0a, 0x0e,
	0x43, 0x72, 0x6f, 0x73, 0x73, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x63, 0x66, 0x67, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63,
	0x66, 0x67, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x67, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x61, 0x67, 0x65, 0x22, 0xcf, 0x01, 0x0a, 0x0b, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x65, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x65, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x06, 0x70, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x22, 0xed, 0x01, 0x0a,
	0x10, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x01, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x04, 0x61, 0x74, 0x74, 0x72, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x74,
	0x74, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x61, 0x74, 0x74, 0x72, 0x12, 0x24, 0x0a, 0x0d,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0d, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x38, 0x0a, 0x0b, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x73,
	0x6b, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x0b, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x22, 0xfc, 0x02, 0x0a,
	0x0a, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6d,
	0x61, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6d, 0x61, 0x70, 0x49,
	0x64, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x78, 0x12,
	0x0c, 0x0a, 0x01, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x79, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x6d,
	0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x12,
	0x23, 0x0a, 0x04, 0x61, 0x74, 0x74, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04,
	0x61, 0x74, 0x74, 0x72, 0x12, 0x20, 0x0a, 0x03, 0x62, 0x61, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x61, 0x67, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x03, 0x62, 0x61, 0x67, 0x12, 0x23, 0x0a, 0x04, 0x74, 0x61, 0x73, 0x6b, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x61, 0x73,
	0x6b, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x74, 0x61, 0x73, 0x6b, 0x12, 0x20, 0x0a, 0x0b, 0x69,
	0x74, 0x65, 0x6d, 0x53, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x05,
	0x52, 0x0b, 0x69, 0x74, 0x65, 0x6d, 0x53, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x26, 0x0a,
	0x05, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05,
	0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x75,
	0x6e, 0x72, 0x65, 0x61, 0x64, 0x4d, 0x61, 0x69, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0a, 0x75, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x4d, 0x61, 0x69, 0x6c, 0x22, 0xf4, 0x02, 0x0a, 0x08,
	0x41, 0x74, 0x74, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x63, 0x6f, 0x6e,
	0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x69, 0x63, 0x6f, 0x6e, 0x31, 0x12, 0x14,
	0x0a, 0x05, 0x69, 0x63, 0x6f, 0x6e, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x69,
	0x63, 0x6f, 0x6e, 0x32, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x63, 0x6f, 0x6e, 0x33, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x69, 0x63, 0x6f, 0x6e, 0x33, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x32, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x32,
	0x12, 0x10, 0x0a, 0x03, 0x65, 0x78, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x65,
	0x78, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x65, 0x78, 0x70, 0x32, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x65, 0x78, 0x70, 0x32, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x69, 0x70, 0x4c, 0x76, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x69, 0x70, 0x4c, 0x76, 0x12, 0x1a, 0x0a, 0x08,
	0x76, 0x69, 0x70, 0x4c, 0x76, 0x4d, 0x61, 0x78, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x76, 0x69, 0x70, 0x4c, 0x76, 0x4d, 0x61, 0x78, 0x12, 0x0e, 0x0a, 0x02, 0x63, 0x70, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x63, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x74, 0x72, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x73, 0x74, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x67,
	0x69, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x61, 0x67, 0x69, 0x12, 0x10, 0x0a, 0x03,
	0x63, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x63, 0x6f, 0x6e, 0x12, 0x10,
	0x0a, 0x03, 0x69, 0x6c, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x69, 0x6c, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x77, 0x69, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x77,
	0x69, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x68, 0x70, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02,
	0x68, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x6d, 0x70, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02,
	0x6d, 0x70, 0x22, 0x8d, 0x02, 0x0a, 0x07, 0x42, 0x61, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x16,
	0x0a, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x31, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x32,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x32, 0x12, 0x16,
	0x0a, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x33, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x33, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x67, 0x53, 0x69, 0x7a,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x62, 0x61, 0x67, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x24, 0x0a, 0x0d, 0x73, 0x65, 0x6c, 0x66, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x73, 0x65, 0x6c, 0x66, 0x53, 0x74, 0x6f,
	0x72, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x2f, 0x0a, 0x05, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x61,
	0x67, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x05, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x1a, 0x49, 0x0a, 0x0a, 0x53, 0x74, 0x6f, 0x72, 0x65,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x25, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49,
	0x74, 0x65, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x84, 0x02, 0x0a, 0x07, 0x50, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14,
	0x0a, 0x05, 0x63, 0x66, 0x67, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63,
	0x66, 0x67, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x67, 0x72, 0x6f, 0x77,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x67, 0x72, 0x6f, 0x77, 0x12, 0x14, 0x0a, 0x05,
	0x6c, 0x65, 0x61, 0x72, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x61,
	0x72, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x72, 0x6f, 0x77, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x77, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x12, 0x23, 0x0a, 0x04, 0x61, 0x74, 0x74, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x04, 0x61, 0x74, 0x74, 0x72, 0x12, 0x26, 0x0a, 0x05, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x6b, 0x69,
	0x6c, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x12, 0x10, 0x0a,
	0x03, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x61, 0x67, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x67, 0x72, 0x6f, 0x77, 0x45, 0x78, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x67, 0x72, 0x6f, 0x77, 0x45, 0x78, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0xd8, 0x07, 0x0a, 0x08, 0x49, 0x74,
	0x65, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x6c, 0x6f, 0x74, 0x50, 0x6f,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x73, 0x6c, 0x6f, 0x74, 0x50, 0x6f, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x0a, 0x06, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x31, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6f, 0x77,
	0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x31, 0x12, 0x28,
	0x0a, 0x06, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x32, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x06, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x32, 0x12, 0x28, 0x0a, 0x06, 0x70, 0x6f, 0x77, 0x65,
	0x72, 0x33, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x70, 0x6f, 0x77, 0x65,
	0x72, 0x33, 0x12, 0x30, 0x0a, 0x0a, 0x62, 0x69, 0x6e, 0x64, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x31,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50,
	0x6f, 0x77, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0a, 0x62, 0x69, 0x6e, 0x64, 0x50, 0x6f,
	0x77, 0x65, 0x72, 0x31, 0x12, 0x30, 0x0a, 0x0a, 0x62, 0x69, 0x6e, 0x64, 0x50, 0x6f, 0x77, 0x65,
	0x72, 0x32, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0a, 0x62, 0x69, 0x6e, 0x64,
	0x50, 0x6f, 0x77, 0x65, 0x72, 0x32, 0x12, 0x28, 0x0a, 0x06, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x34,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50,
	0x6f, 0x77, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x34,
	0x12, 0x28, 0x0a, 0x06, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x35, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x06, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x35, 0x12, 0x28, 0x0a, 0x06, 0x70, 0x6f,
	0x77, 0x65, 0x72, 0x36, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x70, 0x6f,
	0x77, 0x65, 0x72, 0x36, 0x12, 0x28, 0x0a, 0x06, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x37, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6f, 0x77,
	0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x37, 0x12, 0x36,
	0x0a, 0x0d, 0x65, 0x6e, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x31, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6f,
	0x77, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0d, 0x65, 0x6e, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x50, 0x6f, 0x77, 0x65, 0x72, 0x31, 0x12, 0x36, 0x0a, 0x0d, 0x65, 0x6e, 0x63, 0x68, 0x61, 0x6e,
	0x74, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x32, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x0d, 0x65, 0x6e, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x32, 0x12, 0x1e,
	0x0a, 0x0a, 0x64, 0x75, 0x72, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x64, 0x75, 0x72, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x1e,
	0x0a, 0x0a, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x44, 0x6f, 0x6e, 0x65, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x44, 0x6f, 0x6e, 0x65, 0x12, 0x32,
	0x0a, 0x0b, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6f, 0x77, 0x65,
	0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0b, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x50, 0x6f, 0x77,
	0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x74, 0x61, 0x72, 0x18, 0x14, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x73, 0x74, 0x61, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64,
	0x65, 0x53, 0x74, 0x61, 0x72, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x75, 0x70, 0x67,
	0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x65, 0x74, 0x49,
	0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x28,
	0x0a, 0x07, 0x70, 0x65, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x07, 0x70, 0x65, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x30, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x18, 0x18, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x49, 0x74, 0x65, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x1a, 0x38, 0x0a, 0x0a, 0x45, 0x78,
	0x74, 0x72, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x72, 0x0a, 0x09, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x2d, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6e,
	0x75, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x22, 0x51, 0x0a, 0x08, 0x54, 0x61, 0x73, 0x6b,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x61, 0x73, 0x6b,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x22, 0x40, 0x0a, 0x08, 0x54,
	0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x04, 0x63, 0x6f, 0x6e, 0x64, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x63, 0x6f, 0x6e, 0x64, 0x22, 0x4c, 0x0a,
	0x09, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x29, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x4d, 0x79, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x11, 0x0a, 0x0f, 0x4f,
	0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x22, 0x23,
	0x0a, 0x05, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x01, 0x79, 0x22, 0xf8, 0x01, 0x0a, 0x09, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x73,
	0x70, 0x12, 0x2e, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x44, 0x61, 0x74,
	0x61, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03,
	0x63, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x11, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x41, 0x75, 0x74,
	0x6f, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x49,
	0x64, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x75, 0x74, 0x6f, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x49, 0x64,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0b, 0x61, 0x75, 0x74, 0x6f, 0x53, 0x6b, 0x69, 0x6c,
	0x6c, 0x49, 0x64, 0x1a, 0x49, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x26, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x6f,
	0x0a, 0x09, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x62,
	0x61, 0x73, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x62, 0x61, 0x73, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x64, 0x64,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x61, 0x64, 0x64,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x73, 0x4c, 0x65, 0x61, 0x72, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x4c, 0x65, 0x61, 0x72, 0x6e, 0x22,
	0x54, 0x0a, 0x1c, 0x49, 0x74, 0x65, 0x6d, 0x55, 0x73, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x50, 0x65, 0x74, 0x41, 0x64, 0x64, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0x10, 0x0a, 0x03, 0x6f, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x52, 0x03, 0x6f, 0x6c,
	0x64, 0x12, 0x22, 0x0a, 0x03, 0x6e, 0x65, 0x77, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x03, 0x6e, 0x65, 0x77, 0x22, 0x6f, 0x0a, 0x0d, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x50,
	0x6c, 0x61, 0x6e, 0x4f, 0x62, 0x6a, 0x12, 0x2c, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x61, 0x74,
	0x74, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x14, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x22, 0x70, 0x0a, 0x11, 0x4d, 0x61, 0x69, 0x6c, 0x53, 0x69,
	0x6d, 0x70, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2f, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x4d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x2e, 0x4d, 0x41, 0x49,
	0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x75, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x75, 0x6e,
	0x72, 0x65, 0x61, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x65, 0x61, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x72, 0x65, 0x61, 0x64, 0x22, 0x90, 0x04, 0x0a, 0x04, 0x4d, 0x61, 0x69,
	0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x22, 0x0a,
	0x0c, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x2f, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x4d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x2e, 0x4d, 0x41, 0x49, 0x4c, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x35, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x4d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x2e, 0x4d, 0x41,
	0x49, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x2b, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x78, 0x18, 0x0a, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x74, 0x65, 0x6d,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x61, 0x70, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x78, 0x12, 0x2f,
	0x0a, 0x0a, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x18, 0x0b, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x0a, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0x1e, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x72, 0x65, 0x71, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x31, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x72, 0x65, 0x71, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x31, 0x12, 0x1c, 0x0a,
	0x09, 0x72, 0x65, 0x71, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x33, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x72, 0x65, 0x71, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x33, 0x12, 0x16, 0x0a, 0x06, 0x6d,
	0x6f, 0x6e, 0x65, 0x79, 0x31, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6d, 0x6f, 0x6e,
	0x65, 0x79, 0x31, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x33, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x33, 0x42, 0x1c, 0x5a, 0x1a, 0x77,
	0x6f, 0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x42, 0x61,
	0x73, 0x65, 0x3b, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_pbBase_struct_proto_rawDescOnce sync.Once
	file_pbBase_struct_proto_rawDescData = file_pbBase_struct_proto_rawDesc
)

func file_pbBase_struct_proto_rawDescGZIP() []byte {
	file_pbBase_struct_proto_rawDescOnce.Do(func() {
		file_pbBase_struct_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbBase_struct_proto_rawDescData)
	})
	return file_pbBase_struct_proto_rawDescData
}

var file_pbBase_struct_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_pbBase_struct_proto_goTypes = []interface{}{
	(*LoginResult)(nil),                  // 0: proto.LoginResult
	(*AreaLine)(nil),                     // 1: proto.AreaLine
	(*CrossSimplePlayer)(nil),            // 2: proto.CrossSimplePlayer
	(*CrossSimplePet)(nil),               // 3: proto.CrossSimplePet
	(*PlayerEvent)(nil),                  // 4: proto.PlayerEvent
	(*SimplePlayerInfo)(nil),             // 5: proto.SimplePlayerInfo
	(*PlayerInfo)(nil),                   // 6: proto.PlayerInfo
	(*AttrData)(nil),                     // 7: proto.AttrData
	(*BagData)(nil),                      // 8: proto.BagData
	(*PetData)(nil),                      // 9: proto.PetData
	(*ItemData)(nil),                     // 10: proto.ItemData
	(*Condition)(nil),                    // 11: proto.Condition
	(*TaskData)(nil),                     // 12: proto.TaskData
	(*TaskInfo)(nil),                     // 13: proto.TaskInfo
	(*PowerData)(nil),                    // 14: proto.PowerData
	(*OfflineTaskData)(nil),              // 15: proto.OfflineTaskData
	(*Point)(nil),                        // 16: proto.Point
	(*SkillData)(nil),                    // 17: proto.SkillData
	(*SkillInfo)(nil),                    // 18: proto.SkillInfo
	(*ItemUseResultPetAddSkillItem)(nil), // 19: proto.ItemUseResultPetAddSkillItem
	(*BattlePlanObj)(nil),                // 20: proto.BattlePlanObj
	(*MailSimpleNumInfo)(nil),            // 21: proto.MailSimpleNumInfo
	(*Mail)(nil),                         // 22: proto.Mail
	nil,                                  // 23: proto.BagData.StoreEntry
	nil,                                  // 24: proto.ItemData.ExtraEntry
	nil,                                  // 25: proto.SkillData.ListEntry
	(AreaLineState.Type)(0),              // 26: proto.AreaLineState.Type
	(ConditionType.Type)(0),              // 27: proto.ConditionType.Type
	(MyDefine.POWER)(0),                  // 28: proto.MyDefine.POWER
	(BattleDefine.Plan)(0),               // 29: proto.BattleDefine.Plan
	(MailDefine.MAIL_TYPE)(0),            // 30: proto.MailDefine.MAIL_TYPE
	(MailDefine.MAIL_STATUS)(0),          // 31: proto.MailDefine.MAIL_STATUS
}
var file_pbBase_struct_proto_depIdxs = []int32{
	26, // 0: proto.AreaLine.state:type_name -> proto.AreaLineState.Type
	3,  // 1: proto.CrossSimplePlayer.pet:type_name -> proto.CrossSimplePet
	2,  // 2: proto.PlayerEvent.player:type_name -> proto.CrossSimplePlayer
	7,  // 3: proto.SimplePlayerInfo.attr:type_name -> proto.AttrData
	15, // 4: proto.SimplePlayerInfo.offlineTask:type_name -> proto.OfflineTaskData
	7,  // 5: proto.PlayerInfo.attr:type_name -> proto.AttrData
	8,  // 6: proto.PlayerInfo.bag:type_name -> proto.BagData
	12, // 7: proto.PlayerInfo.task:type_name -> proto.TaskData
	17, // 8: proto.PlayerInfo.skill:type_name -> proto.SkillData
	23, // 9: proto.BagData.store:type_name -> proto.BagData.StoreEntry
	7,  // 10: proto.PetData.attr:type_name -> proto.AttrData
	17, // 11: proto.PetData.skill:type_name -> proto.SkillData
	14, // 12: proto.ItemData.power1:type_name -> proto.PowerData
	14, // 13: proto.ItemData.power2:type_name -> proto.PowerData
	14, // 14: proto.ItemData.power3:type_name -> proto.PowerData
	14, // 15: proto.ItemData.bindPower1:type_name -> proto.PowerData
	14, // 16: proto.ItemData.bindPower2:type_name -> proto.PowerData
	14, // 17: proto.ItemData.power4:type_name -> proto.PowerData
	14, // 18: proto.ItemData.power5:type_name -> proto.PowerData
	14, // 19: proto.ItemData.power6:type_name -> proto.PowerData
	14, // 20: proto.ItemData.power7:type_name -> proto.PowerData
	14, // 21: proto.ItemData.enchantPower1:type_name -> proto.PowerData
	14, // 22: proto.ItemData.enchantPower2:type_name -> proto.PowerData
	14, // 23: proto.ItemData.attachPower:type_name -> proto.PowerData
	9,  // 24: proto.ItemData.petItem:type_name -> proto.PetData
	24, // 25: proto.ItemData.extra:type_name -> proto.ItemData.ExtraEntry
	27, // 26: proto.Condition.type:type_name -> proto.ConditionType.Type
	13, // 27: proto.TaskData.tasks:type_name -> proto.TaskInfo
	11, // 28: proto.TaskInfo.cond:type_name -> proto.Condition
	28, // 29: proto.PowerData.type:type_name -> proto.MyDefine.POWER
	25, // 30: proto.SkillData.list:type_name -> proto.SkillData.ListEntry
	18, // 31: proto.ItemUseResultPetAddSkillItem.new:type_name -> proto.SkillInfo
	29, // 32: proto.BattlePlanObj.type:type_name -> proto.BattleDefine.Plan
	30, // 33: proto.MailSimpleNumInfo.type:type_name -> proto.MailDefine.MAIL_TYPE
	30, // 34: proto.Mail.type:type_name -> proto.MailDefine.MAIL_TYPE
	31, // 35: proto.Mail.status:type_name -> proto.MailDefine.MAIL_STATUS
	10, // 36: proto.Mail.appendix:type_name -> proto.ItemData
	10, // 37: proto.Mail.selectItem:type_name -> proto.ItemData
	10, // 38: proto.BagData.StoreEntry.value:type_name -> proto.ItemData
	18, // 39: proto.SkillData.ListEntry.value:type_name -> proto.SkillInfo
	40, // [40:40] is the sub-list for method output_type
	40, // [40:40] is the sub-list for method input_type
	40, // [40:40] is the sub-list for extension type_name
	40, // [40:40] is the sub-list for extension extendee
	0,  // [0:40] is the sub-list for field type_name
}

func init() { file_pbBase_struct_proto_init() }
func file_pbBase_struct_proto_init() {
	if File_pbBase_struct_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbBase_struct_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbBase_struct_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AreaLine); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbBase_struct_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CrossSimplePlayer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbBase_struct_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CrossSimplePet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbBase_struct_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbBase_struct_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SimplePlayerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbBase_struct_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbBase_struct_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AttrData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbBase_struct_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BagData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbBase_struct_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbBase_struct_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ItemData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbBase_struct_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Condition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbBase_struct_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbBase_struct_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbBase_struct_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PowerData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbBase_struct_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OfflineTaskData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbBase_struct_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Point); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbBase_struct_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SkillData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbBase_struct_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SkillInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbBase_struct_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ItemUseResultPetAddSkillItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbBase_struct_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BattlePlanObj); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbBase_struct_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MailSimpleNumInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbBase_struct_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Mail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbBase_struct_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbBase_struct_proto_goTypes,
		DependencyIndexes: file_pbBase_struct_proto_depIdxs,
		MessageInfos:      file_pbBase_struct_proto_msgTypes,
	}.Build()
	File_pbBase_struct_proto = out.File
	file_pbBase_struct_proto_rawDesc = nil
	file_pbBase_struct_proto_goTypes = nil
	file_pbBase_struct_proto_depIdxs = nil
}
