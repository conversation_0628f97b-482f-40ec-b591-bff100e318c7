// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v6.30.1
// source: pbGame/mail.proto

package pbGame

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	pbBase "world/common/pbBase"
	MailDefine "world/common/pbBase/MailDefine"
	Response "world/common/pbBase/Response"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are messages.
type C2S_OnMailOpenMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_OnMailOpenMessage) Reset() {
	*x = C2S_OnMailOpenMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_mail_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_OnMailOpenMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_OnMailOpenMessage) ProtoMessage() {}

func (x *C2S_OnMailOpenMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_mail_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_OnMailOpenMessage.ProtoReflect.Descriptor instead.
func (*C2S_OnMailOpenMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_mail_proto_rawDescGZIP(), []int{0}
}

type S2C_OnMailOpenMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SimpleNumInfo []*pbBase.MailSimpleNumInfo `protobuf:"bytes,1,rep,name=simpleNumInfo,proto3" json:"simpleNumInfo,omitempty"` //
}

func (x *S2C_OnMailOpenMessage) Reset() {
	*x = S2C_OnMailOpenMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_mail_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_OnMailOpenMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_OnMailOpenMessage) ProtoMessage() {}

func (x *S2C_OnMailOpenMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_mail_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_OnMailOpenMessage.ProtoReflect.Descriptor instead.
func (*S2C_OnMailOpenMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_mail_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_OnMailOpenMessage) GetSimpleNumInfo() []*pbBase.MailSimpleNumInfo {
	if x != nil {
		return x.SimpleNumInfo
	}
	return nil
}

type C2S_SendMailMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Content   string             `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`      //内容
	Money1    int32              `protobuf:"varint,2,opt,name=money1,proto3" json:"money1,omitempty"`       //赠送黄金
	Money3    int32              `protobuf:"varint,3,opt,name=money3,proto3" json:"money3,omitempty"`       //赠送铜币
	ReqMoney1 int32              `protobuf:"varint,4,opt,name=reqMoney1,proto3" json:"reqMoney1,omitempty"` //索取黄金
	ReqMoney3 int32              `protobuf:"varint,5,opt,name=reqMoney3,proto3" json:"reqMoney3,omitempty"` //索取铜币
	Appendix  []*pbBase.ItemData `protobuf:"bytes,6,rep,name=appendix,proto3" json:"appendix,omitempty"`    //附件
	ToId      int32              `protobuf:"varint,7,opt,name=toId,proto3" json:"toId,omitempty"`           //接收者id
	ToName    string             `protobuf:"bytes,8,opt,name=toName,proto3" json:"toName,omitempty"`        //接收者名称
}

func (x *C2S_SendMailMessage) Reset() {
	*x = C2S_SendMailMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_mail_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_SendMailMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_SendMailMessage) ProtoMessage() {}

func (x *C2S_SendMailMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_mail_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_SendMailMessage.ProtoReflect.Descriptor instead.
func (*C2S_SendMailMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_mail_proto_rawDescGZIP(), []int{2}
}

func (x *C2S_SendMailMessage) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *C2S_SendMailMessage) GetMoney1() int32 {
	if x != nil {
		return x.Money1
	}
	return 0
}

func (x *C2S_SendMailMessage) GetMoney3() int32 {
	if x != nil {
		return x.Money3
	}
	return 0
}

func (x *C2S_SendMailMessage) GetReqMoney1() int32 {
	if x != nil {
		return x.ReqMoney1
	}
	return 0
}

func (x *C2S_SendMailMessage) GetReqMoney3() int32 {
	if x != nil {
		return x.ReqMoney3
	}
	return 0
}

func (x *C2S_SendMailMessage) GetAppendix() []*pbBase.ItemData {
	if x != nil {
		return x.Appendix
	}
	return nil
}

func (x *C2S_SendMailMessage) GetToId() int32 {
	if x != nil {
		return x.ToId
	}
	return 0
}

func (x *C2S_SendMailMessage) GetToName() string {
	if x != nil {
		return x.ToName
	}
	return ""
}

type S2C_SendMailMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
}

func (x *S2C_SendMailMessage) Reset() {
	*x = S2C_SendMailMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_mail_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_SendMailMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_SendMailMessage) ProtoMessage() {}

func (x *S2C_SendMailMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_mail_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_SendMailMessage.ProtoReflect.Descriptor instead.
func (*S2C_SendMailMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_mail_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_SendMailMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

type C2S_MailListMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type     MailDefine.MAIL_TYPE `protobuf:"varint,1,opt,name=type,proto3,enum=proto.MailDefine.MAIL_TYPE" json:"type,omitempty"` //类型
	PageSize int32                `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`                         //每页大小
	Page     int32                `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`                                 //页码
}

func (x *C2S_MailListMessage) Reset() {
	*x = C2S_MailListMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_mail_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_MailListMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_MailListMessage) ProtoMessage() {}

func (x *C2S_MailListMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_mail_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_MailListMessage.ProtoReflect.Descriptor instead.
func (*C2S_MailListMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_mail_proto_rawDescGZIP(), []int{4}
}

func (x *C2S_MailListMessage) GetType() MailDefine.MAIL_TYPE {
	if x != nil {
		return x.Type
	}
	return MailDefine.MAIL_TYPE(0)
}

func (x *C2S_MailListMessage) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *C2S_MailListMessage) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

type S2C_MailListMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code       Response.Code  `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
	MailList   []*pbBase.Mail `protobuf:"bytes,2,rep,name=mailList,proto3" json:"mailList,omitempty"`                   //邮件列表
	TotalCount int32          `protobuf:"varint,3,opt,name=totalCount,proto3" json:"totalCount,omitempty"`              //总页数
}

func (x *S2C_MailListMessage) Reset() {
	*x = S2C_MailListMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_mail_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_MailListMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_MailListMessage) ProtoMessage() {}

func (x *S2C_MailListMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_mail_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_MailListMessage.ProtoReflect.Descriptor instead.
func (*S2C_MailListMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_mail_proto_rawDescGZIP(), []int{5}
}

func (x *S2C_MailListMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

func (x *S2C_MailListMessage) GetMailList() []*pbBase.Mail {
	if x != nil {
		return x.MailList
	}
	return nil
}

func (x *S2C_MailListMessage) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type C2S_MailDetailMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64                `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                     //邮件id
	Type MailDefine.MAIL_TYPE `protobuf:"varint,2,opt,name=type,proto3,enum=proto.MailDefine.MAIL_TYPE" json:"type,omitempty"` //类型
}

func (x *C2S_MailDetailMessage) Reset() {
	*x = C2S_MailDetailMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_mail_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_MailDetailMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_MailDetailMessage) ProtoMessage() {}

func (x *C2S_MailDetailMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_mail_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_MailDetailMessage.ProtoReflect.Descriptor instead.
func (*C2S_MailDetailMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_mail_proto_rawDescGZIP(), []int{6}
}

func (x *C2S_MailDetailMessage) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *C2S_MailDetailMessage) GetType() MailDefine.MAIL_TYPE {
	if x != nil {
		return x.Type
	}
	return MailDefine.MAIL_TYPE(0)
}

type S2C_MailDetailMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
	Mail *pbBase.Mail  `protobuf:"bytes,2,opt,name=mail,proto3" json:"mail,omitempty"`                           //邮件详情
}

func (x *S2C_MailDetailMessage) Reset() {
	*x = S2C_MailDetailMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_mail_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_MailDetailMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_MailDetailMessage) ProtoMessage() {}

func (x *S2C_MailDetailMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_mail_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_MailDetailMessage.ProtoReflect.Descriptor instead.
func (*S2C_MailDetailMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_mail_proto_rawDescGZIP(), []int{7}
}

func (x *S2C_MailDetailMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

func (x *S2C_MailDetailMessage) GetMail() *pbBase.Mail {
	if x != nil {
		return x.Mail
	}
	return nil
}

type C2S_MailAttachMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64                `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                     //邮件id
	Type     MailDefine.MAIL_TYPE `protobuf:"varint,2,opt,name=type,proto3,enum=proto.MailDefine.MAIL_TYPE" json:"type,omitempty"` //类型
	Selected int32                `protobuf:"varint,3,opt,name=selected,proto3" json:"selected,omitempty"`                         //选择的附件id
}

func (x *C2S_MailAttachMessage) Reset() {
	*x = C2S_MailAttachMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_mail_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_MailAttachMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_MailAttachMessage) ProtoMessage() {}

func (x *C2S_MailAttachMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_mail_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_MailAttachMessage.ProtoReflect.Descriptor instead.
func (*C2S_MailAttachMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_mail_proto_rawDescGZIP(), []int{8}
}

func (x *C2S_MailAttachMessage) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *C2S_MailAttachMessage) GetType() MailDefine.MAIL_TYPE {
	if x != nil {
		return x.Type
	}
	return MailDefine.MAIL_TYPE(0)
}

func (x *C2S_MailAttachMessage) GetSelected() int32 {
	if x != nil {
		return x.Selected
	}
	return 0
}

type S2C_MailAttachMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    Response.Code      `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
	Money1  int32              `protobuf:"varint,2,opt,name=money1,proto3" json:"money1,omitempty"`                      //黄金变动
	Money3  int32              `protobuf:"varint,3,opt,name=money3,proto3" json:"money3,omitempty"`                      //铜币变动
	Rewards []*pbBase.ItemData `protobuf:"bytes,4,rep,name=rewards,proto3" json:"rewards,omitempty"`                     //奖励
}

func (x *S2C_MailAttachMessage) Reset() {
	*x = S2C_MailAttachMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_mail_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_MailAttachMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_MailAttachMessage) ProtoMessage() {}

func (x *S2C_MailAttachMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_mail_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_MailAttachMessage.ProtoReflect.Descriptor instead.
func (*S2C_MailAttachMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_mail_proto_rawDescGZIP(), []int{9}
}

func (x *S2C_MailAttachMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

func (x *S2C_MailAttachMessage) GetMoney1() int32 {
	if x != nil {
		return x.Money1
	}
	return 0
}

func (x *S2C_MailAttachMessage) GetMoney3() int32 {
	if x != nil {
		return x.Money3
	}
	return 0
}

func (x *S2C_MailAttachMessage) GetRewards() []*pbBase.ItemData {
	if x != nil {
		return x.Rewards
	}
	return nil
}

type C2S_MailBackMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //邮件id
}

func (x *C2S_MailBackMessage) Reset() {
	*x = C2S_MailBackMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_mail_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_MailBackMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_MailBackMessage) ProtoMessage() {}

func (x *C2S_MailBackMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_mail_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_MailBackMessage.ProtoReflect.Descriptor instead.
func (*C2S_MailBackMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_mail_proto_rawDescGZIP(), []int{10}
}

func (x *C2S_MailBackMessage) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type S2C_MailBackMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
}

func (x *S2C_MailBackMessage) Reset() {
	*x = S2C_MailBackMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_mail_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_MailBackMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_MailBackMessage) ProtoMessage() {}

func (x *S2C_MailBackMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_mail_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_MailBackMessage.ProtoReflect.Descriptor instead.
func (*S2C_MailBackMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_mail_proto_rawDescGZIP(), []int{11}
}

func (x *S2C_MailBackMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

type C2S_MailDeleteMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64                `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                     //邮件id
	Type MailDefine.MAIL_TYPE `protobuf:"varint,2,opt,name=type,proto3,enum=proto.MailDefine.MAIL_TYPE" json:"type,omitempty"` //类型
}

func (x *C2S_MailDeleteMessage) Reset() {
	*x = C2S_MailDeleteMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_mail_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_MailDeleteMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_MailDeleteMessage) ProtoMessage() {}

func (x *C2S_MailDeleteMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_mail_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_MailDeleteMessage.ProtoReflect.Descriptor instead.
func (*C2S_MailDeleteMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_mail_proto_rawDescGZIP(), []int{12}
}

func (x *C2S_MailDeleteMessage) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *C2S_MailDeleteMessage) GetType() MailDefine.MAIL_TYPE {
	if x != nil {
		return x.Type
	}
	return MailDefine.MAIL_TYPE(0)
}

type S2C_MailDeleteMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
}

func (x *S2C_MailDeleteMessage) Reset() {
	*x = S2C_MailDeleteMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_mail_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_MailDeleteMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_MailDeleteMessage) ProtoMessage() {}

func (x *S2C_MailDeleteMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_mail_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_MailDeleteMessage.ProtoReflect.Descriptor instead.
func (*S2C_MailDeleteMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_mail_proto_rawDescGZIP(), []int{13}
}

func (x *S2C_MailDeleteMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

type C2S_MailAttachAllMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type     MailDefine.MAIL_TYPE `protobuf:"varint,1,opt,name=type,proto3,enum=proto.MailDefine.MAIL_TYPE" json:"type,omitempty"` //类型
	Page     int32                `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`                                 //页码
	PageSize int32                `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`                         //每页大小
}

func (x *C2S_MailAttachAllMessage) Reset() {
	*x = C2S_MailAttachAllMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_mail_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_MailAttachAllMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_MailAttachAllMessage) ProtoMessage() {}

func (x *C2S_MailAttachAllMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_mail_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_MailAttachAllMessage.ProtoReflect.Descriptor instead.
func (*C2S_MailAttachAllMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_mail_proto_rawDescGZIP(), []int{14}
}

func (x *C2S_MailAttachAllMessage) GetType() MailDefine.MAIL_TYPE {
	if x != nil {
		return x.Type
	}
	return MailDefine.MAIL_TYPE(0)
}

func (x *C2S_MailAttachAllMessage) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *C2S_MailAttachAllMessage) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type S2C_MailAttachAllMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code     Response.Code                    `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"`                                                                                                       //响应码
	TotalCnt int32                            `protobuf:"varint,2,opt,name=totalCnt,proto3" json:"totalCnt,omitempty"`                                                                                                                        //操作邮件数量
	Money1   int32                            `protobuf:"varint,3,opt,name=money1,proto3" json:"money1,omitempty"`                                                                                                                            //黄金变动
	Money3   int32                            `protobuf:"varint,4,opt,name=money3,proto3" json:"money3,omitempty"`                                                                                                                            //铜币变动
	Rewards  []*pbBase.ItemData               `protobuf:"bytes,5,rep,name=rewards,proto3" json:"rewards,omitempty"`                                                                                                                           //奖励
	Status   map[int64]MailDefine.MAIL_STATUS `protobuf:"bytes,6,rep,name=status,proto3" json:"status,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3,enum=proto.MailDefine.MAIL_STATUS"` //状态
}

func (x *S2C_MailAttachAllMessage) Reset() {
	*x = S2C_MailAttachAllMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_mail_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_MailAttachAllMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_MailAttachAllMessage) ProtoMessage() {}

func (x *S2C_MailAttachAllMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_mail_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_MailAttachAllMessage.ProtoReflect.Descriptor instead.
func (*S2C_MailAttachAllMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_mail_proto_rawDescGZIP(), []int{15}
}

func (x *S2C_MailAttachAllMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

func (x *S2C_MailAttachAllMessage) GetTotalCnt() int32 {
	if x != nil {
		return x.TotalCnt
	}
	return 0
}

func (x *S2C_MailAttachAllMessage) GetMoney1() int32 {
	if x != nil {
		return x.Money1
	}
	return 0
}

func (x *S2C_MailAttachAllMessage) GetMoney3() int32 {
	if x != nil {
		return x.Money3
	}
	return 0
}

func (x *S2C_MailAttachAllMessage) GetRewards() []*pbBase.ItemData {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *S2C_MailAttachAllMessage) GetStatus() map[int64]MailDefine.MAIL_STATUS {
	if x != nil {
		return x.Status
	}
	return nil
}

var File_pbGame_mail_proto protoreflect.FileDescriptor

var file_pbGame_mail_proto_rawDesc = []byte{
	0x0a, 0x11, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x2f, 0x6d, 0x61, 0x69, 0x6c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x70, 0x62, 0x42, 0x61,
	0x73, 0x65, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1e, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x22, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x4d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x69,
	0x6e, 0x65, 0x2f, 0x4d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x17, 0x0a, 0x15, 0x43, 0x32, 0x53, 0x5f, 0x4f, 0x6e, 0x4d, 0x61, 0x69,
	0x6c, 0x4f, 0x70, 0x65, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x57, 0x0a, 0x15,
	0x53, 0x32, 0x43, 0x5f, 0x4f, 0x6e, 0x4d, 0x61, 0x69, 0x6c, 0x4f, 0x70, 0x65, 0x6e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3e, 0x0a, 0x0d, 0x73, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x4e,
	0x75, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x61, 0x69, 0x6c, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x4e,
	0x75, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x73, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x4e, 0x75,
	0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xf4, 0x01, 0x0a, 0x13, 0x43, 0x32, 0x53, 0x5f, 0x53, 0x65,
	0x6e, 0x64, 0x4d, 0x61, 0x69, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79,
	0x31, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x31, 0x12,
	0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x33, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x33, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x71, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x31, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x72, 0x65, 0x71, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x31, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x71, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x33, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x72, 0x65, 0x71, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x33, 0x12, 0x2b, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x78, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x74,
	0x65, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x61, 0x70, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x78,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x6f, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x74, 0x6f, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x6f, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x6f, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x3f, 0x0a, 0x13,
	0x53, 0x32, 0x43, 0x5f, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x61, 0x69, 0x6c, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x76, 0x0a,
	0x13, 0x43, 0x32, 0x53, 0x5f, 0x4d, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x2f, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x61, 0x69, 0x6c, 0x44,
	0x65, 0x66, 0x69, 0x6e, 0x65, 0x2e, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x88, 0x01, 0x0a, 0x13, 0x53, 0x32, 0x43, 0x5f, 0x4d, 0x61,
	0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64,
	0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x27, 0x0a, 0x08, 0x6d, 0x61, 0x69, 0x6c, 0x4c,
	0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x4d, 0x61, 0x69, 0x6c, 0x52, 0x08, 0x6d, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0x58, 0x0a, 0x15, 0x43, 0x32, 0x53, 0x5f, 0x4d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2f, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x4d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x2e, 0x4d, 0x41, 0x49, 0x4c, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x62, 0x0a, 0x15, 0x53, 0x32,
	0x43, 0x5f, 0x4d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a,
	0x04, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x61, 0x69, 0x6c, 0x52, 0x04, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0x74,
	0x0a, 0x15, 0x43, 0x32, 0x53, 0x5f, 0x4d, 0x61, 0x69, 0x6c, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2f, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x61,
	0x69, 0x6c, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x2e, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x73, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x22, 0x9c, 0x01, 0x0a, 0x15, 0x53, 0x32, 0x43, 0x5f, 0x4d, 0x61, 0x69,
	0x6c, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f,
	0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x6e, 0x65,
	0x79, 0x31, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x31,
	0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x33, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x33, 0x12, 0x29, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x22, 0x25, 0x0a, 0x13, 0x43, 0x32, 0x53, 0x5f, 0x4d, 0x61, 0x69, 0x6c, 0x42,
	0x61, 0x63, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x3f, 0x0a, 0x13, 0x53, 0x32,
	0x43, 0x5f, 0x4d, 0x61, 0x69, 0x6c, 0x42, 0x61, 0x63, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x58, 0x0a, 0x15, 0x43,
	0x32, 0x53, 0x5f, 0x4d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x2f, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x61, 0x69, 0x6c, 0x44,
	0x65, 0x66, 0x69, 0x6e, 0x65, 0x2e, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x41, 0x0a, 0x15, 0x53, 0x32, 0x43, 0x5f, 0x4d, 0x61, 0x69,
	0x6c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f,
	0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x7b, 0x0a, 0x18, 0x43, 0x32, 0x53, 0x5f,
	0x4d, 0x61, 0x69, 0x6c, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x41, 0x6c, 0x6c, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x2f, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x61, 0x69, 0x6c, 0x44,
	0x65, 0x66, 0x69, 0x6e, 0x65, 0x2e, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xda, 0x02, 0x0a, 0x18, 0x53, 0x32, 0x43, 0x5f, 0x4d, 0x61,
	0x69, 0x6c, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x41, 0x6c, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x6e, 0x65,
	0x79, 0x31, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x31,
	0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x33, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x33, 0x12, 0x29, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x12, 0x43, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x32, 0x43, 0x5f,
	0x4d, 0x61, 0x69, 0x6c, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x41, 0x6c, 0x6c, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x1a, 0x58, 0x0a, 0x0b, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x33, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x4d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x2e, 0x4d, 0x41, 0x49, 0x4c,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x42, 0x1c, 0x5a, 0x1a, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x3b, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbGame_mail_proto_rawDescOnce sync.Once
	file_pbGame_mail_proto_rawDescData = file_pbGame_mail_proto_rawDesc
)

func file_pbGame_mail_proto_rawDescGZIP() []byte {
	file_pbGame_mail_proto_rawDescOnce.Do(func() {
		file_pbGame_mail_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbGame_mail_proto_rawDescData)
	})
	return file_pbGame_mail_proto_rawDescData
}

var file_pbGame_mail_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_pbGame_mail_proto_goTypes = []interface{}{
	(*C2S_OnMailOpenMessage)(nil),    // 0: proto.C2S_OnMailOpenMessage
	(*S2C_OnMailOpenMessage)(nil),    // 1: proto.S2C_OnMailOpenMessage
	(*C2S_SendMailMessage)(nil),      // 2: proto.C2S_SendMailMessage
	(*S2C_SendMailMessage)(nil),      // 3: proto.S2C_SendMailMessage
	(*C2S_MailListMessage)(nil),      // 4: proto.C2S_MailListMessage
	(*S2C_MailListMessage)(nil),      // 5: proto.S2C_MailListMessage
	(*C2S_MailDetailMessage)(nil),    // 6: proto.C2S_MailDetailMessage
	(*S2C_MailDetailMessage)(nil),    // 7: proto.S2C_MailDetailMessage
	(*C2S_MailAttachMessage)(nil),    // 8: proto.C2S_MailAttachMessage
	(*S2C_MailAttachMessage)(nil),    // 9: proto.S2C_MailAttachMessage
	(*C2S_MailBackMessage)(nil),      // 10: proto.C2S_MailBackMessage
	(*S2C_MailBackMessage)(nil),      // 11: proto.S2C_MailBackMessage
	(*C2S_MailDeleteMessage)(nil),    // 12: proto.C2S_MailDeleteMessage
	(*S2C_MailDeleteMessage)(nil),    // 13: proto.S2C_MailDeleteMessage
	(*C2S_MailAttachAllMessage)(nil), // 14: proto.C2S_MailAttachAllMessage
	(*S2C_MailAttachAllMessage)(nil), // 15: proto.S2C_MailAttachAllMessage
	nil,                              // 16: proto.S2C_MailAttachAllMessage.StatusEntry
	(*pbBase.MailSimpleNumInfo)(nil), // 17: proto.MailSimpleNumInfo
	(*pbBase.ItemData)(nil),          // 18: proto.ItemData
	(Response.Code)(0),               // 19: proto.Response.Code
	(MailDefine.MAIL_TYPE)(0),        // 20: proto.MailDefine.MAIL_TYPE
	(*pbBase.Mail)(nil),              // 21: proto.Mail
	(MailDefine.MAIL_STATUS)(0),      // 22: proto.MailDefine.MAIL_STATUS
}
var file_pbGame_mail_proto_depIdxs = []int32{
	17, // 0: proto.S2C_OnMailOpenMessage.simpleNumInfo:type_name -> proto.MailSimpleNumInfo
	18, // 1: proto.C2S_SendMailMessage.appendix:type_name -> proto.ItemData
	19, // 2: proto.S2C_SendMailMessage.code:type_name -> proto.Response.Code
	20, // 3: proto.C2S_MailListMessage.type:type_name -> proto.MailDefine.MAIL_TYPE
	19, // 4: proto.S2C_MailListMessage.code:type_name -> proto.Response.Code
	21, // 5: proto.S2C_MailListMessage.mailList:type_name -> proto.Mail
	20, // 6: proto.C2S_MailDetailMessage.type:type_name -> proto.MailDefine.MAIL_TYPE
	19, // 7: proto.S2C_MailDetailMessage.code:type_name -> proto.Response.Code
	21, // 8: proto.S2C_MailDetailMessage.mail:type_name -> proto.Mail
	20, // 9: proto.C2S_MailAttachMessage.type:type_name -> proto.MailDefine.MAIL_TYPE
	19, // 10: proto.S2C_MailAttachMessage.code:type_name -> proto.Response.Code
	18, // 11: proto.S2C_MailAttachMessage.rewards:type_name -> proto.ItemData
	19, // 12: proto.S2C_MailBackMessage.code:type_name -> proto.Response.Code
	20, // 13: proto.C2S_MailDeleteMessage.type:type_name -> proto.MailDefine.MAIL_TYPE
	19, // 14: proto.S2C_MailDeleteMessage.code:type_name -> proto.Response.Code
	20, // 15: proto.C2S_MailAttachAllMessage.type:type_name -> proto.MailDefine.MAIL_TYPE
	19, // 16: proto.S2C_MailAttachAllMessage.code:type_name -> proto.Response.Code
	18, // 17: proto.S2C_MailAttachAllMessage.rewards:type_name -> proto.ItemData
	16, // 18: proto.S2C_MailAttachAllMessage.status:type_name -> proto.S2C_MailAttachAllMessage.StatusEntry
	22, // 19: proto.S2C_MailAttachAllMessage.StatusEntry.value:type_name -> proto.MailDefine.MAIL_STATUS
	20, // [20:20] is the sub-list for method output_type
	20, // [20:20] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_pbGame_mail_proto_init() }
func file_pbGame_mail_proto_init() {
	if File_pbGame_mail_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbGame_mail_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_OnMailOpenMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_mail_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_OnMailOpenMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_mail_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_SendMailMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_mail_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_SendMailMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_mail_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_MailListMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_mail_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_MailListMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_mail_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_MailDetailMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_mail_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_MailDetailMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_mail_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_MailAttachMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_mail_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_MailAttachMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_mail_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_MailBackMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_mail_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_MailBackMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_mail_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_MailDeleteMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_mail_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_MailDeleteMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_mail_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_MailAttachAllMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_mail_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_MailAttachAllMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbGame_mail_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbGame_mail_proto_goTypes,
		DependencyIndexes: file_pbGame_mail_proto_depIdxs,
		MessageInfos:      file_pbGame_mail_proto_msgTypes,
	}.Build()
	File_pbGame_mail_proto = out.File
	file_pbGame_mail_proto_rawDesc = nil
	file_pbGame_mail_proto_goTypes = nil
	file_pbGame_mail_proto_depIdxs = nil
}
