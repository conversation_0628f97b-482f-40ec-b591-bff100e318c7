// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v6.30.1
// source: pbGame/task.proto

package pbGame

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	pbBase "world/common/pbBase"
	Response "world/common/pbBase/Response"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are messages.
type C2S_AcceptTaskMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NpcId  int32 `protobuf:"varint,1,opt,name=npcId,proto3" json:"npcId,omitempty"`   //npcId
	TaskId int32 `protobuf:"varint,2,opt,name=taskId,proto3" json:"taskId,omitempty"` //任务id
}

func (x *C2S_AcceptTaskMessage) Reset() {
	*x = C2S_AcceptTaskMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_task_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_AcceptTaskMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_AcceptTaskMessage) ProtoMessage() {}

func (x *C2S_AcceptTaskMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_task_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_AcceptTaskMessage.ProtoReflect.Descriptor instead.
func (*C2S_AcceptTaskMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_task_proto_rawDescGZIP(), []int{0}
}

func (x *C2S_AcceptTaskMessage) GetNpcId() int32 {
	if x != nil {
		return x.NpcId
	}
	return 0
}

func (x *C2S_AcceptTaskMessage) GetTaskId() int32 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

type S2C_AcceptTaskMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  Response.Code   `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //
	MyPet *pbBase.PetData `protobuf:"bytes,2,opt,name=myPet,proto3" json:"myPet,omitempty"`                         //宠物可能有更新
}

func (x *S2C_AcceptTaskMessage) Reset() {
	*x = S2C_AcceptTaskMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_task_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_AcceptTaskMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_AcceptTaskMessage) ProtoMessage() {}

func (x *S2C_AcceptTaskMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_task_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_AcceptTaskMessage.ProtoReflect.Descriptor instead.
func (*S2C_AcceptTaskMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_task_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_AcceptTaskMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

func (x *S2C_AcceptTaskMessage) GetMyPet() *pbBase.PetData {
	if x != nil {
		return x.MyPet
	}
	return nil
}

type C2S_SubmitTaskMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NpcId  int32 `protobuf:"varint,1,opt,name=npcId,proto3" json:"npcId,omitempty"`   //npcId
	TaskId int32 `protobuf:"varint,2,opt,name=taskId,proto3" json:"taskId,omitempty"` //任务id
	ItemId int32 `protobuf:"varint,3,opt,name=itemId,proto3" json:"itemId,omitempty"` //选择的物品id
}

func (x *C2S_SubmitTaskMessage) Reset() {
	*x = C2S_SubmitTaskMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_task_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_SubmitTaskMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_SubmitTaskMessage) ProtoMessage() {}

func (x *C2S_SubmitTaskMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_task_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_SubmitTaskMessage.ProtoReflect.Descriptor instead.
func (*C2S_SubmitTaskMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_task_proto_rawDescGZIP(), []int{2}
}

func (x *C2S_SubmitTaskMessage) GetNpcId() int32 {
	if x != nil {
		return x.NpcId
	}
	return 0
}

func (x *C2S_SubmitTaskMessage) GetTaskId() int32 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *C2S_SubmitTaskMessage) GetItemId() int32 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

type S2C_SubmitTaskMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  Response.Code   `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //
	MyPet *pbBase.PetData `protobuf:"bytes,2,opt,name=myPet,proto3" json:"myPet,omitempty"`                         //宠物可能有更新
}

func (x *S2C_SubmitTaskMessage) Reset() {
	*x = S2C_SubmitTaskMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_task_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_SubmitTaskMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_SubmitTaskMessage) ProtoMessage() {}

func (x *S2C_SubmitTaskMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_task_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_SubmitTaskMessage.ProtoReflect.Descriptor instead.
func (*S2C_SubmitTaskMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_task_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_SubmitTaskMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

func (x *S2C_SubmitTaskMessage) GetMyPet() *pbBase.PetData {
	if x != nil {
		return x.MyPet
	}
	return nil
}

type C2S_DeleteTaskMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId int32 `protobuf:"varint,1,opt,name=taskId,proto3" json:"taskId,omitempty"` //任务id
}

func (x *C2S_DeleteTaskMessage) Reset() {
	*x = C2S_DeleteTaskMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_task_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_DeleteTaskMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_DeleteTaskMessage) ProtoMessage() {}

func (x *C2S_DeleteTaskMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_task_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_DeleteTaskMessage.ProtoReflect.Descriptor instead.
func (*C2S_DeleteTaskMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_task_proto_rawDescGZIP(), []int{4}
}

func (x *C2S_DeleteTaskMessage) GetTaskId() int32 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

type S2C_DeleteTaskMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //
}

func (x *S2C_DeleteTaskMessage) Reset() {
	*x = S2C_DeleteTaskMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_task_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_DeleteTaskMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_DeleteTaskMessage) ProtoMessage() {}

func (x *S2C_DeleteTaskMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_task_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_DeleteTaskMessage.ProtoReflect.Descriptor instead.
func (*S2C_DeleteTaskMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_task_proto_rawDescGZIP(), []int{5}
}

func (x *S2C_DeleteTaskMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

var File_pbGame_task_proto protoreflect.FileDescriptor

var file_pbGame_task_proto_rawDesc = []byte{
	0x0a, 0x11, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x70, 0x62, 0x42, 0x61,
	0x73, 0x65, 0x2f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2f, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x70, 0x62, 0x42, 0x61,
	0x73, 0x65, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x45, 0x0a, 0x15, 0x43, 0x32, 0x53, 0x5f, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x54, 0x61, 0x73,
	0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x70, 0x63, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6e, 0x70, 0x63, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0x67, 0x0a, 0x15, 0x53, 0x32, 0x43, 0x5f, 0x41, 0x63,
	0x63, 0x65, 0x70, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43,
	0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x24, 0x0a, 0x05, 0x6d, 0x79, 0x50,
	0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x50, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05, 0x6d, 0x79, 0x50, 0x65, 0x74, 0x22,
	0x5d, 0x0a, 0x15, 0x43, 0x32, 0x53, 0x5f, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x54, 0x61, 0x73,
	0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x70, 0x63, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6e, 0x70, 0x63, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x22, 0x67,
	0x0a, 0x15, 0x53, 0x32, 0x43, 0x5f, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x54, 0x61, 0x73, 0x6b,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x24, 0x0a, 0x05, 0x6d, 0x79, 0x50, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x05, 0x6d, 0x79, 0x50, 0x65, 0x74, 0x22, 0x2f, 0x0a, 0x15, 0x43, 0x32, 0x53, 0x5f, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0x41, 0x0a, 0x15, 0x53, 0x32, 0x43, 0x5f,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x1c, 0x5a, 0x1a, 0x77,
	0x6f, 0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x47, 0x61,
	0x6d, 0x65, 0x3b, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_pbGame_task_proto_rawDescOnce sync.Once
	file_pbGame_task_proto_rawDescData = file_pbGame_task_proto_rawDesc
)

func file_pbGame_task_proto_rawDescGZIP() []byte {
	file_pbGame_task_proto_rawDescOnce.Do(func() {
		file_pbGame_task_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbGame_task_proto_rawDescData)
	})
	return file_pbGame_task_proto_rawDescData
}

var file_pbGame_task_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_pbGame_task_proto_goTypes = []interface{}{
	(*C2S_AcceptTaskMessage)(nil), // 0: proto.C2S_AcceptTaskMessage
	(*S2C_AcceptTaskMessage)(nil), // 1: proto.S2C_AcceptTaskMessage
	(*C2S_SubmitTaskMessage)(nil), // 2: proto.C2S_SubmitTaskMessage
	(*S2C_SubmitTaskMessage)(nil), // 3: proto.S2C_SubmitTaskMessage
	(*C2S_DeleteTaskMessage)(nil), // 4: proto.C2S_DeleteTaskMessage
	(*S2C_DeleteTaskMessage)(nil), // 5: proto.S2C_DeleteTaskMessage
	(Response.Code)(0),            // 6: proto.Response.Code
	(*pbBase.PetData)(nil),        // 7: proto.PetData
}
var file_pbGame_task_proto_depIdxs = []int32{
	6, // 0: proto.S2C_AcceptTaskMessage.code:type_name -> proto.Response.Code
	7, // 1: proto.S2C_AcceptTaskMessage.myPet:type_name -> proto.PetData
	6, // 2: proto.S2C_SubmitTaskMessage.code:type_name -> proto.Response.Code
	7, // 3: proto.S2C_SubmitTaskMessage.myPet:type_name -> proto.PetData
	6, // 4: proto.S2C_DeleteTaskMessage.code:type_name -> proto.Response.Code
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_pbGame_task_proto_init() }
func file_pbGame_task_proto_init() {
	if File_pbGame_task_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbGame_task_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_AcceptTaskMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_task_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_AcceptTaskMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_task_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_SubmitTaskMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_task_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_SubmitTaskMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_task_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_DeleteTaskMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_task_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_DeleteTaskMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbGame_task_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbGame_task_proto_goTypes,
		DependencyIndexes: file_pbGame_task_proto_depIdxs,
		MessageInfos:      file_pbGame_task_proto_msgTypes,
	}.Build()
	File_pbGame_task_proto = out.File
	file_pbGame_task_proto_rawDesc = nil
	file_pbGame_task_proto_goTypes = nil
	file_pbGame_task_proto_depIdxs = nil
}
