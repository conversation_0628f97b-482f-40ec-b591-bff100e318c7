// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v6.30.1
// source: pbGame/notify.proto

package pbGame

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	pbBase "world/common/pbBase"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are messages.
type S2C_MapDataMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MapId   int32                       `protobuf:"varint,1,opt,name=mapId,proto3" json:"mapId,omitempty"`            //地图id
	Pos     *pbBase.Point               `protobuf:"bytes,2,opt,name=pos,proto3" json:"pos,omitempty"`                 //玩家位置
	NpcList []int32                     `protobuf:"varint,3,rep,packed,name=npcList,proto3" json:"npcList,omitempty"` //npc数据
	PlrList []*pbBase.CrossSimplePlayer `protobuf:"bytes,4,rep,name=plrList,proto3" json:"plrList,omitempty"`         //玩家数据
}

func (x *S2C_MapDataMessage) Reset() {
	*x = S2C_MapDataMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_notify_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_MapDataMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_MapDataMessage) ProtoMessage() {}

func (x *S2C_MapDataMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_notify_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_MapDataMessage.ProtoReflect.Descriptor instead.
func (*S2C_MapDataMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_notify_proto_rawDescGZIP(), []int{0}
}

func (x *S2C_MapDataMessage) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *S2C_MapDataMessage) GetPos() *pbBase.Point {
	if x != nil {
		return x.Pos
	}
	return nil
}

func (x *S2C_MapDataMessage) GetNpcList() []int32 {
	if x != nil {
		return x.NpcList
	}
	return nil
}

func (x *S2C_MapDataMessage) GetPlrList() []*pbBase.CrossSimplePlayer {
	if x != nil {
		return x.PlrList
	}
	return nil
}

type S2C_BagDataMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bag *pbBase.BagData `protobuf:"bytes,1,opt,name=bag,proto3" json:"bag,omitempty"` //数据列表，不包含仓库
}

func (x *S2C_BagDataMessage) Reset() {
	*x = S2C_BagDataMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_notify_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_BagDataMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_BagDataMessage) ProtoMessage() {}

func (x *S2C_BagDataMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_notify_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_BagDataMessage.ProtoReflect.Descriptor instead.
func (*S2C_BagDataMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_notify_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_BagDataMessage) GetBag() *pbBase.BagData {
	if x != nil {
		return x.Bag
	}
	return nil
}

type S2C_LogoutMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //0服务器踢除，1账号在别处登录
}

func (x *S2C_LogoutMessage) Reset() {
	*x = S2C_LogoutMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_notify_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_LogoutMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_LogoutMessage) ProtoMessage() {}

func (x *S2C_LogoutMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_notify_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_LogoutMessage.ProtoReflect.Descriptor instead.
func (*S2C_LogoutMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_notify_proto_rawDescGZIP(), []int{2}
}

func (x *S2C_LogoutMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type S2C_SendAwardMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ary []*pbBase.ItemData `protobuf:"bytes,1,rep,name=ary,proto3" json:"ary,omitempty"` //列表
}

func (x *S2C_SendAwardMessage) Reset() {
	*x = S2C_SendAwardMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_notify_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_SendAwardMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_SendAwardMessage) ProtoMessage() {}

func (x *S2C_SendAwardMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_notify_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_SendAwardMessage.ProtoReflect.Descriptor instead.
func (*S2C_SendAwardMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_notify_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_SendAwardMessage) GetAry() []*pbBase.ItemData {
	if x != nil {
		return x.Ary
	}
	return nil
}

type S2C_NewMailMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *S2C_NewMailMessage) Reset() {
	*x = S2C_NewMailMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_notify_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_NewMailMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_NewMailMessage) ProtoMessage() {}

func (x *S2C_NewMailMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_notify_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_NewMailMessage.ProtoReflect.Descriptor instead.
func (*S2C_NewMailMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_notify_proto_rawDescGZIP(), []int{4}
}

var File_pbGame_notify_proto protoreflect.FileDescriptor

var file_pbGame_notify_proto_rawDesc = []byte{
	0x0a, 0x13, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x70, 0x62,
	0x42, 0x61, 0x73, 0x65, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x98, 0x01, 0x0a, 0x12, 0x53, 0x32, 0x43, 0x5f, 0x4d, 0x61, 0x70, 0x44, 0x61, 0x74,
	0x61, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x61, 0x70, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x12, 0x1e,
	0x0a, 0x03, 0x70, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x03, 0x70, 0x6f, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x6e, 0x70, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x05, 0x52,
	0x07, 0x6e, 0x70, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x32, 0x0a, 0x07, 0x70, 0x6c, 0x72, 0x4c,
	0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x52, 0x07, 0x70, 0x6c, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x36, 0x0a, 0x12,
	0x53, 0x32, 0x43, 0x5f, 0x42, 0x61, 0x67, 0x44, 0x61, 0x74, 0x61, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x20, 0x0a, 0x03, 0x62, 0x61, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x61, 0x67, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x03, 0x62, 0x61, 0x67, 0x22, 0x27, 0x0a, 0x11, 0x53, 0x32, 0x43, 0x5f, 0x4c, 0x6f, 0x67, 0x6f,
	0x75, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x39, 0x0a,
	0x14, 0x53, 0x32, 0x43, 0x5f, 0x53, 0x65, 0x6e, 0x64, 0x41, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x21, 0x0a, 0x03, 0x61, 0x72, 0x79, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x03, 0x61, 0x72, 0x79, 0x22, 0x14, 0x0a, 0x12, 0x53, 0x32, 0x43, 0x5f,
	0x4e, 0x65, 0x77, 0x4d, 0x61, 0x69, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x42, 0x1c,
	0x5a, 0x1a, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70,
	0x62, 0x47, 0x61, 0x6d, 0x65, 0x3b, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbGame_notify_proto_rawDescOnce sync.Once
	file_pbGame_notify_proto_rawDescData = file_pbGame_notify_proto_rawDesc
)

func file_pbGame_notify_proto_rawDescGZIP() []byte {
	file_pbGame_notify_proto_rawDescOnce.Do(func() {
		file_pbGame_notify_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbGame_notify_proto_rawDescData)
	})
	return file_pbGame_notify_proto_rawDescData
}

var file_pbGame_notify_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_pbGame_notify_proto_goTypes = []interface{}{
	(*S2C_MapDataMessage)(nil),       // 0: proto.S2C_MapDataMessage
	(*S2C_BagDataMessage)(nil),       // 1: proto.S2C_BagDataMessage
	(*S2C_LogoutMessage)(nil),        // 2: proto.S2C_LogoutMessage
	(*S2C_SendAwardMessage)(nil),     // 3: proto.S2C_SendAwardMessage
	(*S2C_NewMailMessage)(nil),       // 4: proto.S2C_NewMailMessage
	(*pbBase.Point)(nil),             // 5: proto.Point
	(*pbBase.CrossSimplePlayer)(nil), // 6: proto.CrossSimplePlayer
	(*pbBase.BagData)(nil),           // 7: proto.BagData
	(*pbBase.ItemData)(nil),          // 8: proto.ItemData
}
var file_pbGame_notify_proto_depIdxs = []int32{
	5, // 0: proto.S2C_MapDataMessage.pos:type_name -> proto.Point
	6, // 1: proto.S2C_MapDataMessage.plrList:type_name -> proto.CrossSimplePlayer
	7, // 2: proto.S2C_BagDataMessage.bag:type_name -> proto.BagData
	8, // 3: proto.S2C_SendAwardMessage.ary:type_name -> proto.ItemData
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_pbGame_notify_proto_init() }
func file_pbGame_notify_proto_init() {
	if File_pbGame_notify_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbGame_notify_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_MapDataMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_notify_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_BagDataMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_notify_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_LogoutMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_notify_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_SendAwardMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_notify_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_NewMailMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbGame_notify_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbGame_notify_proto_goTypes,
		DependencyIndexes: file_pbGame_notify_proto_depIdxs,
		MessageInfos:      file_pbGame_notify_proto_msgTypes,
	}.Build()
	File_pbGame_notify_proto = out.File
	file_pbGame_notify_proto_rawDesc = nil
	file_pbGame_notify_proto_goTypes = nil
	file_pbGame_notify_proto_depIdxs = nil
}
