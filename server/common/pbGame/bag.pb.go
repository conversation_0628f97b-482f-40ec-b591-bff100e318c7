// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v6.30.1
// source: pbGame/bag.proto

package pbGame

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
	pbBase "world/common/pbBase"
	Bag "world/common/pbBase/Bag"
	BagReset "world/common/pbBase/BagReset"
	Response "world/common/pbBase/Response"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are messages.
type C2S_BagResetMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type BagReset.Type `protobuf:"varint,1,opt,name=type,proto3,enum=proto.BagReset.Type" json:"type,omitempty"` //整理类型
}

func (x *C2S_BagResetMessage) Reset() {
	*x = C2S_BagResetMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_bag_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_BagResetMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_BagResetMessage) ProtoMessage() {}

func (x *C2S_BagResetMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_bag_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_BagResetMessage.ProtoReflect.Descriptor instead.
func (*C2S_BagResetMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_bag_proto_rawDescGZIP(), []int{0}
}

func (x *C2S_BagResetMessage) GetType() BagReset.Type {
	if x != nil {
		return x.Type
	}
	return BagReset.Type(0)
}

type S2C_BagResetMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  Response.Code              `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"`                                                                  //响应码
	Store map[int32]*pbBase.ItemData `protobuf:"bytes,2,rep,name=store,proto3" json:"store,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //新的物品数据
}

func (x *S2C_BagResetMessage) Reset() {
	*x = S2C_BagResetMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_bag_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_BagResetMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_BagResetMessage) ProtoMessage() {}

func (x *S2C_BagResetMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_bag_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_BagResetMessage.ProtoReflect.Descriptor instead.
func (*S2C_BagResetMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_bag_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_BagResetMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

func (x *S2C_BagResetMessage) GetStore() map[int32]*pbBase.ItemData {
	if x != nil {
		return x.Store
	}
	return nil
}

type C2S_BagItemSellMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type int32              `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"` //操作类型,1出售,2丢弃
	List []*pbBase.ItemData `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`  //操作的物品数据列表
}

func (x *C2S_BagItemSellMessage) Reset() {
	*x = C2S_BagItemSellMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_bag_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_BagItemSellMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_BagItemSellMessage) ProtoMessage() {}

func (x *C2S_BagItemSellMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_bag_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_BagItemSellMessage.ProtoReflect.Descriptor instead.
func (*C2S_BagItemSellMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_bag_proto_rawDescGZIP(), []int{2}
}

func (x *C2S_BagItemSellMessage) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *C2S_BagItemSellMessage) GetList() []*pbBase.ItemData {
	if x != nil {
		return x.List
	}
	return nil
}

type S2C_BagItemSellMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //物品系列操作
}

func (x *S2C_BagItemSellMessage) Reset() {
	*x = S2C_BagItemSellMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_bag_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_BagItemSellMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_BagItemSellMessage) ProtoMessage() {}

func (x *S2C_BagItemSellMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_bag_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_BagItemSellMessage.ProtoReflect.Descriptor instead.
func (*S2C_BagItemSellMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_bag_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_BagItemSellMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

type C2S_EquipWearMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SlotPos int32 `protobuf:"varint,1,opt,name=slotPos,proto3" json:"slotPos,omitempty"` //物品位置
	ItemId  int32 `protobuf:"varint,2,opt,name=itemId,proto3" json:"itemId,omitempty"`   //物品id
}

func (x *C2S_EquipWearMessage) Reset() {
	*x = C2S_EquipWearMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_bag_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_EquipWearMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_EquipWearMessage) ProtoMessage() {}

func (x *C2S_EquipWearMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_bag_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_EquipWearMessage.ProtoReflect.Descriptor instead.
func (*C2S_EquipWearMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_bag_proto_rawDescGZIP(), []int{4}
}

func (x *C2S_EquipWearMessage) GetSlotPos() int32 {
	if x != nil {
		return x.SlotPos
	}
	return 0
}

func (x *C2S_EquipWearMessage) GetItemId() int32 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

type S2C_EquipWearMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //
}

func (x *S2C_EquipWearMessage) Reset() {
	*x = S2C_EquipWearMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_bag_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_EquipWearMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_EquipWearMessage) ProtoMessage() {}

func (x *S2C_EquipWearMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_bag_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_EquipWearMessage.ProtoReflect.Descriptor instead.
func (*S2C_EquipWearMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_bag_proto_rawDescGZIP(), []int{5}
}

func (x *S2C_EquipWearMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

type C2S_EquipTakeOffMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SlotPos int32 `protobuf:"varint,1,opt,name=slotPos,proto3" json:"slotPos,omitempty"` //物品位置
	ItemId  int32 `protobuf:"varint,2,opt,name=itemId,proto3" json:"itemId,omitempty"`   //物品id
}

func (x *C2S_EquipTakeOffMessage) Reset() {
	*x = C2S_EquipTakeOffMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_bag_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_EquipTakeOffMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_EquipTakeOffMessage) ProtoMessage() {}

func (x *C2S_EquipTakeOffMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_bag_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_EquipTakeOffMessage.ProtoReflect.Descriptor instead.
func (*C2S_EquipTakeOffMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_bag_proto_rawDescGZIP(), []int{6}
}

func (x *C2S_EquipTakeOffMessage) GetSlotPos() int32 {
	if x != nil {
		return x.SlotPos
	}
	return 0
}

func (x *C2S_EquipTakeOffMessage) GetItemId() int32 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

type S2C_EquipTakeOffMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //
}

func (x *S2C_EquipTakeOffMessage) Reset() {
	*x = S2C_EquipTakeOffMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_bag_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_EquipTakeOffMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_EquipTakeOffMessage) ProtoMessage() {}

func (x *S2C_EquipTakeOffMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_bag_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_EquipTakeOffMessage.ProtoReflect.Descriptor instead.
func (*S2C_EquipTakeOffMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_bag_proto_rawDescGZIP(), []int{7}
}

func (x *S2C_EquipTakeOffMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

type C2S_PlayerBagUseMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UseType     Bag.ItemUseType `protobuf:"varint,1,opt,name=useType,proto3,enum=proto.Bag.ItemUseType" json:"useType,omitempty"` //操作类型
	ItemSlotPos int32           `protobuf:"varint,2,opt,name=itemSlotPos,proto3" json:"itemSlotPos,omitempty"`                    //物品位置
	ItemId      int32           `protobuf:"varint,3,opt,name=itemId,proto3" json:"itemId,omitempty"`                              //物品id
	UseNum      int32           `protobuf:"varint,4,opt,name=useNum,proto3" json:"useNum,omitempty"`                              //使用数量
	ExtraId     int32           `protobuf:"varint,5,opt,name=extraId,proto3" json:"extraId,omitempty"`                            //额外参数
}

func (x *C2S_PlayerBagUseMessage) Reset() {
	*x = C2S_PlayerBagUseMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_bag_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_PlayerBagUseMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_PlayerBagUseMessage) ProtoMessage() {}

func (x *C2S_PlayerBagUseMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_bag_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_PlayerBagUseMessage.ProtoReflect.Descriptor instead.
func (*C2S_PlayerBagUseMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_bag_proto_rawDescGZIP(), []int{8}
}

func (x *C2S_PlayerBagUseMessage) GetUseType() Bag.ItemUseType {
	if x != nil {
		return x.UseType
	}
	return Bag.ItemUseType(0)
}

func (x *C2S_PlayerBagUseMessage) GetItemSlotPos() int32 {
	if x != nil {
		return x.ItemSlotPos
	}
	return 0
}

func (x *C2S_PlayerBagUseMessage) GetItemId() int32 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *C2S_PlayerBagUseMessage) GetUseNum() int32 {
	if x != nil {
		return x.UseNum
	}
	return 0
}

func (x *C2S_PlayerBagUseMessage) GetExtraId() int32 {
	if x != nil {
		return x.ExtraId
	}
	return 0
}

type S2C_PlayerBagUseMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code           Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //
	ResponseString string        `protobuf:"bytes,2,opt,name=responseString,proto3" json:"responseString,omitempty"`       //响应字符串
	Any            *anypb.Any    `protobuf:"bytes,3,opt,name=any,proto3" json:"any,omitempty"`                             //任意扩展数据 用于响应结果
}

func (x *S2C_PlayerBagUseMessage) Reset() {
	*x = S2C_PlayerBagUseMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_bag_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_PlayerBagUseMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_PlayerBagUseMessage) ProtoMessage() {}

func (x *S2C_PlayerBagUseMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_bag_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_PlayerBagUseMessage.ProtoReflect.Descriptor instead.
func (*S2C_PlayerBagUseMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_bag_proto_rawDescGZIP(), []int{9}
}

func (x *S2C_PlayerBagUseMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

func (x *S2C_PlayerBagUseMessage) GetResponseString() string {
	if x != nil {
		return x.ResponseString
	}
	return ""
}

func (x *S2C_PlayerBagUseMessage) GetAny() *anypb.Any {
	if x != nil {
		return x.Any
	}
	return nil
}

type C2S_BagItemBindMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Item *pbBase.ItemData `protobuf:"bytes,1,opt,name=item,proto3" json:"item,omitempty"` //物品数据
}

func (x *C2S_BagItemBindMessage) Reset() {
	*x = C2S_BagItemBindMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_bag_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_BagItemBindMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_BagItemBindMessage) ProtoMessage() {}

func (x *C2S_BagItemBindMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_bag_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_BagItemBindMessage.ProtoReflect.Descriptor instead.
func (*C2S_BagItemBindMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_bag_proto_rawDescGZIP(), []int{10}
}

func (x *C2S_BagItemBindMessage) GetItem() *pbBase.ItemData {
	if x != nil {
		return x.Item
	}
	return nil
}

type S2C_BagItemBindMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
}

func (x *S2C_BagItemBindMessage) Reset() {
	*x = S2C_BagItemBindMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_bag_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_BagItemBindMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_BagItemBindMessage) ProtoMessage() {}

func (x *S2C_BagItemBindMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_bag_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_BagItemBindMessage.ProtoReflect.Descriptor instead.
func (*S2C_BagItemBindMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_bag_proto_rawDescGZIP(), []int{11}
}

func (x *S2C_BagItemBindMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

type C2S_BagItemStarMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Item      *pbBase.ItemData `protobuf:"bytes,1,opt,name=item,proto3" json:"item,omitempty"`            //物品数据
	IsUpgrade bool             `protobuf:"varint,2,opt,name=isUpgrade,proto3" json:"isUpgrade,omitempty"` //是否是进阶升星
}

func (x *C2S_BagItemStarMessage) Reset() {
	*x = C2S_BagItemStarMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_bag_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_BagItemStarMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_BagItemStarMessage) ProtoMessage() {}

func (x *C2S_BagItemStarMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_bag_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_BagItemStarMessage.ProtoReflect.Descriptor instead.
func (*C2S_BagItemStarMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_bag_proto_rawDescGZIP(), []int{12}
}

func (x *C2S_BagItemStarMessage) GetItem() *pbBase.ItemData {
	if x != nil {
		return x.Item
	}
	return nil
}

func (x *C2S_BagItemStarMessage) GetIsUpgrade() bool {
	if x != nil {
		return x.IsUpgrade
	}
	return false
}

type S2C_BagItemStarMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code   Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
	Result bool          `protobuf:"varint,2,opt,name=result,proto3" json:"result,omitempty"`                      //是否成功
}

func (x *S2C_BagItemStarMessage) Reset() {
	*x = S2C_BagItemStarMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_bag_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_BagItemStarMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_BagItemStarMessage) ProtoMessage() {}

func (x *S2C_BagItemStarMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_bag_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_BagItemStarMessage.ProtoReflect.Descriptor instead.
func (*S2C_BagItemStarMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_bag_proto_rawDescGZIP(), []int{13}
}

func (x *S2C_BagItemStarMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

func (x *S2C_BagItemStarMessage) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

type C2S_BagItemIdentifyMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Item      *pbBase.ItemData `protobuf:"bytes,1,opt,name=item,proto3" json:"item,omitempty"`            //物品数据
	IsUpgrade bool             `protobuf:"varint,2,opt,name=isUpgrade,proto3" json:"isUpgrade,omitempty"` //是否是进阶鉴定
}

func (x *C2S_BagItemIdentifyMessage) Reset() {
	*x = C2S_BagItemIdentifyMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_bag_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_BagItemIdentifyMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_BagItemIdentifyMessage) ProtoMessage() {}

func (x *C2S_BagItemIdentifyMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_bag_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_BagItemIdentifyMessage.ProtoReflect.Descriptor instead.
func (*C2S_BagItemIdentifyMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_bag_proto_rawDescGZIP(), []int{14}
}

func (x *C2S_BagItemIdentifyMessage) GetItem() *pbBase.ItemData {
	if x != nil {
		return x.Item
	}
	return nil
}

func (x *C2S_BagItemIdentifyMessage) GetIsUpgrade() bool {
	if x != nil {
		return x.IsUpgrade
	}
	return false
}

type S2C_BagItemIdentifyMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code    `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
	Item *pbBase.ItemData `protobuf:"bytes,2,opt,name=item,proto3" json:"item,omitempty"`                           //物品数据
}

func (x *S2C_BagItemIdentifyMessage) Reset() {
	*x = S2C_BagItemIdentifyMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_bag_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_BagItemIdentifyMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_BagItemIdentifyMessage) ProtoMessage() {}

func (x *S2C_BagItemIdentifyMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_bag_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_BagItemIdentifyMessage.ProtoReflect.Descriptor instead.
func (*S2C_BagItemIdentifyMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_bag_proto_rawDescGZIP(), []int{15}
}

func (x *S2C_BagItemIdentifyMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

func (x *S2C_BagItemIdentifyMessage) GetItem() *pbBase.ItemData {
	if x != nil {
		return x.Item
	}
	return nil
}

type C2S_BagItemIdentifyAnswerMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`           //物品id
	SlotPos int32 `protobuf:"varint,2,opt,name=slotPos,proto3" json:"slotPos,omitempty"` //物品位置
}

func (x *C2S_BagItemIdentifyAnswerMessage) Reset() {
	*x = C2S_BagItemIdentifyAnswerMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_bag_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_BagItemIdentifyAnswerMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_BagItemIdentifyAnswerMessage) ProtoMessage() {}

func (x *C2S_BagItemIdentifyAnswerMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_bag_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_BagItemIdentifyAnswerMessage.ProtoReflect.Descriptor instead.
func (*C2S_BagItemIdentifyAnswerMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_bag_proto_rawDescGZIP(), []int{16}
}

func (x *C2S_BagItemIdentifyAnswerMessage) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *C2S_BagItemIdentifyAnswerMessage) GetSlotPos() int32 {
	if x != nil {
		return x.SlotPos
	}
	return 0
}

type S2C_BagItemIdentifyAnswerMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
}

func (x *S2C_BagItemIdentifyAnswerMessage) Reset() {
	*x = S2C_BagItemIdentifyAnswerMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_bag_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_BagItemIdentifyAnswerMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_BagItemIdentifyAnswerMessage) ProtoMessage() {}

func (x *S2C_BagItemIdentifyAnswerMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_bag_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_BagItemIdentifyAnswerMessage.ProtoReflect.Descriptor instead.
func (*S2C_BagItemIdentifyAnswerMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_bag_proto_rawDescGZIP(), []int{17}
}

func (x *S2C_BagItemIdentifyAnswerMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

type C2S_BagItemEnchaseMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemSlotPos int32 `protobuf:"varint,1,opt,name=itemSlotPos,proto3" json:"itemSlotPos,omitempty"` //物品位置
	GemSlotPos  int32 `protobuf:"varint,2,opt,name=gemSlotPos,proto3" json:"gemSlotPos,omitempty"`   //宝石位置
}

func (x *C2S_BagItemEnchaseMessage) Reset() {
	*x = C2S_BagItemEnchaseMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_bag_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_BagItemEnchaseMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_BagItemEnchaseMessage) ProtoMessage() {}

func (x *C2S_BagItemEnchaseMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_bag_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_BagItemEnchaseMessage.ProtoReflect.Descriptor instead.
func (*C2S_BagItemEnchaseMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_bag_proto_rawDescGZIP(), []int{18}
}

func (x *C2S_BagItemEnchaseMessage) GetItemSlotPos() int32 {
	if x != nil {
		return x.ItemSlotPos
	}
	return 0
}

func (x *C2S_BagItemEnchaseMessage) GetGemSlotPos() int32 {
	if x != nil {
		return x.GemSlotPos
	}
	return 0
}

type S2C_BagItemEnchaseMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code     Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
	IsBroken bool          `protobuf:"varint,2,opt,name=isBroken,proto3" json:"isBroken,omitempty"`                  //是否失败破损
}

func (x *S2C_BagItemEnchaseMessage) Reset() {
	*x = S2C_BagItemEnchaseMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_bag_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_BagItemEnchaseMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_BagItemEnchaseMessage) ProtoMessage() {}

func (x *S2C_BagItemEnchaseMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_bag_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_BagItemEnchaseMessage.ProtoReflect.Descriptor instead.
func (*S2C_BagItemEnchaseMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_bag_proto_rawDescGZIP(), []int{19}
}

func (x *S2C_BagItemEnchaseMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

func (x *S2C_BagItemEnchaseMessage) GetIsBroken() bool {
	if x != nil {
		return x.IsBroken
	}
	return false
}

type C2S_BagItemGemReplaceMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemSlotPos int32 `protobuf:"varint,1,opt,name=itemSlotPos,proto3" json:"itemSlotPos,omitempty"` //物品位置
	GemSlotPos  int32 `protobuf:"varint,2,opt,name=gemSlotPos,proto3" json:"gemSlotPos,omitempty"`   //宝石位置
}

func (x *C2S_BagItemGemReplaceMessage) Reset() {
	*x = C2S_BagItemGemReplaceMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_bag_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_BagItemGemReplaceMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_BagItemGemReplaceMessage) ProtoMessage() {}

func (x *C2S_BagItemGemReplaceMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_bag_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_BagItemGemReplaceMessage.ProtoReflect.Descriptor instead.
func (*C2S_BagItemGemReplaceMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_bag_proto_rawDescGZIP(), []int{20}
}

func (x *C2S_BagItemGemReplaceMessage) GetItemSlotPos() int32 {
	if x != nil {
		return x.ItemSlotPos
	}
	return 0
}

func (x *C2S_BagItemGemReplaceMessage) GetGemSlotPos() int32 {
	if x != nil {
		return x.GemSlotPos
	}
	return 0
}

type S2C_BagItemGemReplaceMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
}

func (x *S2C_BagItemGemReplaceMessage) Reset() {
	*x = S2C_BagItemGemReplaceMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_bag_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_BagItemGemReplaceMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_BagItemGemReplaceMessage) ProtoMessage() {}

func (x *S2C_BagItemGemReplaceMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_bag_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_BagItemGemReplaceMessage.ProtoReflect.Descriptor instead.
func (*S2C_BagItemGemReplaceMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_bag_proto_rawDescGZIP(), []int{21}
}

func (x *S2C_BagItemGemReplaceMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

var File_pbGame_bag_proto protoreflect.FileDescriptor

var file_pbGame_bag_proto_rawDesc = []byte{
	0x0a, 0x10, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x2f, 0x62, 0x61, 0x67, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x70, 0x62, 0x42, 0x61, 0x73,
	0x65, 0x2f, 0x42, 0x61, 0x67, 0x52, 0x65, 0x73, 0x65, 0x74, 0x2f, 0x42, 0x61, 0x67, 0x52, 0x65,
	0x73, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x70, 0x62, 0x42, 0x61, 0x73,
	0x65, 0x2f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2f, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x70, 0x62, 0x42, 0x61, 0x73,
	0x65, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14,
	0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x42, 0x61, 0x67, 0x2f, 0x42, 0x61, 0x67, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x3f, 0x0a, 0x13, 0x43, 0x32, 0x53, 0x5f, 0x42, 0x61, 0x67, 0x52, 0x65, 0x73, 0x65, 0x74, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x61, 0x67,
	0x52, 0x65, 0x73, 0x65, 0x74, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x22, 0xc7, 0x01, 0x0a, 0x13, 0x53, 0x32, 0x43, 0x5f, 0x42, 0x61, 0x67, 0x52, 0x65, 0x73, 0x65,
	0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x3b, 0x0a, 0x05, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x32, 0x43, 0x5f, 0x42, 0x61,
	0x67, 0x52, 0x65, 0x73, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x53, 0x74,
	0x6f, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x1a,
	0x49, 0x0a, 0x0a, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x25, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x51, 0x0a, 0x16, 0x43, 0x32,
	0x53, 0x5f, 0x42, 0x61, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x53, 0x65, 0x6c, 0x6c, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49,
	0x74, 0x65, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x42, 0x0a,
	0x16, 0x53, 0x32, 0x43, 0x5f, 0x42, 0x61, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x53, 0x65, 0x6c, 0x6c,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x22, 0x48, 0x0a, 0x14, 0x43, 0x32, 0x53, 0x5f, 0x45, 0x71, 0x75, 0x69, 0x70, 0x57, 0x65,
	0x61, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x6c, 0x6f,
	0x74, 0x50, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x73, 0x6c, 0x6f, 0x74,
	0x50, 0x6f, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x22, 0x40, 0x0a, 0x14, 0x53,
	0x32, 0x43, 0x5f, 0x45, 0x71, 0x75, 0x69, 0x70, 0x57, 0x65, 0x61, 0x72, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x4b, 0x0a,
	0x17, 0x43, 0x32, 0x53, 0x5f, 0x45, 0x71, 0x75, 0x69, 0x70, 0x54, 0x61, 0x6b, 0x65, 0x4f, 0x66,
	0x66, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x6c, 0x6f, 0x74,
	0x50, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x73, 0x6c, 0x6f, 0x74, 0x50,
	0x6f, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x22, 0x43, 0x0a, 0x17, 0x53, 0x32,
	0x43, 0x5f, 0x45, 0x71, 0x75, 0x69, 0x70, 0x54, 0x61, 0x6b, 0x65, 0x4f, 0x66, 0x66, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22,
	0xb7, 0x01, 0x0a, 0x17, 0x43, 0x32, 0x53, 0x5f, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x42, 0x61,
	0x67, 0x55, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x30, 0x0a, 0x07, 0x75,
	0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x61, 0x67, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x55, 0x73, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x75, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x69, 0x74, 0x65, 0x6d, 0x53, 0x6c, 0x6f, 0x74, 0x50, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x69, 0x74, 0x65, 0x6d, 0x53, 0x6c, 0x6f, 0x74, 0x50, 0x6f, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x4e, 0x75,
	0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x75, 0x73, 0x65, 0x4e, 0x75, 0x6d, 0x12,
	0x18, 0x0a, 0x07, 0x65, 0x78, 0x74, 0x72, 0x61, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x65, 0x78, 0x74, 0x72, 0x61, 0x49, 0x64, 0x22, 0x93, 0x01, 0x0a, 0x17, 0x53, 0x32,
	0x43, 0x5f, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x42, 0x61, 0x67, 0x55, 0x73, 0x65, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x26, 0x0a, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x26, 0x0a, 0x03, 0x61, 0x6e, 0x79, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x03, 0x61, 0x6e, 0x79, 0x22,
	0x3d, 0x0a, 0x16, 0x43, 0x32, 0x53, 0x5f, 0x42, 0x61, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x69,
	0x6e, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x23, 0x0a, 0x04, 0x69, 0x74, 0x65,
	0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x49, 0x74, 0x65, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x22, 0x42,
	0x0a, 0x16, 0x53, 0x32, 0x43, 0x5f, 0x42, 0x61, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x69, 0x6e,
	0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x22, 0x5b, 0x0a, 0x16, 0x43, 0x32, 0x53, 0x5f, 0x42, 0x61, 0x67, 0x49, 0x74, 0x65,
	0x6d, 0x53, 0x74, 0x61, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x23, 0x0a, 0x04,
	0x69, 0x74, 0x65, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x69, 0x74, 0x65,
	0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x22,
	0x5a, 0x0a, 0x16, 0x53, 0x32, 0x43, 0x5f, 0x42, 0x61, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x53, 0x74,
	0x61, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x5f, 0x0a, 0x1a, 0x43,
	0x32, 0x53, 0x5f, 0x42, 0x61, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x23, 0x0a, 0x04, 0x69, 0x74, 0x65,
	0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x49, 0x74, 0x65, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x12, 0x1c,
	0x0a, 0x09, 0x69, 0x73, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x09, 0x69, 0x73, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x22, 0x6b, 0x0a, 0x1a,
	0x53, 0x32, 0x43, 0x5f, 0x42, 0x61, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x22, 0x4c, 0x0a, 0x20, 0x43, 0x32, 0x53,
	0x5f, 0x42, 0x61, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x79,
	0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x6c, 0x6f, 0x74, 0x50, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x73, 0x6c, 0x6f, 0x74, 0x50, 0x6f, 0x73, 0x22, 0x4c, 0x0a, 0x20, 0x53, 0x32, 0x43, 0x5f, 0x42,
	0x61, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x79, 0x41, 0x6e,
	0x73, 0x77, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x5d, 0x0a, 0x19, 0x43, 0x32, 0x53, 0x5f, 0x42, 0x61, 0x67,
	0x49, 0x74, 0x65, 0x6d, 0x45, 0x6e, 0x63, 0x68, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x69, 0x74, 0x65, 0x6d, 0x53, 0x6c, 0x6f, 0x74, 0x50, 0x6f,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x69, 0x74, 0x65, 0x6d, 0x53, 0x6c, 0x6f,
	0x74, 0x50, 0x6f, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x67, 0x65, 0x6d, 0x53, 0x6c, 0x6f, 0x74, 0x50,
	0x6f, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x67, 0x65, 0x6d, 0x53, 0x6c, 0x6f,
	0x74, 0x50, 0x6f, 0x73, 0x22, 0x61, 0x0a, 0x19, 0x53, 0x32, 0x43, 0x5f, 0x42, 0x61, 0x67, 0x49,
	0x74, 0x65, 0x6d, 0x45, 0x6e, 0x63, 0x68, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x69,
	0x73, 0x42, 0x72, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69,
	0x73, 0x42, 0x72, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x60, 0x0a, 0x1c, 0x43, 0x32, 0x53, 0x5f, 0x42,
	0x61, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x47, 0x65, 0x6d, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x69, 0x74, 0x65, 0x6d, 0x53,
	0x6c, 0x6f, 0x74, 0x50, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x69, 0x74,
	0x65, 0x6d, 0x53, 0x6c, 0x6f, 0x74, 0x50, 0x6f, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x67, 0x65, 0x6d,
	0x53, 0x6c, 0x6f, 0x74, 0x50, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x67,
	0x65, 0x6d, 0x53, 0x6c, 0x6f, 0x74, 0x50, 0x6f, 0x73, 0x22, 0x48, 0x0a, 0x1c, 0x53, 0x32, 0x43,
	0x5f, 0x42, 0x61, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x47, 0x65, 0x6d, 0x52, 0x65, 0x70, 0x6c, 0x61,
	0x63, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x42, 0x1c, 0x5a, 0x1a, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x3b, 0x70, 0x62, 0x47, 0x61, 0x6d,
	0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbGame_bag_proto_rawDescOnce sync.Once
	file_pbGame_bag_proto_rawDescData = file_pbGame_bag_proto_rawDesc
)

func file_pbGame_bag_proto_rawDescGZIP() []byte {
	file_pbGame_bag_proto_rawDescOnce.Do(func() {
		file_pbGame_bag_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbGame_bag_proto_rawDescData)
	})
	return file_pbGame_bag_proto_rawDescData
}

var file_pbGame_bag_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_pbGame_bag_proto_goTypes = []interface{}{
	(*C2S_BagResetMessage)(nil),              // 0: proto.C2S_BagResetMessage
	(*S2C_BagResetMessage)(nil),              // 1: proto.S2C_BagResetMessage
	(*C2S_BagItemSellMessage)(nil),           // 2: proto.C2S_BagItemSellMessage
	(*S2C_BagItemSellMessage)(nil),           // 3: proto.S2C_BagItemSellMessage
	(*C2S_EquipWearMessage)(nil),             // 4: proto.C2S_EquipWearMessage
	(*S2C_EquipWearMessage)(nil),             // 5: proto.S2C_EquipWearMessage
	(*C2S_EquipTakeOffMessage)(nil),          // 6: proto.C2S_EquipTakeOffMessage
	(*S2C_EquipTakeOffMessage)(nil),          // 7: proto.S2C_EquipTakeOffMessage
	(*C2S_PlayerBagUseMessage)(nil),          // 8: proto.C2S_PlayerBagUseMessage
	(*S2C_PlayerBagUseMessage)(nil),          // 9: proto.S2C_PlayerBagUseMessage
	(*C2S_BagItemBindMessage)(nil),           // 10: proto.C2S_BagItemBindMessage
	(*S2C_BagItemBindMessage)(nil),           // 11: proto.S2C_BagItemBindMessage
	(*C2S_BagItemStarMessage)(nil),           // 12: proto.C2S_BagItemStarMessage
	(*S2C_BagItemStarMessage)(nil),           // 13: proto.S2C_BagItemStarMessage
	(*C2S_BagItemIdentifyMessage)(nil),       // 14: proto.C2S_BagItemIdentifyMessage
	(*S2C_BagItemIdentifyMessage)(nil),       // 15: proto.S2C_BagItemIdentifyMessage
	(*C2S_BagItemIdentifyAnswerMessage)(nil), // 16: proto.C2S_BagItemIdentifyAnswerMessage
	(*S2C_BagItemIdentifyAnswerMessage)(nil), // 17: proto.S2C_BagItemIdentifyAnswerMessage
	(*C2S_BagItemEnchaseMessage)(nil),        // 18: proto.C2S_BagItemEnchaseMessage
	(*S2C_BagItemEnchaseMessage)(nil),        // 19: proto.S2C_BagItemEnchaseMessage
	(*C2S_BagItemGemReplaceMessage)(nil),     // 20: proto.C2S_BagItemGemReplaceMessage
	(*S2C_BagItemGemReplaceMessage)(nil),     // 21: proto.S2C_BagItemGemReplaceMessage
	nil,                                      // 22: proto.S2C_BagResetMessage.StoreEntry
	(BagReset.Type)(0),                       // 23: proto.BagReset.Type
	(Response.Code)(0),                       // 24: proto.Response.Code
	(*pbBase.ItemData)(nil),                  // 25: proto.ItemData
	(Bag.ItemUseType)(0),                     // 26: proto.Bag.ItemUseType
	(*anypb.Any)(nil),                        // 27: google.protobuf.Any
}
var file_pbGame_bag_proto_depIdxs = []int32{
	23, // 0: proto.C2S_BagResetMessage.type:type_name -> proto.BagReset.Type
	24, // 1: proto.S2C_BagResetMessage.code:type_name -> proto.Response.Code
	22, // 2: proto.S2C_BagResetMessage.store:type_name -> proto.S2C_BagResetMessage.StoreEntry
	25, // 3: proto.C2S_BagItemSellMessage.list:type_name -> proto.ItemData
	24, // 4: proto.S2C_BagItemSellMessage.code:type_name -> proto.Response.Code
	24, // 5: proto.S2C_EquipWearMessage.code:type_name -> proto.Response.Code
	24, // 6: proto.S2C_EquipTakeOffMessage.code:type_name -> proto.Response.Code
	26, // 7: proto.C2S_PlayerBagUseMessage.useType:type_name -> proto.Bag.ItemUseType
	24, // 8: proto.S2C_PlayerBagUseMessage.code:type_name -> proto.Response.Code
	27, // 9: proto.S2C_PlayerBagUseMessage.any:type_name -> google.protobuf.Any
	25, // 10: proto.C2S_BagItemBindMessage.item:type_name -> proto.ItemData
	24, // 11: proto.S2C_BagItemBindMessage.code:type_name -> proto.Response.Code
	25, // 12: proto.C2S_BagItemStarMessage.item:type_name -> proto.ItemData
	24, // 13: proto.S2C_BagItemStarMessage.code:type_name -> proto.Response.Code
	25, // 14: proto.C2S_BagItemIdentifyMessage.item:type_name -> proto.ItemData
	24, // 15: proto.S2C_BagItemIdentifyMessage.code:type_name -> proto.Response.Code
	25, // 16: proto.S2C_BagItemIdentifyMessage.item:type_name -> proto.ItemData
	24, // 17: proto.S2C_BagItemIdentifyAnswerMessage.code:type_name -> proto.Response.Code
	24, // 18: proto.S2C_BagItemEnchaseMessage.code:type_name -> proto.Response.Code
	24, // 19: proto.S2C_BagItemGemReplaceMessage.code:type_name -> proto.Response.Code
	25, // 20: proto.S2C_BagResetMessage.StoreEntry.value:type_name -> proto.ItemData
	21, // [21:21] is the sub-list for method output_type
	21, // [21:21] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_pbGame_bag_proto_init() }
func file_pbGame_bag_proto_init() {
	if File_pbGame_bag_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbGame_bag_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_BagResetMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_bag_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_BagResetMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_bag_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_BagItemSellMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_bag_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_BagItemSellMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_bag_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_EquipWearMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_bag_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_EquipWearMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_bag_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_EquipTakeOffMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_bag_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_EquipTakeOffMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_bag_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_PlayerBagUseMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_bag_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_PlayerBagUseMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_bag_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_BagItemBindMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_bag_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_BagItemBindMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_bag_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_BagItemStarMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_bag_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_BagItemStarMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_bag_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_BagItemIdentifyMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_bag_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_BagItemIdentifyMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_bag_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_BagItemIdentifyAnswerMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_bag_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_BagItemIdentifyAnswerMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_bag_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_BagItemEnchaseMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_bag_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_BagItemEnchaseMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_bag_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_BagItemGemReplaceMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_bag_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_BagItemGemReplaceMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbGame_bag_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbGame_bag_proto_goTypes,
		DependencyIndexes: file_pbGame_bag_proto_depIdxs,
		MessageInfos:      file_pbGame_bag_proto_msgTypes,
	}.Build()
	File_pbGame_bag_proto = out.File
	file_pbGame_bag_proto_rawDesc = nil
	file_pbGame_bag_proto_goTypes = nil
	file_pbGame_bag_proto_depIdxs = nil
}
