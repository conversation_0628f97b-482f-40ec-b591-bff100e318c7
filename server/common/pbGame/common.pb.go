// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v6.30.1
// source: pbGame/common.proto

package pbGame

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	pbBase "world/common/pbBase"
	Camp "world/common/pbBase/Camp"
	Job "world/common/pbBase/Job"
	Response "world/common/pbBase/Response"
	Sex "world/common/pbBase/Sex"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are messages.
type C2S_GetPlayerListMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_GetPlayerListMessage) Reset() {
	*x = C2S_GetPlayerListMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_GetPlayerListMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_GetPlayerListMessage) ProtoMessage() {}

func (x *C2S_GetPlayerListMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_GetPlayerListMessage.ProtoReflect.Descriptor instead.
func (*C2S_GetPlayerListMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_common_proto_rawDescGZIP(), []int{0}
}

type S2C_GetPlayerListMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List       []*pbBase.SimplePlayerInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`              //角色列表
	LastRoleId int32                      `protobuf:"varint,2,opt,name=lastRoleId,proto3" json:"lastRoleId,omitempty"` //上次登陆选择的角色
}

func (x *S2C_GetPlayerListMessage) Reset() {
	*x = S2C_GetPlayerListMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_common_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_GetPlayerListMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_GetPlayerListMessage) ProtoMessage() {}

func (x *S2C_GetPlayerListMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_common_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_GetPlayerListMessage.ProtoReflect.Descriptor instead.
func (*S2C_GetPlayerListMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_common_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_GetPlayerListMessage) GetList() []*pbBase.SimplePlayerInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *S2C_GetPlayerListMessage) GetLastRoleId() int32 {
	if x != nil {
		return x.LastRoleId
	}
	return 0
}

type C2S_CreateRoleMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sex   Sex.Type  `protobuf:"varint,1,opt,name=sex,proto3,enum=proto.Sex.Type" json:"sex,omitempty"`    //性别
	Race  Camp.Type `protobuf:"varint,2,opt,name=race,proto3,enum=proto.Camp.Type" json:"race,omitempty"` //阵营
	Job   Job.Type  `protobuf:"varint,3,opt,name=job,proto3,enum=proto.Job.Type" json:"job,omitempty"`    //职业
	Model int32     `protobuf:"varint,4,opt,name=model,proto3" json:"model,omitempty"`                    //造型
	Name  string    `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`                       //角色名称
}

func (x *C2S_CreateRoleMessage) Reset() {
	*x = C2S_CreateRoleMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_common_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_CreateRoleMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_CreateRoleMessage) ProtoMessage() {}

func (x *C2S_CreateRoleMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_common_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_CreateRoleMessage.ProtoReflect.Descriptor instead.
func (*C2S_CreateRoleMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_common_proto_rawDescGZIP(), []int{2}
}

func (x *C2S_CreateRoleMessage) GetSex() Sex.Type {
	if x != nil {
		return x.Sex
	}
	return Sex.Type(0)
}

func (x *C2S_CreateRoleMessage) GetRace() Camp.Type {
	if x != nil {
		return x.Race
	}
	return Camp.Type(0)
}

func (x *C2S_CreateRoleMessage) GetJob() Job.Type {
	if x != nil {
		return x.Job
	}
	return Job.Type(0)
}

func (x *C2S_CreateRoleMessage) GetModel() int32 {
	if x != nil {
		return x.Model
	}
	return 0
}

func (x *C2S_CreateRoleMessage) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type S2C_CreateRoleMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code            `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
	Role *pbBase.SimplePlayerInfo `protobuf:"bytes,2,opt,name=role,proto3" json:"role,omitempty"`                           //创建好的角色信息
}

func (x *S2C_CreateRoleMessage) Reset() {
	*x = S2C_CreateRoleMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_common_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_CreateRoleMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_CreateRoleMessage) ProtoMessage() {}

func (x *S2C_CreateRoleMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_common_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_CreateRoleMessage.ProtoReflect.Descriptor instead.
func (*S2C_CreateRoleMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_common_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_CreateRoleMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

func (x *S2C_CreateRoleMessage) GetRole() *pbBase.SimplePlayerInfo {
	if x != nil {
		return x.Role
	}
	return nil
}

type C2S_EnterGameMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameId int32 `protobuf:"varint,1,opt,name=gameId,proto3" json:"gameId,omitempty"` //角色id
}

func (x *C2S_EnterGameMessage) Reset() {
	*x = C2S_EnterGameMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_common_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_EnterGameMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_EnterGameMessage) ProtoMessage() {}

func (x *C2S_EnterGameMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_common_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_EnterGameMessage.ProtoReflect.Descriptor instead.
func (*C2S_EnterGameMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_common_proto_rawDescGZIP(), []int{4}
}

func (x *C2S_EnterGameMessage) GetGameId() int32 {
	if x != nil {
		return x.GameId
	}
	return 0
}

type S2C_EnterGameMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code      `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
	Data *pbBase.PlayerInfo `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`                           //角色数据
}

func (x *S2C_EnterGameMessage) Reset() {
	*x = S2C_EnterGameMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_common_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_EnterGameMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_EnterGameMessage) ProtoMessage() {}

func (x *S2C_EnterGameMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_common_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_EnterGameMessage.ProtoReflect.Descriptor instead.
func (*S2C_EnterGameMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_common_proto_rawDescGZIP(), []int{5}
}

func (x *S2C_EnterGameMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

func (x *S2C_EnterGameMessage) GetData() *pbBase.PlayerInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type C2S_GmExecuteMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cmd string `protobuf:"bytes,1,opt,name=cmd,proto3" json:"cmd,omitempty"` //gm命令
}

func (x *C2S_GmExecuteMessage) Reset() {
	*x = C2S_GmExecuteMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_common_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_GmExecuteMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_GmExecuteMessage) ProtoMessage() {}

func (x *C2S_GmExecuteMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_common_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_GmExecuteMessage.ProtoReflect.Descriptor instead.
func (*C2S_GmExecuteMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_common_proto_rawDescGZIP(), []int{6}
}

func (x *C2S_GmExecuteMessage) GetCmd() string {
	if x != nil {
		return x.Cmd
	}
	return ""
}

type S2C_GmExecuteMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Reply string `protobuf:"bytes,1,opt,name=reply,proto3" json:"reply,omitempty"` //
}

func (x *S2C_GmExecuteMessage) Reset() {
	*x = S2C_GmExecuteMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_common_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_GmExecuteMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_GmExecuteMessage) ProtoMessage() {}

func (x *S2C_GmExecuteMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_common_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_GmExecuteMessage.ProtoReflect.Descriptor instead.
func (*S2C_GmExecuteMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_common_proto_rawDescGZIP(), []int{7}
}

func (x *S2C_GmExecuteMessage) GetReply() string {
	if x != nil {
		return x.Reply
	}
	return ""
}

var File_pbGame_common_proto protoreflect.FileDescriptor

var file_pbGame_common_proto_rawDesc = []byte{
	0x0a, 0x13, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x70, 0x62,
	0x42, 0x61, 0x73, 0x65, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x14, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x53, 0x65, 0x78, 0x2f, 0x53, 0x65,
	0x78, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f,
	0x43, 0x61, 0x6d, 0x70, 0x2f, 0x43, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x14, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x4a, 0x6f, 0x62, 0x2f, 0x4a, 0x6f, 0x62, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x1a, 0x0a, 0x18, 0x43, 0x32, 0x53, 0x5f, 0x47, 0x65, 0x74,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x22, 0x67, 0x0a, 0x18, 0x53, 0x32, 0x43, 0x5f, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2b, 0x0a,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x6c, 0x61,
	0x73, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x6c, 0x61, 0x73, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x22, 0xad, 0x01, 0x0a, 0x15, 0x43,
	0x32, 0x53, 0x5f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x21, 0x0a, 0x03, 0x73, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x65, 0x78, 0x2e, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x03, 0x73, 0x65, 0x78, 0x12, 0x24, 0x0a, 0x04, 0x72, 0x61, 0x63, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x61,
	0x6d, 0x70, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x72, 0x61, 0x63, 0x65, 0x12, 0x21, 0x0a,
	0x03, 0x6a, 0x6f, 0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x4a, 0x6f, 0x62, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x03, 0x6a, 0x6f, 0x62,
	0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x6e, 0x0a, 0x15, 0x53, 0x32,
	0x43, 0x5f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x2b, 0x0a,
	0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x22, 0x2e, 0x0a, 0x14, 0x43, 0x32,
	0x53, 0x5f, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x47, 0x61, 0x6d, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x22, 0x67, 0x0a, 0x14, 0x53, 0x32,
	0x43, 0x5f, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x47, 0x61, 0x6d, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x22, 0x28, 0x0a, 0x14, 0x43, 0x32, 0x53, 0x5f, 0x47, 0x6d, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x63,
	0x6d, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x63, 0x6d, 0x64, 0x22, 0x2c, 0x0a,
	0x14, 0x53, 0x32, 0x43, 0x5f, 0x47, 0x6d, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x42, 0x1c, 0x5a, 0x1a, 0x77,
	0x6f, 0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x47, 0x61,
	0x6d, 0x65, 0x3b, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_pbGame_common_proto_rawDescOnce sync.Once
	file_pbGame_common_proto_rawDescData = file_pbGame_common_proto_rawDesc
)

func file_pbGame_common_proto_rawDescGZIP() []byte {
	file_pbGame_common_proto_rawDescOnce.Do(func() {
		file_pbGame_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbGame_common_proto_rawDescData)
	})
	return file_pbGame_common_proto_rawDescData
}

var file_pbGame_common_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_pbGame_common_proto_goTypes = []interface{}{
	(*C2S_GetPlayerListMessage)(nil), // 0: proto.C2S_GetPlayerListMessage
	(*S2C_GetPlayerListMessage)(nil), // 1: proto.S2C_GetPlayerListMessage
	(*C2S_CreateRoleMessage)(nil),    // 2: proto.C2S_CreateRoleMessage
	(*S2C_CreateRoleMessage)(nil),    // 3: proto.S2C_CreateRoleMessage
	(*C2S_EnterGameMessage)(nil),     // 4: proto.C2S_EnterGameMessage
	(*S2C_EnterGameMessage)(nil),     // 5: proto.S2C_EnterGameMessage
	(*C2S_GmExecuteMessage)(nil),     // 6: proto.C2S_GmExecuteMessage
	(*S2C_GmExecuteMessage)(nil),     // 7: proto.S2C_GmExecuteMessage
	(*pbBase.SimplePlayerInfo)(nil),  // 8: proto.SimplePlayerInfo
	(Sex.Type)(0),                    // 9: proto.Sex.Type
	(Camp.Type)(0),                   // 10: proto.Camp.Type
	(Job.Type)(0),                    // 11: proto.Job.Type
	(Response.Code)(0),               // 12: proto.Response.Code
	(*pbBase.PlayerInfo)(nil),        // 13: proto.PlayerInfo
}
var file_pbGame_common_proto_depIdxs = []int32{
	8,  // 0: proto.S2C_GetPlayerListMessage.list:type_name -> proto.SimplePlayerInfo
	9,  // 1: proto.C2S_CreateRoleMessage.sex:type_name -> proto.Sex.Type
	10, // 2: proto.C2S_CreateRoleMessage.race:type_name -> proto.Camp.Type
	11, // 3: proto.C2S_CreateRoleMessage.job:type_name -> proto.Job.Type
	12, // 4: proto.S2C_CreateRoleMessage.code:type_name -> proto.Response.Code
	8,  // 5: proto.S2C_CreateRoleMessage.role:type_name -> proto.SimplePlayerInfo
	12, // 6: proto.S2C_EnterGameMessage.code:type_name -> proto.Response.Code
	13, // 7: proto.S2C_EnterGameMessage.data:type_name -> proto.PlayerInfo
	8,  // [8:8] is the sub-list for method output_type
	8,  // [8:8] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_pbGame_common_proto_init() }
func file_pbGame_common_proto_init() {
	if File_pbGame_common_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbGame_common_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_GetPlayerListMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_common_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_GetPlayerListMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_common_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_CreateRoleMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_common_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_CreateRoleMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_common_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_EnterGameMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_common_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_EnterGameMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_common_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_GmExecuteMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_common_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_GmExecuteMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbGame_common_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbGame_common_proto_goTypes,
		DependencyIndexes: file_pbGame_common_proto_depIdxs,
		MessageInfos:      file_pbGame_common_proto_msgTypes,
	}.Build()
	File_pbGame_common_proto = out.File
	file_pbGame_common_proto_rawDesc = nil
	file_pbGame_common_proto_goTypes = nil
	file_pbGame_common_proto_depIdxs = nil
}
