package comm

import (
	"fmt"
	"strings"
	"time"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

func InitDeadlockListener() {
	deadlock.Opts.DeadlockTimeout = time.Second * 3
	deadlock.Opts.OnPotentialDeadlock = onDeadLock
	deadlock.Opts.LogBuf = &DeadLockLogger{}
	deadlock.Opts.LogBuf.Write([]byte(fmt.Sprintf("====init dead lock listener====")))
}

func onDeadLock() {
	// do nothing here
	const str = "===== on dead lock end ====="
	deadlock.Opts.LogBuf.Write([]byte(str))
}

type DeadLockLogger struct {
}

func (this *DeadLockLogger) Write(p []byte) (n int, err error) {
	if len(p) == 0 {
		return 0, nil
	}
	str := string(p)
	if strings.ReplaceAll(str, " ", "") == "\n" {
		return 0, nil
	}
	//writeMsg will always add a '\n' character
	if p[len(p)-1] == '\n' {
		p = p[0 : len(p)-1]
	}
	log.Info(string(p))
	return 0, err
}
