package router

// msg_define 自动生成，不要在这个文件添加任何内容。

// S2CBagResetMessage 整理物品
const S2CBagResetMessage = "S2C_BagResetMessage" 
// S2CBagItemSellMessage 物品操作
const S2CBagItemSellMessage = "S2C_BagItemSellMessage" 
// S2CEquipWearMessage 穿戴装备
const S2CEquipWearMessage = "S2C_EquipWearMessage" 
// S2CEquipTakeOffMessage 脱下装备
const S2CEquipTakeOffMessage = "S2C_EquipTakeOffMessage" 
// S2CPlayerBagUseMessage 使用物品
const S2CPlayerBagUseMessage = "S2C_PlayerBagUseMessage" 
// S2CBagItemBindMessage 物品绑定
const S2CBagItemBindMessage = "S2C_BagItemBindMessage" 
// S2CBagItemStarMessage 装备物品升星
const S2CBagItemStarMessage = "S2C_BagItemStarMessage" 
// S2CBagItemIdentifyMessage 装备物品鉴定
const S2CBagItemIdentifyMessage = "S2C_BagItemIdentifyMessage" 
// S2CBagItemIdentifyAnswerMessage 鉴定响应
const S2CBagItemIdentifyAnswerMessage = "S2C_BagItemIdentifyAnswerMessage" 
// S2CBagItemEnchaseMessage 宝石镶嵌
const S2CBagItemEnchaseMessage = "S2C_BagItemEnchaseMessage" 
// S2CBagItemGemReplaceMessage 宝石替换
const S2CBagItemGemReplaceMessage = "S2C_BagItemGemReplaceMessage" 
// S2CBattlePassTestMessage 战斗测试
const S2CBattlePassTestMessage = "S2C_BattlePassTestMessage" 
// S2CRunLocalBattleMessage 校验本地战斗
const S2CRunLocalBattleMessage = "S2C_RunLocalBattleMessage" 
// S2CGetPlayerListMessage 拉取角色列表
const S2CGetPlayerListMessage = "S2C_GetPlayerListMessage" 
// S2CCreateRoleMessage 创建角色
const S2CCreateRoleMessage = "S2C_CreateRoleMessage" 
// S2CEnterGameMessage 角色登入
const S2CEnterGameMessage = "S2C_EnterGameMessage" 
// S2CGmExecuteMessage 执行gm
const S2CGmExecuteMessage = "S2C_GmExecuteMessage" 
// S2CEnterMapMessage 进入地图
const S2CEnterMapMessage = "S2C_EnterMapMessage" 
// S2CLeaveMapMessage 离开地图
const S2CLeaveMapMessage = "S2C_LeaveMapMessage" 
// S2COtherMoveMessage 其他玩家移动或者模型改变
const S2COtherMoveMessage = "S2C_OtherMoveMessage" 
// S2CTeamJoinMessage 加入队伍
const S2CTeamJoinMessage = "S2C_TeamJoinMessage" 
// S2CTeamInviteMessage 邀请加入队伍
const S2CTeamInviteMessage = "S2C_TeamInviteMessage" 
// S2CJumpMapMessage 跳转地图
const S2CJumpMapMessage = "S2C_JumpMapMessage" 
// S2CPlayerMoveMessage 玩家移动
const S2CPlayerMoveMessage = "S2C_PlayerMoveMessage" 
// S2CScenePlayerEventMessage 场景玩家事件,某个玩家对另一个玩家发起组队邀请等事件
const S2CScenePlayerEventMessage = "S2C_ScenePlayerEventMessage" 
// S2CPlayerEventChooseMessage 玩家对于场景事件的选择处理响应
const S2CPlayerEventChooseMessage = "S2C_PlayerEventChooseMessage" 
// S2CGetPlayerEventChooseResultMessage 收到其他玩家选择场景事件的结果
const S2CGetPlayerEventChooseResultMessage = "S2C_GetPlayerEventChooseResultMessage" 
// S2CBroadcastTeamJoinMessage 地图广播玩家加入队伍
const S2CBroadcastTeamJoinMessage = "S2C_BroadcastTeamJoinMessage" 
// S2CBroadcastTeamLeaveMessage 地图广播玩家离开队伍
const S2CBroadcastTeamLeaveMessage = "S2C_BroadcastTeamLeaveMessage" 
// S2CExitTeamMessage 队员退出队伍
const S2CExitTeamMessage = "S2C_ExitTeamMessage" 
// S2CRemoveMemberMessage 剔除队伍成员
const S2CRemoveMemberMessage = "S2C_RemoveMemberMessage" 
// S2CChangeLeaderMessage 切换队长
const S2CChangeLeaderMessage = "S2C_ChangeLeaderMessage" 
// S2CBroadcastChangeLeaderMessage 地图广播玩家改变队长
const S2CBroadcastChangeLeaderMessage = "S2C_BroadcastChangeLeaderMessage" 
// S2CDisbandTeamMessage 解散队伍
const S2CDisbandTeamMessage = "S2C_DisbandTeamMessage" 
// S2CBroadcastDisbandTeamMessage 地图广播玩家解散队伍
const S2CBroadcastDisbandTeamMessage = "S2C_BroadcastDisbandTeamMessage" 
// S2CBroadcastEnterMapTeamMessage 地图广播玩家改变队长
const S2CBroadcastEnterMapTeamMessage = "S2C_BroadcastEnterMapTeamMessage" 
// S2COnMailOpenMessage 打开邮件界面时获取部分信息
const S2COnMailOpenMessage = "S2C_OnMailOpenMessage" 
// S2CSendMailMessage 发送邮件
const S2CSendMailMessage = "S2C_SendMailMessage" 
// S2CMailListMessage 获取邮件列表
const S2CMailListMessage = "S2C_MailListMessage" 
// S2CMailDetailMessage 获取邮件详情
const S2CMailDetailMessage = "S2C_MailDetailMessage" 
// S2CMailAttachMessage 获取邮件附件
const S2CMailAttachMessage = "S2C_MailAttachMessage" 
// S2CMailBackMessage 拒收邮件
const S2CMailBackMessage = "S2C_MailBackMessage" 
// S2CMailDeleteMessage 删除邮件
const S2CMailDeleteMessage = "S2C_MailDeleteMessage" 
// S2CMailAttachAllMessage 全部领取
const S2CMailAttachAllMessage = "S2C_MailAttachAllMessage" 
// S2CMapDataMessage 主动推送，地图数据
const S2CMapDataMessage = "S2C_MapDataMessage" 
// S2CBagDataMessage 主动推送，背包数据
const S2CBagDataMessage = "S2C_BagDataMessage" 
// S2CLogoutMessage 使账号登出，返回登录界面
const S2CLogoutMessage = "S2C_LogoutMessage" 
// S2CSendAwardMessage 发放奖励弹窗
const S2CSendAwardMessage = "S2C_SendAwardMessage" 
// S2CNewMailMessage 收到新邮件
const S2CNewMailMessage = "S2C_NewMailMessage" 
// S2CPetAddSkillSureMessage 宠物潜能石结果替换确认
const S2CPetAddSkillSureMessage = "S2C_PetAddSkillSureMessage" 
// S2CPetSealMessage 宠物封印
const S2CPetSealMessage = "S2C_PetSealMessage" 
// S2CPetSkillBookLearnMessage 宠物使用技能书
const S2CPetSkillBookLearnMessage = "S2C_PetSkillBookLearnMessage" 
// S2CActorAttributeMessage 用户加点操作
const S2CActorAttributeMessage = "S2C_ActorAttributeMessage" 
// S2CLearnSkillByShopMessage 学习技能响应
const S2CLearnSkillByShopMessage = "S2C_LearnSkillByShopMessage" 
// S2CAutoSkillSetMessage 自动释放的技能设置
const S2CAutoSkillSetMessage = "S2C_AutoSkillSetMessage" 
// S2CAcceptTaskMessage 领取任务
const S2CAcceptTaskMessage = "S2C_AcceptTaskMessage" 
// S2CSubmitTaskMessage 提交任务
const S2CSubmitTaskMessage = "S2C_SubmitTaskMessage" 
// S2CDeleteTaskMessage 放弃任务
const S2CDeleteTaskMessage = "S2C_DeleteTaskMessage" 
// S2CClientCheckMessage 客户端版本检查
const S2CClientCheckMessage = "S2C_ClientCheckMessage" 
// S2CApplyLoginNoticeMessage 登录时获取公告
const S2CApplyLoginNoticeMessage = "S2C_ApplyLoginNoticeMessage" 
// S2CLoginMessage 登录
const S2CLoginMessage = "S2C_LoginMessage" 
// S2CGetAreaLinesMessage 获取区服信息
const S2CGetAreaLinesMessage = "S2C_GetAreaLinesMessage" 
// S2CErrorMessage 告知客户端弹出错误
const S2CErrorMessage = "S2C_ErrorMessage" 
// S2CSelectAreaMessage 选区
const S2CSelectAreaMessage = "S2C_SelectAreaMessage" 
// S2CSyncServerTimeMessage 同步服务器时间
const S2CSyncServerTimeMessage = "S2C_SyncServerTimeMessage" 
// S2RNotifyNewMailMessage 收到新邮件
const S2RNotifyNewMailMessage = "S2R_NotifyNewMailMessage" 
// S2ROnLeaveMessage session离线
const S2ROnLeaveMessage = "S2R_OnLeaveMessage" 
// S2ROnPlayerLoginMessage 用户选区登录后通知逻辑服
const S2ROnPlayerLoginMessage = "S2R_OnPlayerLoginMessage" 
// R2SOnPlayerLoginMessage 逻辑服回应
const R2SOnPlayerLoginMessage = "R2S_OnPlayerLoginMessage" 
// S2RIsPlayerOnlineMessage 查询玩家是不是在节点中在线
const S2RIsPlayerOnlineMessage = "S2R_IsPlayerOnlineMessage" 
// R2SIsPlayerOnlineMessage 查询玩家是不是在节点中在线响应
const R2SIsPlayerOnlineMessage = "R2S_IsPlayerOnlineMessage" 
// S2RKickPlayerForceByPidMessage 节点之间相互通知，使玩家离线
const S2RKickPlayerForceByPidMessage = "S2R_KickPlayerForceByPidMessage" 
// S2RKickPlayerForceByUidMessage 节点之间相互通知，使玩家离线
const S2RKickPlayerForceByUidMessage = "S2R_KickPlayerForceByUidMessage" 
// S2RKickSessionByUidMessage 节点之间相互通知，使会话失效，登录服使用
const S2RKickSessionByUidMessage = "S2R_KickSessionByUidMessage" 
// S2RRemoveUserByUidMessage game通知login，相关用户在game登录了某个角色，login用来移除user
const S2RRemoveUserByUidMessage = "S2R_RemoveUserByUidMessage" 
// R2SKickPlayerForceMessage 节点通知玩家离线结果
const R2SKickPlayerForceMessage = "R2S_KickPlayerForceMessage" 
// R2SSimpleResponseMessage 简单响应
const R2SSimpleResponseMessage = "R2S_SimpleResponseMessage" 
// S2RNotifyPlayerEnterMapMessage 通知，玩家进入某个地图
const S2RNotifyPlayerEnterMapMessage = "S2R_NotifyPlayerEnterMapMessage" 
// R2SReplayPlayerEnterMapMessage 玩家进入某个地图响应
const R2SReplayPlayerEnterMapMessage = "R2S_ReplayPlayerEnterMapMessage" 
// S2RNotifyPlayerLeaveMapMessage 通知，玩家离开某个地图
const S2RNotifyPlayerLeaveMapMessage = "S2R_NotifyPlayerLeaveMapMessage" 
// S2RNotifyPlayerMoveMessage 通知，玩家移动
const S2RNotifyPlayerMoveMessage = "S2R_NotifyPlayerMoveMessage" 
// S2RForwardMessage 将消息转发到玩家所在节点，然后通过玩家的session发送给客户端
const S2RForwardMessage = "S2R_ForwardMessage" 
// S2RNotifyTeamInviteMessage 通知，邀请玩家入队
const S2RNotifyTeamInviteMessage = "S2R_NotifyTeamInviteMessage" 
// S2RResponseTeamInviteMessage 最终响应入队邀请
const S2RResponseTeamInviteMessage = "S2R_ResponseTeamInviteMessage" 
// S2RBroadcastJoinTeamMessage 广播加入队伍
const S2RBroadcastJoinTeamMessage = "S2R_BroadcastJoinTeamMessage" 
// S2RNotifyTeamApplyMessage 通知，玩家申请入队
const S2RNotifyTeamApplyMessage = "S2R_NotifyTeamApplyMessage" 
// S2RResponseTeamApplyMessage 最终响应入队申请
const S2RResponseTeamApplyMessage = "S2R_ResponseTeamApplyMessage" 
// S2RNotifyExitTeamMessage 发送到队长节点，退出队伍
const S2RNotifyExitTeamMessage = "S2R_NotifyExitTeamMessage" 
// S2RBroadcastExitTeamMessage 广播退出队伍
const S2RBroadcastExitTeamMessage = "S2R_BroadcastExitTeamMessage" 
// S2RBroadcastChangeLeaderMessage 广播改变队长
const S2RBroadcastChangeLeaderMessage = "S2R_BroadcastChangeLeaderMessage" 
// S2RBroadcastDisbandTeamMessage 广播解散队伍
const S2RBroadcastDisbandTeamMessage = "S2R_BroadcastDisbandTeamMessage" 
// S2RBroadcastTeamEnterMapMessage 广播队伍切换地图
const S2RBroadcastTeamEnterMapMessage = "S2R_BroadcastTeamEnterMapMessage" 
// S2RBroadcastAcceptTaskMessage 广播领取任务
const S2RBroadcastAcceptTaskMessage = "S2R_BroadcastAcceptTaskMessage" 
// R2SReplayAcceptTaskMessage 广播领取任务响应
const R2SReplayAcceptTaskMessage = "R2S_ReplayAcceptTaskMessage" 
// S2RBroadcastSubmitTaskMessage 广播提交任务
const S2RBroadcastSubmitTaskMessage = "S2R_BroadcastSubmitTaskMessage" 
// R2SReplaySubmitTaskMessage 广播提交任务响应
const R2SReplaySubmitTaskMessage = "R2S_ReplaySubmitTaskMessage" 
