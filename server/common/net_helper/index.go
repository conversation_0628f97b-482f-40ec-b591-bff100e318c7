package net_helper

import (
	"errors"
	"fmt"
	"runtime"
	"strings"
	"sync"
	"world/base/enum/ServerType"
	ut "world/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	"github.com/huyangv/vmqant/registry"
	ants "github.com/panjf2000/ants/v2"
	"github.com/samber/lo"
	"google.golang.org/protobuf/reflect/protoreflect"
)

var (
	app  module.App
	pool *ants.Pool
)

const NOT_FOUND = "nofound"

func Init(mainApp module.App) {
	app = mainApp
	// 创建固定大小的协程池
	var err error
	// PreAlloc预分配内存
	pool, err = ants.NewPool(256, ants.WithPreAlloc(true))
	if err != nil {
		panic(err)
	}

	// 在主程序退出时释放池
	runtime.SetFinalizer(pool, func(p *ants.Pool) {
		p.Release()
	})
}

type rpcCallHelper struct {
	services []*registry.Service
}

func (r *rpcCallHelper) Random() *registry.Node {
	idx := ut.Random(0, len(r.services[0].Nodes)-1)
	return r.services[0].Nodes[idx]
}

func (r *rpcCallHelper) First() *registry.Node {
	return r.services[0].Nodes[0]
}

func (r *rpcCallHelper) ForEach(predicate func(node *registry.Node)) {
	lo.ForEach(r.services[0].Nodes, func(t *registry.Node, i int) {
		predicate(t)
	})
}

// 判断rpc消息的错误是不是没有找到节点
func IsNodeNotFoundError(err error) bool {
	if err == nil {
		return false
	}
	return strings.Contains(err.Error(), NOT_FOUND)
}

// GetServices
/*
 * @description 获取服务
 * @param serverType
 */
func GetServices(serverType string) (*rpcCallHelper, error) {
	services, err := app.Registry().GetService(serverType)
	if err != nil {
		return nil, err
	}
	if len(services) == 0 {
		return nil, errors.New("没有找到服务")
	}
	return &rpcCallHelper{
		services: services,
	}, nil
}

// Invoke
/*
 * @description rpc调用
 * @param sender 发送者
 * @param toNodeId 接收者
 * @param router 路由
 * @param args 参数
 * @return replay 响应，全局都是使用了proto，所以这里是proto的byte数组
 * @return err 错误
 */
func Invoke(sender module.RPCModule, toNodeId string, router string, args ...any) (replay []byte, err error) {
	log.Info("[ Invoke ] 接收方：%s, 路由:%s, 数据:%v", toNodeId, router, args)
	if toNodeId == "" {
		str := fmt.Sprintf("net_helper Invoke [%s] [%v] error: 需要指定节点发送。", router, args)
		log.Error(str)
		return nil, errors.New(str)
	}
	ret, e := sender.Invoke(toNodeId, router, args...)
	if e != "" {
		str := fmt.Sprintf("net_helper Invoke [%s] [%v] error: %s", router, args, e)
		log.Error("这里一般都是参数传递不正确，检查一下rpc接收方法的参数:%s", str)
		return nil, errors.New(str)
	}
	if ret != nil {
		replay, _ = ret.([]byte)
	}
	return replay, nil
}

func AsyncInvoke(sender module.RPCModule, toNodeId string, router string, args ...any) error {
	log.Info("[ AsyncInvoke ] 接收方：%s, 路由:%s, 数据:%v", toNodeId, router, args)
	if toNodeId == "" {
		str := fmt.Sprintf("net_helper AsyncInvoke [%s] [%v] error: 需要指定节点发送。", router, args)
		log.Error(str)
		return errors.New(str)
	}
	e := sender.InvokeNR(toNodeId, router, args...)
	if e != nil {
		str := fmt.Sprintf("net_helper AsyncInvoke [%s] [%v] error: %s", router, args, e.Error())
		log.Error("这里一般都是参数传递不正确，检查一下rpc接收方法的参数:%s", str)
		return errors.New(str)
	}
	return nil
}

func CallMiddle(sender module.RPCModule, router string, msg protoreflect.ProtoMessage) (replay []byte) {
	callHelper, err := GetServices(ServerType.Mid)
	if err != nil {
		panic(err)
	}
	bytes, err := Invoke(sender, callHelper.First().Id, router, msg)
	if err != nil {
		panic(err)
	}
	return bytes
}

// CallGameSync
/*
 * @description
 * @param sender
 * @param router
 * @param args
 */
func CallGameSync(sender module.RPCModule, router string, args ...any) [][]byte {
	return CallAll(ServerType.Game, sender, router, args...)
}

func CallLoginSync(sender module.RPCModule, router string, args ...any) [][]byte {
	return CallAll(ServerType.Login, sender, router, args...)
}

func CallAll(typ string, sender module.RPCModule, router string, args ...any) [][]byte {
	callHelper, err := GetServices(typ)
	if err != nil {
		panic(err)
	}
	replay := make([][]byte, 0)
	var wg sync.WaitGroup
	callHelper.ForEach(func(node *registry.Node) {
		wg.Add(1)
		err := pool.Submit(func() {
			defer wg.Done()
			r, err := Invoke(sender, node.Id, router, args...)
			if err != nil {
				log.Error("CallAll 调用失败 - 节点: %s, 路由: %s, 错误: %s",
					node.Id, router, err.Error())
			}
			replay = append(replay, r)
		})
		if err != nil {
			wg.Done()
			log.Error("提交任务到协程池失败: %v", err)
		}
	})
	wg.Wait()
	return replay
}
