// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v6.30.1
// source: pbCross/mail.proto

package pbCross

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	pbBase "world/common/pbBase"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are messages.
type S2R_NotifyNewMailMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mail *pbBase.Mail `protobuf:"bytes,1,opt,name=mail,proto3" json:"mail,omitempty"` //邮件数据
}

func (x *S2R_NotifyNewMailMessage) Reset() {
	*x = S2R_NotifyNewMailMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_mail_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2R_NotifyNewMailMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2R_NotifyNewMailMessage) ProtoMessage() {}

func (x *S2R_NotifyNewMailMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_mail_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2R_NotifyNewMailMessage.ProtoReflect.Descriptor instead.
func (*S2R_NotifyNewMailMessage) Descriptor() ([]byte, []int) {
	return file_pbCross_mail_proto_rawDescGZIP(), []int{0}
}

func (x *S2R_NotifyNewMailMessage) GetMail() *pbBase.Mail {
	if x != nil {
		return x.Mail
	}
	return nil
}

var File_pbCross_mail_proto protoreflect.FileDescriptor

var file_pbCross_mail_proto_rawDesc = []byte{
	0x0a, 0x12, 0x70, 0x62, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x2f, 0x6d, 0x61, 0x69, 0x6c, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x70, 0x62, 0x42,
	0x61, 0x73, 0x65, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x3b, 0x0a, 0x18, 0x53, 0x32, 0x52, 0x5f, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x4e, 0x65,
	0x77, 0x4d, 0x61, 0x69, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1f, 0x0a, 0x04,
	0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x4d, 0x61, 0x69, 0x6c, 0x52, 0x04, 0x6d, 0x61, 0x69, 0x6c, 0x42, 0x1e, 0x5a,
	0x1c, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62,
	0x43, 0x72, 0x6f, 0x73, 0x73, 0x3b, 0x70, 0x62, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbCross_mail_proto_rawDescOnce sync.Once
	file_pbCross_mail_proto_rawDescData = file_pbCross_mail_proto_rawDesc
)

func file_pbCross_mail_proto_rawDescGZIP() []byte {
	file_pbCross_mail_proto_rawDescOnce.Do(func() {
		file_pbCross_mail_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbCross_mail_proto_rawDescData)
	})
	return file_pbCross_mail_proto_rawDescData
}

var file_pbCross_mail_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbCross_mail_proto_goTypes = []interface{}{
	(*S2R_NotifyNewMailMessage)(nil), // 0: proto.S2R_NotifyNewMailMessage
	(*pbBase.Mail)(nil),              // 1: proto.Mail
}
var file_pbCross_mail_proto_depIdxs = []int32{
	1, // 0: proto.S2R_NotifyNewMailMessage.mail:type_name -> proto.Mail
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_pbCross_mail_proto_init() }
func file_pbCross_mail_proto_init() {
	if File_pbCross_mail_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbCross_mail_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2R_NotifyNewMailMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbCross_mail_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbCross_mail_proto_goTypes,
		DependencyIndexes: file_pbCross_mail_proto_depIdxs,
		MessageInfos:      file_pbCross_mail_proto_msgTypes,
	}.Build()
	File_pbCross_mail_proto = out.File
	file_pbCross_mail_proto_rawDesc = nil
	file_pbCross_mail_proto_goTypes = nil
	file_pbCross_mail_proto_depIdxs = nil
}
