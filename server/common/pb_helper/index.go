package pb_helper

import (
	"google.golang.org/protobuf/proto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	"strconv"
	"strings"
	"world/common/ecode"
)

// ProtoMarshalForce 强制序列化proto对象 不返回错误
func ProtoMarshalForce(m protoreflect.ProtoMessage) (bytes []byte) {
	bytes, e := proto.Marshal(m)
	if e != nil {
		return nil
	}
	return bytes
}

// ProtoMarshal 序列化proto对象
func ProtoMarshal(m protoreflect.ProtoMessage) (bytes []byte, err string) {
	bytes, e := proto.Marshal(m)
	if e != nil {
		return nil, ecode.UNKNOWN.String()
	}
	return bytes, ""
}

func ProtoUnMarshal(bytes []byte, m protoreflect.ProtoMessage) error {
	return proto.Unmarshal(bytes, m)
}

func Int32(val interface{}) int32 {
	switch reply := val.(type) {
	case float32:
		return int32(reply)
	case float64:
		return int32(reply)
	case int32:
		return reply
	case int64:
		return int32(reply)
	case int:
		return int32(reply)
	case string:
		s, _ := strconv.Atoi(reply)
		return int32(s)
	case nil:
		return 0
	}
	return 0
}

func Int64(val interface{}) int64 {
	switch reply := val.(type) {
	case float32:
		return int64(reply)
	case float64:
		return int64(reply)
	case int32:
		return int64(reply)
	case int64:
		return reply
	case int:
		return int64(reply)
	case string:
		s, _ := strconv.Atoi(reply)
		return int64(s)
	case nil:
		return 0
	}
	return 0
}

func String(val interface{}) string {
	switch reply := val.(type) {
	case string:
		return reply
	case float64:
		return Itoa(reply)
	case int:
		return Itoa(reply)
	case bool:
		return strconv.FormatBool(reply)
	case nil:
		return ""
	}
	return ""
}

func Bool(val interface{}) bool {
	switch reply := val.(type) {
	case bool:
		return reply
	case nil:
		return false
	}
	return false
}

func Int(val interface{}) int {
	switch reply := val.(type) {
	case float32:
		return int(reply)
	case float64:
		return int(reply)
	case int32:
		return int(reply)
	case int64:
		return int(reply)
	case int:
		return reply
	case string:
		s, _ := strconv.Atoi(reply)
		return s
	case nil:
		return 0
	}
	return 0
}

// int转字符串
func Itoa(val interface{}) string {
	return strconv.Itoa(Int(val))
}

// string转int
func Atoi(val interface{}) int {
	r, _ := strconv.Atoi(String(val))
	return r
}

// string转Float
func Atof(val interface{}) float64 {
	r, _ := strconv.ParseFloat(String(val), 64)
	return r
}

// int数组转int32
func IntArrayToInt32(arr []int) []int32 {
	ret := []int32{}
	for _, v := range arr {
		ret = append(ret, Int32(v))
	}
	return ret
}

// 转int32数组
func Int32Array(val interface{}) []int32 {
	switch reply := val.(type) {
	case []int:
		arr := []int32{}
		for _, v := range reply {
			arr = append(arr, Int32(v))
		}
		return arr
	case []interface{}:
		arr := []int32{}
		for _, v := range reply {
			arr = append(arr, Int32(v))
		}
		return arr
	case nil:
		return []int32{}
	}
	return []int32{}
}

// 将一个字符串拆分为数组
func StringToInts(val string, separator string) []int32 {
	if val == "" {
		return []int32{}
	}
	arr := strings.Split(val, separator)
	ret := []int32{}
	for _, s := range arr {
		ret = append(ret, int32(Atoi(s)))
	}
	return ret
}

func AddFlags(flags ...int32) int32 {
	data := int32(0)
	for _, v := range flags {
		data |= (1 << v)
	}
	return data
}

func HasFlag(data int32, flag int32) bool {
	return data&(1<<flag) == (1 << flag)
}
