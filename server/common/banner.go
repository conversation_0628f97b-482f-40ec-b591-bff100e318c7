package comm

import "github.com/huyangv/vmqant/log"

const banner = "" +
	"\n\t     ██╗██╗██╗   ██╗    ██╗    ██╗ █████╗ ███╗   ██╗    ███╗   ███╗██╗   ██╗" +
	"\n\t     ██║██║██║   ██║    ██║    ██║██╔══██╗████╗  ██║    ████╗ ████║██║   ██║" +
	"\n\t     ██║██║██║   ██║    ██║ █╗ ██║███████║██╔██╗ ██║    ██╔████╔██║██║   ██║" +
	"\n\t██   ██║██║██║   ██║    ██║███╗██║██╔══██║██║╚██╗██║    ██║╚██╔╝██║██║   ██║" +
	"\n\t╚█████╔╝██║╚██████╔╝    ╚███╔███╔╝██║  ██║██║ ╚████║    ██║ ╚═╝ ██║╚██████╔╝" +
	"\n\t ╚════╝ ╚═╝ ╚═════╝      ╚══╝╚══╝ ╚═╝  ╚═╝╚═╝  ╚═══╝    ╚═╝     ╚═╝ ╚═════╝ "

func PrintBanner() {
	log.Info("\n%s", banner)
}
