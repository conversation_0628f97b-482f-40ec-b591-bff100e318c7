package db

import (
	"fmt"
	"world/base/env"
)

/** 下面是redis lock **/

// 玩家锁
func RedisLockUid(uid string) string { return fmt.Sprintf("UserLockKey_%s", uid) }

// 清理redis缓存的锁
func RedisLockClearCache() string { return fmt.Sprintf("UserLockClearCache_%d", env.GetSid()) }

/** 下面是redis key **/

// 玩家当前所在节点hset(game服)
func RedisKeySessionId() string     { return fmt.Sprintf("SessionHashKey_%d", env.GetSid()) }
func RedisKeySessionGameId() string { return fmt.Sprintf("SessionHashKey_GameId_%d", env.GetSid()) }

// 玩家名称
func RedisKeyName() string { return fmt.Sprintf("SessionHashKey_Name_%d", env.GetSid()) }

// 服务器宝箱限制
func RedisKeyServerChestLimit(chestId int) string {
	return fmt.Sprintf("ServerChestLimit_%d_%d", env.GetSid(), chestId)
}

// 开启宝箱锁
func RedisKeyChestOpenLock(chestId int) string {
	return fmt.Sprintf("ChestOpenLock%d_%d", env.GetSid(), chestId)
}

// 邮件缓存
func RedisKeyMailCache(gameId int) string {
	return fmt.Sprintf("MailCache_%d_%d", env.GetSid(), gameId)
}
