package db

import (
	"go.mongodb.org/mongo-driver/mongo"
)

var tables = make([]*TableDef, 0)

// Def 创建表结构
//
// Parameters:
//   - name string 表名
//
// Returns:
//   - *TableDef
func Def(name string) *TableDef {
	t := NewTableDef(name)
	tables = append(tables, t)
	return t
}

type TableDef struct {
	name         string          // 表名称
	index        map[string]bool // 索引信息,放入map的字段均会被创建索引，如果是值是true则该字段创建为唯一索引
	complexIndex []IndexDef      // 复合索引
}

// Schema 配置字段信息
//
// Parameters:
//   - imap map[string]bool 字段名=>是否唯一索引
//
// Returns:
//   - *TableDef
func (t *TableDef) Schema(imap map[string]bool) *TableDef {
	t.index = imap
	return t
}

// Index 配置索引信息
//
// Parameters:
//   - complexIndex ...IndexDef
//
// Returns:
//   - *TableDef
func (t *TableDef) Index(complexIndex ...IndexDef) *TableDef {
	t.complexIndex = complexIndex
	return t
}

func (t *TableDef) GetIndex() map[string]bool {
	return t.index
}

// Get 获取表定义的名称
func (t *TableDef) Name() string {
	return t.name
}

func (t *TableDef) GetCollection() *mongo.Collection {
	return GetCollection(t)
}

func NewTableDef(name string) *TableDef {
	if name == "" {
		panic("table name 不能是空值")
	}
	return &TableDef{name: name}
}
