package db

import (
	"context"
	"errors"
	"math/rand"
	"strings"
	"time"

	"github.com/huyangv/vmqant/log"
	"github.com/redis/go-redis/v9"
)

// redis document: https://redis.uptrace.dev/zh/guide/go-redis.html

var (
	rdb *redis.Client = nil
)

// GetRedis 获取redis连接对象
func GetRedis() *redis.Client {
	return rdb
}

// InitRedis  初始化redis
//
// Parameters:
//   - url string
//   - password string
func InitRedis(url string, password string) {
	if url == "" {
		panic(" 错误的redis连接地址! ")
	}
	if rdb != nil {
		return
	}
	options := &redis.Options{
		Addr: url,
	}
	if password != "" {
		options.Password = password
	}
	rdb = redis.NewClient(options)
	// 测试连接是否成功
	ctx := context.Background()
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		log.Error("redis连接失败: %v", err)
		panic("redis连接失败: " + err.Error())
	}

	log.Info("redis init success! " + url)
}

// RedisLock 表示一个Redis分布式锁
type RedisLock struct {
	key        string        // 锁的键名
	value      string        // 锁的值（通常是唯一标识，用于安全释放）
	expiration time.Duration // 锁的过期时间
}

// NewRedisLock 创建一个新的Redis锁
func NewRedisLock(key string, expiration time.Duration) *RedisLock {
	// 生成一个唯一的锁值，可以使用UUID或其他唯一标识
	value := "lock-" + time.Now().String()

	return &RedisLock{
		key:        key,
		value:      value,
		expiration: expiration,
	}
}

// TryLock 尝试获取锁，成功返回true，失败返回false
func (lock *RedisLock) TryLock(ctx context.Context) (bool, error) {
	// 使用SET NX EX命令原子性地设置锁
	result, err := rdb.SetNX(ctx, lock.key, lock.value, lock.expiration).Result()
	if err != nil {
		return false, err
	}
	return result, nil
}

// Lock 阻塞直到获取锁或超时
func (lock *RedisLock) Lock(ctx context.Context, timeout time.Duration) error {
	deadline := time.Now().Add(timeout)
	// 基础间隔50ms
	baseInterval := 50 * time.Millisecond
	// 最大间隔500ms
	maxInterval := 500 * time.Millisecond
	currentInterval := baseInterval

	for time.Now().Before(deadline) {
		acquired, err := lock.TryLock(ctx)
		if err != nil {
			return err
		}
		if acquired {
			return nil
		}
		// 指数退避 + 随机抖动
		jitter := time.Duration(rand.Intn(int(currentInterval / 2)))
		sleepTime := currentInterval + jitter
		time.Sleep(sleepTime)
		// 指数增长，但不超过最大值
		currentInterval = time.Duration(float64(currentInterval) * 1.5)
		if currentInterval > maxInterval {
			currentInterval = maxInterval
		}
	}

	return errors.New("获取锁超时")
}

// Unlock 释放锁（只有锁的持有者才能释放锁，使用Lua脚本保证原子性）
func (lock *RedisLock) Unlock(ctx context.Context) (bool, error) {
	// 使用Lua脚本确保只释放自己持有的锁
	script := `
    if redis.call("GET", KEYS[1]) == ARGV[1] then
        return redis.call("DEL", KEYS[1])
    else
        return 0
    end
    `

	result, err := rdb.Eval(ctx, script, []string{lock.key}, lock.value).Int64()
	if err != nil {
		return false, err
	}

	return result == 1, nil
}

// RefreshLock 只有锁的持有者才能操作，刷新锁的过期时间
func (lock *RedisLock) RefreshLock(ctx context.Context) (bool, error) {
	script := `
    if redis.call("GET", KEYS[1]) == ARGV[1] then
        return redis.call("EXPIRE", KEYS[1], ARGV[2])
    else
        return 0
    end
    `

	seconds := int(lock.expiration / time.Second)
	result, err := rdb.Eval(ctx, script, []string{lock.key}, lock.value, seconds).Int64()
	if err != nil {
		return false, err
	}

	return result == 1, nil
}

// IsRedisNetworkError 判断是否为网络相关错误
func IsRedisNetworkError(err error) bool {
	if err == nil {
		return false
	}
	errStr := err.Error()
	return strings.Contains(errStr, "connection") ||
		strings.Contains(errStr, "timeout") ||
		strings.Contains(errStr, "network") ||
		strings.Contains(errStr, "dial")
}
