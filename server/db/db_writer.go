package db

import (
	"context"
	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
	"go.mongodb.org/mongo-driver/mongo"
)

type MongoDBWriter interface {
	GetDefine() *TableDef
	BulkSaveOperation() *mongo.UpdateOneModel
	GetUnique() string
}

// 暂存  没使用
var dbManager *Manager

func GetBulkMgr() *Manager {
	if dbManager == nil {
		dbManager = &Manager{
			data: make(map[*TableDef]map[string]MongoDBWriter),
		}
	}
	return dbManager
}

type Manager struct {
	data map[*TableDef]map[string]MongoDBWriter
	deadlock.RWMutex
}

func (m *Manager) Run() {
	m.Write()
}

// Put
/*
 * @description 放入
 * @param writer
 */
func (m *Manager) Put(writer MongoDBWriter) {
	define := writer.GetDefine()
	m.RLock()
	defer m.RUnlock()
	writers, ok := m.data[define]
	if !ok {
		writers = make(map[string]MongoDBWriter, 0)
		m.data[define] = writers
	}
	writers[writer.GetUnique()] = writer
}

// Remove
/*
 * @description 移除
 * @param writer
 */
func (m *Manager) Remove(writer MongoDBWriter) {
	define := writer.GetDefine()
	m.RLock()
	defer m.RUnlock()
	writers, ok := m.data[define]
	if ok {
		delete(writers, writer.GetUnique())
	}
}

// Write
/*
 * @description 批量写
 */
func (m *Manager) Write() {
	m.Lock()
	defer m.Unlock()
	for def, writers := range m.data {
		operations := make([]mongo.WriteModel, 0)
		for _, writer := range writers {
			operations = append(operations, writer.BulkSaveOperation())
		}
		_, err := def.GetCollection().BulkWrite(context.Background(), operations)
		if err != nil {
			log.Error("批量写入出错啦:%s", err.Error())
		}
	}
}
