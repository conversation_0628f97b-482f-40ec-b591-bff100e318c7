package h

import (
	"github.com/spf13/cast"
)

const (
	KickPlayer = "kickPlayer"
)

func RpcParamsSort(args ...any) map[string]any {
	data := make(map[string]any)
	for i, arg := range args {
		data[cast.ToString(i)] = arg
	}
	return data
}

//func RpcParams(args ...any) map[string]any {
//	if len(args)%2 != 0 {
//		log.Warning("RpcParams args length is not even.")
//	}
//	data := make(map[string]any)
//	for i := 0; i < len(args); {
//		key := args[i+1].(string)
//		i++
//		if i >= len(args) {
//			break
//		}
//		val := args[i+1]
//		i++
//		data[key] = val
//	}
//	return data
//}
