package h

const (
	TokenCheckErr       = -1022 // token验证失败
	FormCheckErr        = -1023 // 参数校验失败
	JsonDataError       = -1024 // Content-Type 是 application/json 但是，request数据不存在
	JsonDataDecodeError = -1025 // Content-Type 是 application/json 但是，request数据解析失败
)

const (
	OK        = 200
	Error     = 500
	NONE_PAGE = 404
)
const (
	ERROR   = -1
	SUCCESS = 0
)

// ResponseConstruct http请求返回
func ResponseConstruct(code int, data interface{}, notify string) map[string]interface{} {
	return map[string]interface{}{
		"code":   code,
		"data":   data,
		"notify": notify,
	}
}

// ResponseSuccessNoDataNoDesc  http成功 但无数据 无提示返回
func ResponseSuccessNoDataNoDesc() (map[string]interface{}, string) {
	return ResponseConstruct(SUCCESS, nil, ""), ""
}

// ResponseSuccessWithDataWithDesc http成功 有数据 有提示返回
func ResponseSuccessWithDataWithDesc(data interface{}, desc string) (map[string]interface{}, string) {
	return ResponseConstruct(SUCCESS, data, desc), ""
}

// ResponseSuccessWithDataNoDesc http成功 有数据 无提示返回
func ResponseSuccessWithDataNoDesc(data interface{}) (map[string]interface{}, string) {
	return ResponseConstruct(SUCCESS, data, ""), ""
}

// ResponseErrorNoDataNoDesc http失败 无数据 无提示返回
func ResponseErrorNoDataNoDesc() (map[string]interface{}, string) {
	return ResponseConstruct(ERROR, "", ""), ""
}

// ResponseErrorNoDataWithDesc http失败 无数据 有提示返回
func ResponseErrorNoDataWithDesc(desc string) (map[string]interface{}, string) {
	return ResponseConstruct(ERROR, "", desc), ""
}

// ResponseErrorWithDataNoDesc http失败 有数据 无提示返回
func ResponseErrorWithDataNoDesc(data interface{}) (map[string]interface{}, string) {
	return ResponseConstruct(ERROR, data, ""), ""
}

// ResponseErrorWithDataWithDesc http失败 有数据 有提示返回
func ResponseErrorWithDataWithDesc(data interface{}, desc string) (map[string]interface{}, string) {
	return ResponseConstruct(ERROR, data, desc), ""
}

// ResponseSuccessNoDataWithDesc http成功 无数据 有提示返回
func ResponseSuccessNoDataWithDesc(desc string) (map[string]interface{}, string) {
	return ResponseConstruct(SUCCESS, nil, desc), ""
}
