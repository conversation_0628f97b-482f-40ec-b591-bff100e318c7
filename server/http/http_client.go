package http

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/huyangv/vmqant/log"
	logs "github.com/huyangv/vmqant/log/beego"
	mqrpc "github.com/huyangv/vmqant/rpc"
	"github.com/spf13/cast"
	"math"
	"net/http"
	"net/url"
	"runtime"
	"strings"
	"sync"
	"time"
	"world/http/h"
	ut "world/utils"
)

const green = "\033[97;42m"
const reset = "\u001B[0m"

var _lock sync.Once
var _httpClient *Client = nil

func GetHttpClient() *Client {
	_lock.Do(func() {
		_httpClient = &Client{
			engine: gin.Default(),
			module: nil,
		}
		_httpClient.initLogger()
		_httpClient.engine.Use(_httpClient.cross)
		_httpClient.engine.Use(_httpClient.middleware)
	})
	return _httpClient
}

type Client struct {
	engine *gin.Engine
	module *Http
}

func (c *Client) Run(addr ...string) {
	root := fmt.Sprintf("%s%s", ut.WorkDir(), "/bin/dist")
	if !ut.IsEmpty(root) {
		c.engine.Static("/", root)
	}
	err := c.engine.Run(addr...)
	if err != nil {
		panic(err)
	}
}

func (c *Client) cross(ctx *gin.Context) {
	ctx.Header("Access-Control-Allow-Origin", "*")
	ctx.Header("Access-Control-Allow-Methods", "POST, GET")
	ctx.Header("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, Authorization")
	ctx.Header("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Cache-Control, Content-Language, Content-Type")
	ctx.Header("Access-Control-Allow-Credentials", "true")
	if ctx.Request.Method == "OPTIONS" {
		ctx.AbortWithStatus(204)
		return
	}
	ctx.Next()
}
func (c *Client) middleware(ctx *gin.Context) {
	reqUri := ctx.Request.RequestURI
	clientIP := ctx.ClientIP()
	c.DecodeRequest(ctx)

	writer := responseWriter{
		ctx.Writer,
		bytes.NewBuffer([]byte{}),
	}
	if i := strings.Index(reqUri, "?"); i != -1 {
		reqUri = reqUri[:i]
	}
	ctx.Writer = writer
	startTime := time.Now()
	ctx.Next()
	latencyTime := time.Now().Sub(startTime)
	islog := false
	value, exists := ctx.Get("@log")
	if !exists {
		islog = true
	} else {
		islog = cast.ToBool(value)
	}
	if ctx.FullPath() == "/*filepath" {
		islog = false
	}
	if islog {
		log.Info("[O] [%13v] [%s] %s[%s]%s %s", latencyTime, clientIP, green, reqUri, reset, writer.b.String())
	}
}

func (c *Client) DecodeRequest(ctx *gin.Context) {
	contentType := ctx.Request.Header.Get("Content-Type")
	if strings.HasPrefix(contentType, binding.MIMEMultipartPOSTForm) {
		contentType = binding.MIMEMultipartPOSTForm
	}
	switch contentType {
	case "":
		c.DecodeContentNone(ctx)
	case binding.MIMEJSON:
		c.DecodeContentApplicationJson(ctx)
	case binding.MIMEMultipartPOSTForm:
		c.DecodeContentMultipartPOSTForm(ctx)
	case binding.MIMEPOSTForm:
		c.DecodeContentMultipartPOSTForm(ctx)
	default:
		ctx.JSON(h.OK, map[string]interface{}{
			"code":   h.JsonDataError,
			"notify": "unsupported request Content-Type",
		})
		log.Info("unsupported request Content-Type: %s", contentType)
		ctx.Abort()
	}
}

// DecodeContentNone 解析 get 基本请求
func (c *Client) DecodeContentNone(ctx *gin.Context) {
	reqMethod := ctx.Request.Method
	reqUri := ctx.Request.RequestURI
	clientIP := ctx.ClientIP()
	if reqMethod == "GET" {
		c.CommonSetRequestParams(EncodeUrlValues(ctx.Request.URL.Query()), ctx)
	}
	if reqMethod == "POST" {
		//log.Error("reqMethod = POST, but Content-Type is none")
		// 中断请求
		//ctx.Abort()
		log.Info("[I] [%13s] [%s] %s[%s]%s %s", reqMethod, clientIP, green, reqUri, reset, "{}")
	}
}

// DecodeContentApplicationJson 解析 application/json
func (c *Client) DecodeContentApplicationJson(ctx *gin.Context) {
	data, err := ctx.GetRawData()
	if err != nil {
		ctx.JSON(h.Error, map[string]interface{}{
			"code": h.JsonDataError,
		})
		log.Info("JsonDataError: %s", err.Error())
		ctx.Abort()
		return
	}
	tm := make(map[string]interface{})
	err = json.Unmarshal(data, &tm)
	if err != nil {
		ctx.JSON(h.Error, map[string]interface{}{
			"code": h.JsonDataDecodeError,
		})
		log.Info("JsonDataDecodeError: %s", err.Error())
		ctx.Abort()
		return
	}
	c.CommonSetRequestParams(tm, ctx)
}

// DecodeContentMultipartPOSTForm  解析 multipart/form-data
func (c *Client) DecodeContentMultipartPOSTForm(ctx *gin.Context) {
	err := ctx.Request.ParseMultipartForm(c.engine.MaxMultipartMemory)
	if err != nil && !errors.Is(err, http.ErrNotMultipart) {
		ctx.Abort()
		log.Error("error on parse multipart form array: %v", err)
	}
	c.CommonSetRequestParams(EncodeUrlValues(ctx.Request.PostForm), ctx)
}

// CommonSetRequestParams 参数解析后直接放入上下文
func (c *Client) CommonSetRequestParams(tm map[string]interface{}, ctx *gin.Context) {
	ctx.Set("@LEN", len(tm))
	for k, v := range tm {
		ctx.Set(k, v)
	}
	reqMethod := ctx.Request.Method
	reqUri := ctx.Request.RequestURI
	clientIP := ctx.ClientIP()
	if i := strings.Index(reqUri, "?"); i != -1 {
		reqUri = reqUri[:i]
	}
	token := ctx.Request.Header.Get("Authorization")
	if !ut.IsEmpty(token) {
		ctx.Set("@token", token)
	}
	if ctx.FullPath() == "/*filepath" {
		return
	}
	log.Info("[I] [%13s] [%s] %s[%s]%s %s", reqMethod, clientIP, green, reqUri, reset, EncodeUrlValues2Json(tm))
}

func (c *Client) Get(path string, handlers ...gin.HandlerFunc) *Client {
	c.engine.GET(path, handlers...)
	return c
}
func (c *Client) Post(path string, handlers ...gin.HandlerFunc) *Client {
	c.engine.POST(path, handlers...)
	return c
}

func (c *Client) initLogger() {
	const withStack = true
	logs.FormatTime = ""
	logs.WriteLogFileColor = true
	log.LogBeego().SetFormatFunc(func(when time.Time, span *logs.BeegoTraceSpan, logLevel int, msg string, v ...interface{}) (string, error) {
		timestr := when.Format("2006/01/02 - 15:04:05")
		color := reset
		if logLevel == logs.LevelAlert {
			color = "\u001B[97;42m" //绿色背景 白字
		}
		if logLevel == logs.LevelError {
			color = "\u001B[97;41m" // 红色背景 白字
		}
		if logLevel == logs.LevelDebug {
			color = "\u001B[97;45m" // 浅紫色背景 白字
		}
		if logLevel == logs.LevelWarning {
			color = "\u001B[97;43m" // 黄色背景 白字
		}
		msg = fmt.Sprintf(msg, v...)
		if withStack {
			_, file, line, _ := runtime.Caller(4)
			i := math.Max(cast.ToFloat64(strings.LastIndex(file, "/"))+1, 0)
			msg = fmt.Sprintf("%s%-22s %s %s: %s %s", reset, fmt.Sprintf("%s:%d", file[cast.ToInt(i):], line), color, timestr, msg, reset)
		} else {
			msg = fmt.Sprintf("%s %s: %s %s", color, timestr, msg, reset)
		}
		return msg, nil
	})
}

func (c *Client) setModule(module *Http) *Client {
	c.module = module
	return c
}

func (c *Client) rpcCall(w http.ResponseWriter, targetServer string, topic string, params mqrpc.ParamOption) (body map[string]interface{}) {
	ctx, cancel := context.WithTimeout(context.TODO(), time.Second*3)
	defer cancel()
	http := c.module
	if http == nil {
		log.Error("http module is nil")
		return nil
	}
	serialized := make([]interface{}, len(params()))
	for i, v := range params() {
		serialized[i], _ = json.Marshal(v)
	}
	body, e := mqrpc.InterfaceMap(http.Call(
		ctx,
		targetServer,
		"/"+topic,
		func() []interface{} {
			return serialized
		},
	))
	if e != nil {
		log.Error("http rpcCall error : %v", e.Error())
	}
	return
}

type responseWriter struct {
	gin.ResponseWriter
	b *bytes.Buffer
}

func (w responseWriter) Write(b []byte) (int, error) {
	//向一个bytes.buffer中写一份数据来为获取body使用
	w.b.Write(b)
	//完成gin.Context.Writer.Write()原有功能
	return w.ResponseWriter.Write(b)
}

func EncodeUrlValues(uv url.Values) (p map[string]interface{}) {
	p = make(map[string]interface{})
	for s, i := range uv {
		if i == nil || len(i) == 0 {
			p[s] = ""
			continue
		}
		p[s] = i[0]
	}
	return
}

func EncodeUrlValues2Json(p map[string]interface{}) string {
	if p == nil {
		return ""
	}
	marshal, err := json.Marshal(p)
	if err != nil {
		return ""
	}
	return string(marshal)
}
