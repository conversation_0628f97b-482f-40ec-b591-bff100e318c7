{"setting": {"keyCompare": "global=gl,inland=in"}, "list": {"test": {"game": [{"name": "[国内]列车测试服务器", "host": "************", "pem": "tw_hotel.pem", "compress": "bin", "compressIgnore": "*.go logs dist bi", "build": "GOOS=linux GOARCH=amd64 go build", "directory": "/root/train_test", "reload": "pm2 reload 7", "check": "pm2 show 7 |grep 'status' |awk '{print $4}'"}]}, "global": {"http": [{"name": "[海外]列车工具服", "host": "*************", "pem": "tw_train.pem", "compress": "bin", "compressIgnore": "*.go logs dist bi", "build": "GOOS=linux GOARCH=amd64 go build", "directory": "/data/train", "reload": "pm2 reload 1", "check": "pm2 show 1 |grep 'status' |awk '{print $4}'"}], "game": [{"name": "[海外]列车游戏服1", "host": "*************", "pem": "tw_train.pem", "compress": "bin", "compressIgnore": "*.go logs dist bi", "build": "GOOS=linux GOARCH=amd64 go build", "directory": "/data/train", "reload": "pm2 reload 0", "check": "pm2 show 0 |grep 'status' |awk '{print $4}'"}, {"name": "[海外]列车游戏服2", "host": "*************", "pem": "tw_train.pem", "compress": "bin", "compressIgnore": "*.go logs dist bi", "build": "GOOS=linux GOARCH=amd64 go build", "directory": "/data/train", "reload": "pm2 reload 0", "check": "pm2 show 0 |grep 'status' |awk '{print $4}'"}], "gate": [{"name": "[海外]列车网关1", "host": "*************", "pem": "tw_train.pem", "compress": "bin", "compressIgnore": "*.go logs dist bi", "build": "GOOS=linux GOARCH=amd64 go build", "directory": "/data/train", "reload": "pm2 reload 0", "check": "pm2 show 0 |grep 'status' |awk '{print $4}'"}], "login": [{"name": "[海外]列车登录服1", "host": "***************", "pem": "tw_train.pem", "compress": "bin", "compressIgnore": "*.go logs dist bi", "build": "GOOS=linux GOARCH=amd64 go build", "directory": "/data/train", "reload": "pm2 reload 0", "check": "pm2 show 0 |grep 'status' |awk '{print $4}'"}]}}}