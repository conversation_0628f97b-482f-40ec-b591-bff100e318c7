# 项目架构设计文档

## 1. 分布式架构设计

### 1.1 NATS+Consul高可用架构

项目采用NATS作为消息队列和RPC通信框架，Consul作为服务注册与发现中心，构建了一个高可用的分布式游戏服务器架构。

**核心实现**：
```go
// 初始化Consul服务注册
rs := consul.NewRegistry(
    registry.Addrs(env.GetConsulUrl()),
    registry.Timeout(time.Second*3),
)
// 初始化NATS连接
nc, err := nats.Connect(env.GetNatsUrl())
if err != nil {
    panic(err)
}
// 创建应用实例
app := mqant.CreateApp(
    module.ProcessID(env.GetServerTypeStr()),
    module.Debug(env.IsDebug()),
    module.Nats(nc),                        //指定nats rpc
    module.Registry(rs),                    //指定服务发现
    module.RegisterTTL(10*time.Second),     //TTL指定注册信息存在时间
    module.RegisterInterval(5*time.Second), //服务重新注册的时间间隔
    module.RPCExpired(time.Duration(lo.If(env.IsDebug(), 60).Else(3))*time.Second), // RPC调用超时时间
)
```

**优势**：
1. **动态扩展**：服务可以根据负载动态增减节点
2. **自动故障转移**：节点故障时自动切换到健康节点
3. **资源优化**：按需分配资源，降低服务器成本
4. **负载均衡**：请求可以分散到多个节点处理

**挑战**：
1. **通信复杂度增加**：节点间需要通过RPC通信，增加了开发复杂度
2. **数据一致性**：分布式环境下需要特别关注数据一致性问题

### 1.2 服务模块划分

项目按功能划分为多个独立的服务模块，每个模块可以独立部署和扩展：

1. **Gate模块**：网关服务，处理客户端连接和消息路由
2. **Login模块**：登录服务，处理用户认证和区服选择
3. **Game模块**：游戏逻辑服务，处理核心游戏功能
4. **Http模块**：HTTP服务，提供Web API接口
5. **Middle模块**：中间件服务，处理跨服务通信

**模块初始化示例**：
```go
// 按需启动模块
runMods := make([]module.Module, 0)
// game模块
if env.GetMark()&ServerType.MarkGame > 0 {
    gameMod = game.Module()
    runMods = append(runMods, gameMod)
}
// login模块
if env.GetMark()&ServerType.MarkLogin > 0 {
    runMods = append(runMods, login.Module())
}
// gate模块
if env.GetMark()&ServerType.MarkGate > 0 {
    runMods = append(runMods, mgate.Module())
}
// http模块
if env.GetMark()&ServerType.MarkHttp > 0 {
    runMods = append(runMods, http.Module())
}
// mid模块
if env.GetMark()&ServerType.MarkMid > 0 {
    runMods = append(runMods, middle.Module())
}
```

## 2. 消息路由与转发机制

### 2.1 Gate模块消息路由

Gate模块是客户端与服务器通信的桥梁，负责接收客户端消息并转发到对应的服务模块。

**路由实现**：
```go
func (g *Gate) OnRoute(session gate.Session, topic string, msg []byte) (bool, interface{}, error) {
    topics := strings.Split(topic, "/")
    if len(topics) != 3 {
        // 正确格式为:module/func/reqId"
        return true, nil, errors.Errorf("OnRoute topic format error %s %s", session.GetUserID(), topic)
    }
    
    targetModule := topics[0]  // 目标模块
    msgId := topics[1]         // 消息ID
    
    if targetModule == ServerType.Gate {
        return g.handleGateMsg(msgId, session)
    }
    
    // 设置RPC参数
    var ArgsType = make([]string, 2)
    var args = make([][]byte, 2)
    args[0], err = session.Serializable()
    ArgsType[0] = gate.RPCParamSessionType
    ArgsType[1] = argsutil.BYTES
    args[1] = msg
    
    // 获取目标服务节点并转发消息
    serverSession, err := g.getServer(session, targetModule)
    // ...
    result, e := serverSession.CallArgs(ctx, msgId, ArgsType, args)
    // ...
}
```

**节点选择策略**：
- 过滤节点版本：确保客户端版本兼容性
- 过滤区服ID：Game服务必须匹配指定区服
- 过滤节点状态：排除离线和准备下线的节点
- 优先选择已绑定的节点：保持会话连续性
- 随机选择可用节点：实现负载均衡

### 2.2 中间件锁机制

项目引入了中间件(middle_ware)来自动处理玩家数据锁，简化开发复杂度。

**中间件核心功能**：
1. **自动检测Player参数**：通过反射检查函数参数是否包含Player类型
2. **自动加锁**：如果包含Player类型，自动获取玩家锁
3. **自动释放锁**：函数执行完毕后自动释放锁
4. **错误处理**：玩家不存在时返回特定错误码，触发重新路由

**锁管理实现**：
```go
// LockByUid 使用uid锁住玩家
func (p *PlayerManager) LockByUid(uid string) *deadlock.Mutex {
    plr, exists := p.TryGetPlayerByUid(uid)
    if exists {
        lock, exists := p.lockMap.Get(plr.GetId())
        if exists {
            lock.Lock()
            return lock
        }
    }
    return p.SetLockByUid(uid)
}
```

**优势**：
1. **简化开发**：开发者不需要手动管理锁，可以专注于业务逻辑
2. **避免死锁**：统一的锁管理机制减少了死锁风险
3. **提高效率**：只有在操作其他玩家数据时才需要额外加锁

## 3. Manager模式设计

为避免循环引用问题，项目采用Manager模式将逻辑从数据结构中分离出来。

### 3.1 单例模式实现

每个Manager都使用单例模式实现，确保全局只有一个实例：

```go
var playerManagerLock deadlock.Once
var playerManager *PlayerManager

// Player 全局获取玩家管理中心
func Player() *PlayerManager {
    playerManagerLock.Do(func() {
        playerManager = &PlayerManager{
            playerMap: cmap.New[*gameStruct.Player](),
            idMap:     cmap.NewWithCustomShardingFunction[int, string](func(key int) uint32 { return uint32(key) % 100 }),
            lockMap:   cmap.New[*deadlock.Mutex](),
        }
        jobrunner.Schedule("@every 1m", playerManager)
    })
    return playerManager
}
```

### 3.2 避免循环引用

通过将逻辑从数据结构中分离，避免了包之间的循环依赖：

```go
// 数据结构定义
type Player struct {
    id        string
    gameId    int
    name      string
    // ...其他数据字段
}

// 逻辑处理放在Manager中
type PlayerManager struct {
    playerMap cmap.ConcurrentMap[string, *gameStruct.Player]
    idMap     cmap.ConcurrentMap[int, string]
    lockMap   cmap.ConcurrentMap[string, *deadlock.Mutex]
}

// 逻辑方法实现
func (p *PlayerManager) UserKickOffline(plr *gameStruct.Player, save bool) {
    // 实现踢下线逻辑
}
```

**优势**：
1. **避免循环引用**：将逻辑从数据结构中分离，避免了包之间的循环依赖
2. **职责分离**：数据结构只负责存储数据，逻辑处理放在Manager中
3. **全局访问**：通过单例模式可以在任何地方访问Manager
4. **可测试性**：逻辑与数据分离，更容易进行单元测试

## 4. 数据存储与缓存

### 4.1 MongoDB数据存储

项目使用MongoDB作为主数据库，存储玩家数据、游戏配置等持久化信息。

**连接配置**：
- 本地阈值：3秒（只使用响应时间小于3秒的连接）
- 最大空闲时间：5秒
- 最大连接池大小：1024

### 4.2 Redis缓存与分布式锁

使用Redis实现缓存和分布式锁，保证数据一致性。

**Redis分布式锁特性**：
- 使用SET NX EX命令原子性设置锁
- 使用Lua脚本确保只释放自己持有的锁
- 支持锁的刷新和超时机制
- 指数退避重试策略

## 5. 总结

项目采用了一种高度模块化、可扩展的分布式架构设计，通过NATS+Consul实现高可用性，通过中间件自动处理锁机制简化开发，通过Manager模式避免循环引用问题。这种架构设计既保证了系统的可扩展性和高可用性，又简化了开发复杂度，让开发者可以专注于游戏逻辑的实现。

虽然分布式架构增加了节点间通信的复杂度，但通过合理的设计和抽象，将这种复杂性封装起来，为开发者提供了简洁的接口。同时，通过优化资源利用率，降低了服务器成本，实现了高性能、低成本的游戏服务器架构。
