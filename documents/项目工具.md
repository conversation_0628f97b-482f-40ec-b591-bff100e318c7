# 项目工具文档

## 概述

**wld-proto** 是项目的核心协议转换生成工具，负责将XML配置文件转换为标准的protobuf文件，并自动生成服务器路由处理代码。

## 工具功能

### 主要作用
1. **XML转Proto**：将自定义XML配置转换成标准的`.proto`文件
2. **代码生成**：使用protoc生成服务器Go代码和客户端TypeScript代码
3. **路由生成**：根据消息配置自动生成服务器路由注册代码和处理器模板

### 工作流程
```
XML配置文件 → wld-proto工具 → .proto文件 → protoc → pb.go文件
                    ↓
            自动生成路由注册代码和处理器模板
```

## 使用方法

### 命令格式
```bash
wld-proto -in ./ -go-out ../../server/common -ts-out /Users/<USER>/Documents/UGit/worldh5/ -go-logic /Users/<USER>/Documents/UGit/WorldServer/server/
```

### 参数说明
- `-in`：XML消息文件输入路径（相对路径）
- `-go-out`：服务器消息包输出路径（protoc生成的pb.go文件）
- `-ts-out`：客户端TypeScript代码输出路径（绝对路径）
- `-go-logic`：服务器逻辑代码输出路径（路由处理器代码）

### 执行位置
必须在 `base/proto` 路径下执行命令

### 依赖环境
工具会自动下载所需依赖，无需手动安装。部分电脑可能需要提权运行。

## XML配置规范

### 文件组织结构
```
base/proto/
├── pbBase/          # 基础数据结构和枚举
│   ├── struct.xml   # 通用结构体定义
│   ├── Sex/         # 性别枚举
│   ├── Response/    # 响应码枚举
│   └── ...
├── pbGame/          # 游戏相关消息
│   ├── bag.xml      # 背包相关
│   ├── battle.xml   # 战斗相关
│   └── ...
├── pbLogin/         # 登录相关消息
└── pbCross/         # 跨服务器通信消息
```

### 三种定义类型

#### 1. enum（枚举）
用于定义一组命名常量值
```xml
<enum name="Type" explain="性别类型" allowAlias="false">
    <type name="Male" number="0" explain="男"/>
    <type name="Female" number="1" explain="女"/>
</enum>
```

#### 2. struct（结构体）
用于定义可复用的数据结构
```xml
<struct name="LoginResult" explain="登录结果信息">
    <field class="string" name="id" explain="用户id"/>
    <field class="string" name="token" explain="会话凭证"/>
</struct>
```

#### 3. message（消息）
用于定义通信消息
```xml
<message type="C2S" name="EquipWear" module="game" explain="穿戴装备">
    <field class="int32" name="slotPos" explain="物品位置"/>
    <field class="int32" name="itemId" explain="物品id"/>
</message>
```

### 消息类型说明

#### C2S（Client to Server）
- 客户端发送给服务器的请求消息
- **必须**指定`module`属性，用于确定路由到哪个服务器模块
- 会自动生成路由注册代码和处理器模板

#### S2C（Server to Client）
- 服务器发送给客户端的响应消息
- **不需要**指定`module`属性
- 不会生成路由注册代码

#### S2R（Server to Server）
- 服务器节点之间的内部通信消息
- **必须**指定`module`属性
- 会自动生成路由注册代码和处理器模板

### 字段类型支持
- **基础类型**：`int32`、`string`、`bool`、`int64`等
- **数组类型**：`<array class="int32" name="list"/>`
- **映射类型**：`<map><key class="int32"/><value class="string"/></map>`
- **自定义类型**：引用其他struct或enum，如`pbBase/struct.ItemData`

## 代码生成规则

### 消息命名规则
- XML中定义：`<message name="EquipWear"/>`
- 生成的消息名：`C2S_EquipWearMessage`、`S2C_EquipWearMessage`
- 固定添加类型前缀和"Message"后缀

### 包路径映射
- XML配置：`package="world/common/pbGame"`
- 实际路径：`server/common/pbGame`
- `world`是项目根路径的代指

### 路由注册代码生成
工具会在对应模块的`rpc.go`文件中自动生成路由注册代码：
```go
// 穿戴装备
this.middleware.Wrap("C2S_EquipWearMessage", bag.C2sEquipWearMessageHandler)
```

### 处理器代码生成
在`server/{module}/handler/{package}/`目录下生成处理器文件：

**Game模块C2S消息处理器示例：**
```go
// C2sEquipWearMessageHandler 穿戴装备
func C2sEquipWearMessageHandler(this module.RPCModule) any {
    /* action-code-start */
    return func(player *gameStruct.Player, msg *pbGame.C2S_EquipWearMessage) protoreflect.ProtoMessage {
        // 在这里实现具体的业务逻辑
        return &pbGame.S2C_EquipWearMessage{Code: Response.NoError}
    }
    /* action-code-end */
}
```

**Login模块C2S消息处理器示例：**
```go
func C2sLoginMessageHandler(this module.RPCModule) any {
    return func(session gate.Session, msg *pbLogin.C2S_LoginMessage) protoreflect.ProtoMessage {
        // 处理登录逻辑
        return &pbLogin.S2C_LoginMessage{Code: Response.NoError}
    }
}
```

**跨服S2R消息处理器示例：**
```go
func S2rNotifyNewMailMessageHandler(this module.RPCModule) any {
    return func(msg *pbCross.S2R_NotifyNewMailMessage) protoreflect.ProtoMessage {
        // 处理跨服消息
        return &pbCross.R2S_SimpleResponseMessage{}
    }
}
```

## 开发新功能流程

### 1. 分析需求
- 确定功能属于哪个模块（game、login、cross等）
- 分析需要哪些消息类型（C2S请求、S2C响应）
- 确定数据结构需求

### 2. 定义数据结构
如果需要新的数据结构，在`pbBase/struct.xml`中添加：
```xml
<struct name="NewDataStruct" explain="新数据结构">
    <field class="int32" name="id" explain="ID"/>
    <field class="string" name="name" explain="名称"/>
</struct>
```

### 3. 定义枚举类型
如果需要新的枚举，在对应目录下创建枚举文件：
```xml
<enum name="NewType" explain="新类型" allowAlias="false">
    <type name="TypeA" number="0" explain="类型A"/>
    <type name="TypeB" number="1" explain="类型B"/>
</enum>
```

### 4. 定义消息
在对应模块的XML文件中添加消息定义：
```xml
<!-- 客户端请求 -->
<message type="C2S" name="NewFeature" module="game" explain="新功能请求">
    <field class="int32" name="param1" explain="参数1"/>
    <field class="string" name="param2" explain="参数2"/>
</message>

<!-- 服务器响应 -->
<message type="S2C" name="NewFeature" explain="新功能响应">
    <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
    <field class="pbBase/struct.NewDataStruct" name="data" explain="返回数据"/>
</message>
```

### 5. 生成代码
在`base/proto`目录下执行wld-proto命令：
```bash
wld-proto -in ./ -go-out ../../server/common -ts-out /path/to/client -go-logic /path/to/server
```

### 6. 实现业务逻辑
在生成的处理器文件中实现具体逻辑：
```go
return func(player *gameStruct.Player, msg *pbGame.C2S_NewFeatureMessage) protoreflect.ProtoMessage {
    // 实现新功能的业务逻辑
    param1 := msg.GetParam1()
    param2 := msg.GetParam2()
    
    // 处理逻辑...
    
    return &pbGame.S2C_NewFeatureMessage{
        Code: Response.NoError,
        Data: &pbBase.NewDataStruct{
            Id:   1,
            Name: "result",
        },
    }
}
```

## 注意事项

1. **代码生成区域**：处理器文件中的`/* action-code-start */`和`/* action-code-end */`之间的代码会在重新生成时保留
2. **路由自动注册**：只有C2S和S2R类型的消息会生成路由注册代码
3. **模块属性必填**：C2S和S2R消息必须指定`module`属性
4. **文件覆盖**：每次运行工具都会重新生成.proto文件和路由注册代码
5. **包路径一致性**：确保XML中的package路径与实际Go包结构一致

## 常见问题

### Q: 如何引用其他包的类型？
A: 使用完整路径，如`pbBase/struct.ItemData`或`pbBase/Response/Response.Code`

### Q: 如何定义数组和映射？
A: 
- 数组：`<array class="int32" name="list" explain="列表"/>`
- 映射：`<map name="data"><key class="int32"/><value class="string"/></map>`

### Q: 消息处理器的参数规则？
A: 参数规则根据模块和消息类型确定：
- **Login模块 C2S消息**：第一个参数是`session`会话，第二个参数是消息内容
- **Game模块 C2S消息**：第一个参数是`player`玩家，第二个参数是消息内容
- **跨服S2R消息**：只有一个参数，即消息内容

### Q: 如何处理跨服务器通信？
A: 使用S2R类型消息，指定对应的module，工具会自动生成跨服务器路由代码
